<template>
    <div>
        <div class="nav-all-wrapper">
            <!-- 控制面板 -->
            <div class="input-wrapper">
                <div v-if="active==5">
                    <el-date-picker
                        class="m-2"
                        @change="changeDate"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        v-model="value2"
                        type="date"
                        :clearable="false"
                    />
                    <el-select
                        v-model="carPlate"
                        class="m-2"
                        placeholder="请选择车牌"
                        clearable
                        @change="changeCarPlate"
                        filterable
                    >
                        <el-option
                            v-for="item in carPlateOptions"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    <div class="reset-btn" @click="reset">
                        <img
                            src="../../../assets/images/closed/reset.svg"
                            style="width: 20px; height: 20px"
                            alt="重置"
                        />
                    </div>
                </div>
            </div>

            <!-- 导航按钮 -->
            <div v-if="showFlag != 1" class="nav-all-btns">
                <div
                    :class="active === 4 ? 'active-btn' : 'btn'"
                    @click="setPage(4)"
                >
                    <img
                        class="icon"
                        src="@/assets/newImages/Nav/ClosedManagement/monitor.svg"
                    />
                    <div class="text">实时监控</div>
                </div>
                <div
                    :class="active === 5 ? 'active-btn' : 'btn'"
                    @click="setPage(5)"
                >
                    <img
                        class="icon"
                        src="@/assets/newImages/Nav/ClosedManagement/history.svg"
                    />
                    <div class="text">历史轨迹</div>
                </div>
                <div
                    :class="active === 1 ? 'active-btn' : 'btn'"
                    @click="setPage(1)"
                >
                    <img
                        class="icon"
                        src="@/assets/newImages/Nav/ClosedManagement/bayonet.svg"
                    />
                    <div class="text">卡口设备</div>
                </div>
                <div
                    :class="active === 2 ? 'active-btn' : 'btn'"
                    @click="setPage(2)"
                >
                    <img class="icon" src="@/assets/newImages/Nav/ClosedManagement/video.svg" />
                    <div class="text">视频监控</div>
                </div>
                <div
                    :class="active === 3 ? 'active-btn' : 'btn'"
                    @click="setPage(3)"
                >
                    <img
                        class="icon"
                        src="@/assets/newImages/Nav/ClosedManagement/comprehensive_monitor.svg"
                    />
                    <div class="text">全方位监控</div>
                </div>
            </div>
        </div>
        <bayonet-dialog
            v-if="bayonetDialogShow"
            :pickId="bayonetId"
            :positionData="positionData"
            :equipmentId="equipmentId"
            @closeDialog="closeDialog"
        ></bayonet-dialog>
        <infrastructure-dialog
            :pickId="videoId"
            v-if="videoDialogShow"
            @closeDialog="closeDialog"
        ></infrastructure-dialog>
        <car-detail-dialog
            :pickId="carId"
            v-if="carDialogShow"
            @closeDialog="closeDialog"
        ></car-detail-dialog>
        <car-data-detail-dialog
            :pickId="carData"
            v-if="carDataDialogShow"
            @closeDialog="closeDialog"
        ></car-data-detail-dialog>
        <departure-dialog
            :departureData="departureData"
            v-if="departureShow"
            @closeDialog="closeDialog"
        >
        </departure-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    onUnmounted,
    getCurrentInstance,
    watch,
    nextTick,
} from "vue";

// API导入
import {
    nj_equipments,
    nj_equipment_park,
} from "../../../assets/js/api/parkArchives";
import { nj_equipment_entrance_guard } from "../../../assets/js/api/closedManagement";
import {
    alarmInfoPersonRecord,
    cameraPersonLink,
    carGpsPoint,
    getDictByType,
    gpsCarPlate,
    gpsLineByTimeCarPlate,
    nj_iocGateC,
    se_gpsCarPlate,
    se_gpsLineByTimeCarPlate,
    detail,
} from "../../../assets/js/api/comprehensiveSupervision";

// 组件导入
// 卡口弹窗
import bayonetDialog from "./subgroup/bayonetDialog.vue";
import videoDialog from "./subgroup/videoDialog.vue";
// 视频弹窗
import infrastructureDialog from "./subgroup/infrastructureDialog.vue";
//车辆弹窗
import CarDetailDialog from "./subgroup/carDetailDialog.vue";
//
import CarDataDetailDialog from "./subgroup/carDataDetailDialog.vue";
//人员详情弹窗
import DepartureDialog from "./subgroup/departureDialog.vue";

// 图片资源导入
import url from "../../../assets/images/closed/car_success.svg";
import aliveImg from "../../../assets/images/card/icon1.png";
import bayonetImg from "/static/newPoi/ClosedManagement/bayonet.png";
import videoImg from "/static/newPoi/ClosedManagement/video.png";
import url_success from "../../../assets/images/closed/car_success.svg";
import url_error from "../../../assets/images/closed/car_error.svg";
import * as Cesium from "cesium";

// 组件实例
const { proxy } = getCurrentInstance();

// 基础状态
const showFlag = ref(1);
const active = ref(0);

// 时间和车牌相关
const value1 = ref("");
const value2 = ref("");
const carPlate = ref(null);
const begin = ref(null);
const end = ref(null);
const carPlateOptions = ref([]);

// 弹窗控制
const bayonetDialogShow = ref(false);
const videoDialogShow = ref(false);
//车辆弹窗
const carDialogShow = ref(false);
const carDataDialogShow = ref(false);
const departureShow = ref(false);

// ID相关
const videoId = ref(null);
const carId = ref(null);
const carData = ref(null);
const bayonetId = ref(null);
const equipmentId = ref(null);

// 数据相关
const positionData = ref([]);
const areaDataThree = ref([]);
const project = ref([]);
const actual = ref([]);
const pointList = ref([]);
const limitData = ref(null);
const trueData = ref(null);
const departureData = ref([]);

// 定时器
const timer = ref(null);

// 对话框数据
const dialogData = ref({
    carPlate: "",
    goodsName: "",
    goodsWeight: "",
    goodsType: "",
    driverName: "",
    driverPhone: "",
    bevisitCompany: "",
    bevisitPerson: "",
    bevisitNumber: "",
    planTime: "",
});
// watch(active, (a, b) => {
//     console.log(a, b);
//     if (active.value == 0) {
//         quitOnePost();
//         initCarMap();
//     } else {
//         console.log(222);
//         window.clearInterval(timer.value);
//         timer.value = null;
//     }
// });

// 清除地图实体
const clearMapEntities = () => {
    console.log(timer.value,'清除前--------------');
    
    window.viewer.entities.removeAll();
    window.map1.clearMap();
    window.clearInterval(timer.value);
    timer.value = null;
    console.log(timer.value,'清除后--------------');

};

// 切换车牌选择
const changeCarPlate = (e) => {
    if (e) {
        se_gpsLineByTimeCarPlate({
            beginTime: begin.value,
            endTime: end.value,
            carPlate: e,
        }).then((res) => {
            window.map1.clearMap();
            initBoundary();
            window.clearInterval(timer.value);
            timer.value = null;
            let lat = [];
            let path = [];
            console.log(res.data.data.carGpsRecordList);
            if (res.data.data.carGpsRecordList != []) {
                res.data.data.carGpsRecordList.forEach((long) => {
                    lat.push([long.lng, long.lat]);
                });
                path.push(lat);
            }

            if (
                res.data.data.drivePlan != null &&
                res.data.data.drivePlan.route != null
            )
                project.value = eval(res.data.data.drivePlan.route);
            else project.value = [];
            limitData.value = res.data.data.drivePlan;
            pointList.value = res.data.data.carGpsRecordList;
            trueData.value = res.data.data;
            actual.value = path;
            if (res.data.data.heavyTruckEdit != null)
                dialogData.value = res.data.data.heavyTruckEdit;
            initPathSimplifier();
        });
    }
};
const dealWith = (e) => {
    return Number(e.toString().substring(0, 5));
};
// 初始化轨迹
const initPathSimplifier = () => {
    let path = actual.value[0];
    let marker = new AMap.Marker({
        icon: url,
        position: [path[0][0], path[0][1]],
        offset: new AMap.Pixel(-23, -60),
        exData: {
            data: dialogData.value,
            trueData: trueData.value,
        },
    });
    marker.on("click", (e) => {
        let car = e.target._opts.exData.trueData.heavyTruck;
        // console.log(e.target._opts.exData.trueData.heavyTruck)
        carId.value = car.carPlate;
        carDialogShow.value = true;
        proxy.$loading.show();
        // window.map1.setZoomAndCenter(22, e.target._opts.position)
        console.log(carId.value);
    });
    marker.setMap(window.map1);
    let lineArr = []; // 存储轨迹的一维数组
    let mapList = new Map();
    let arr = [];
    let arr1 = [];
    let arr2 = [];
    console.log(pointList);
    pointList.value.forEach((pos, index) => {
        let marker1 = new AMap.CircleMarker({
            center: [pos.lng, pos.lat], //圆心
            radius: 10.1, //半径
            strokeColor: "white", //轮廓线颜色
            strokeWeight: 0.1, //轮廓线宽度
            strokeOpacity: 0, //轮廓线透明度
            fillColor: "rgba(255,255,255,0)", //多边形填充颜色
            fillOpacity: 0, //多边形填充透明度
            zIndex: 199, //多边形覆盖物的叠加顺序
            cursor: "pointer", //鼠标悬停时的鼠标样式
            exData: {
                data: trueData.value,
            },
        });
        marker1.on("mouseover", (e) => {
            let item = e.target._opts.exData.data;
            console.log(item);
            let speedLimit;
            if (item.drivePlan == null) speedLimit = 999;
            else speedLimit = item.drivePlan.speedLimit;
            if (pos.speed > speedLimit) {
                window.infoWindowone = new AMap.InfoWindow({
                    position: [pos.lng, pos.lat],
                    offset: new AMap.Pixel(0, 0),
                    content: `<div style="background: #fff;padding:10px">
                超速：${pos.speed}km/h,参考值${speedLimit}km/h
            </div>`,
                });
                window.infoWindowone.open(window.map1);
            }
        });
        window.map1.add(marker1);
        let limit_1;
        if (limitData.value == null) {
            limit_1 = 999;
        } else {
            limit_1 = limitData.value.speedLimit;
        }
        console.log(dealWith(pos.lng));
        console.log(
            project.value.map((e) => dealWith(e[0])),
            pos,
        );
        if (
            project.value.length != 0 &&
            (!project.value
                .map((e) => dealWith(e[0]))
                .includes(dealWith(pos.lng)) ||
                !project.value
                    .map((e) => dealWith(e[1]))
                    .includes(dealWith(pos.lat)))
        ) {
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
            if (arr.length != 0) {
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            arr1.push([pos.lng, pos.lat]);
        } else if (pos.speed > limit_1) {
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
            if (arr1.length != 0) {
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            arr.push([pos.lng, pos.lat]);
        } else {
            if (arr.length != 0) {
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            if (arr1.length != 0) {
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            arr2.push([pos.lng, pos.lat]);
        }
        if (index == pointList.value.length - 1) {
            if (arr.length != 0) {
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            if (arr1.length != 0) {
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
        }
    });
    let objKeyArr = Array.from(mapList.keys());
    for (let i = 0; i < objKeyArr.length; i++) {
        if (i < objKeyArr.length - 1) {
            mapList.get(objKeyArr[i]).push(mapList.get(objKeyArr[i + 1])[0]);
        }
    }
    console.log(mapList);
    mapList.forEach((key, i) => {
        let color = ""; // 定义轨迹的颜色变量
        let type = i.split("_"); // 获取轨迹的类型
        if (type[1] == "speed") {
            // 根据轨迹的类型进行颜色的赋值
            color = "#ff8119";
        } else if (type[1] == "boundary") {
            color = "#ff0000";
        } else if (type[1] == "success") {
            color = "#AF5";
        }
        // 配置轨迹
        name = new AMap.Polyline({
            map: window.map1,
            path: key, // 轨迹的坐标数组
            showDir: true,
            strokeColor: color,
            strokeWeight: 6, //线宽
            lineJoin: "round",
        });
        window.map1.add([name]);
        lineArr = lineArr.concat(key);
    });
    // })
    var passedPolyline = new AMap.Polyline({
        map: window.map1,
        strokeColor: "#AF5", //线颜色
        strokeWeight: 6, //线宽
    });
    console.log(lineArr);
    marker.moveAlong(lineArr, {
        duration: 500, //可根据实际采集时间间隔设置
        autoRotation: false,
    });
    // marker.on("moving", function (e) {
    //   passedPolyline.setPath(e.passedPath)
    //   map.setCenter(e.target.getPosition())
    // })
    // })
    // window.map1.setFitView()
};
const reset = () => {
    value1.value = null;
    value2.value = null;
    carPlate.value = null;
    begin.value = null;
    end.value = null;
    window.map1.clearMap();
    window.clearInterval(timer.value);
    timer.value = null;
    setPage('')
    // initBoundary();
    // initCarMap();
    // quitOnePost();
};
const changeDate = (e, c) => {
    console.log(e, "进入changeData");
    if (e) {
        se_gpsCarPlate({
            beginTime: `${value2.value} 00:00:00`,
            endTime: `${value2.value} 24:00:00`,
        }).then((res) => {
            begin.value = `${value2.value} 00:00:00`;
            end.value = `${value2.value} 24:00:00`;
            carPlateOptions.value = res.data.data;
        });
    } else {
        begin.value = null;
        end.value = null;
    }
};
const changeTime = (e, c) => {
    if (e) {
        se_gpsCarPlate({
            beginTime: `${value2.value} ${e[0]}`,
            endTime: `${value2.value} ${e[1]}`,
        }).then((res) => {
            begin.value = `${value2.value} ${e[0]}`;
            end.value = `${value2.value} ${e[1]}`;
            carPlateOptions.value = res.data.data;
        });
    } else {
        begin.value = null;
        end.value = null;
    }
};
// 切换导航按钮
const setPage = (val) => {
    if (val === active.value) {
        // 再次点击同一按钮，取消选择
        active.value = 0;
        clearMapEntities();
        initBoundary();
        
    } else {
    
        clearMapEntities();
        active.value = val;
        initBoundary();

        // 根据选择获取不同状态的数据
        getData(val);
    }
};

// 统一的数据获取方法
const getData = async (val) => {
    positionData.value = [];

    if (val === 1) {
        // 卡口数据
        await getBayonetData();
    } else if (val === 2) {
        // 视频数据
        await getVideoData();
    } else if (val === 3) {
        // 全方位监控
        console.log("启用全方位监控");
        proxy.$bus.emit("uesMonitor", true);
    }else if (val == 4){
        initCarMap();
        quitOnePost();
    }
};

// 获取卡口数据
const getBayonetData = async () => {
    try {
        const res = await nj_iocGateC({ "": "" });
        if (res.data.success && res.data.data?.length) {
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.equipment_id,
                        id: item.id,
                        cameraId: item.cameraId,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            renderMapPoints(positionData.value, bayonetImg, 'bayonet');
        }
    } catch (error) {
        console.error("获取卡口数据失败:", error);
    }
};

// 获取视频数据
const getVideoData = async () => {
    try {
        const res = await nj_equipment_park();
        if (res.data.success && res.data.data?.length) {
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            renderMapPoints(positionData.value, videoImg, 'video');
        }
    } catch (error) {
        console.error("获取视频数据失败:", error);
    }
};

// 统一的地图渲染方法
const renderMapPoints = (data, img, type) => {
    if (window.toggle == 2) {
        // Cesium 3D地图渲染
        putIcons(data, img);
        setupCesiumClickHandler(type);
    } else if (window.toggle == 3) {
        // 高德2D地图渲染
        render2DMapPoints(data, img, type);
    }
};

// 设置Cesium点击事件处理
const setupCesiumClickHandler = (type) => {
    window.viewer.screenSpaceEventHandler.setInputAction(
        function (e) {
            var pick = viewer.scene.pick(e.position);
            if (pick && pick.id) {
                clickOpenDialog(pick.id, type);
                window.viewer.camera.setView({
                    destination: Cesium.Cartesian3.fromDegrees(
                        pick.id._longitude,
                        pick.id._latitude,
                        800,
                    ),
                });
            }
        },
        Cesium.ScreenSpaceEventType.LEFT_CLICK,
    );
};

// 渲染2D地图点位
const render2DMapPoints = (data, imgPath, type) => {
    data.forEach((item) => {
        let marker = new AMap.Marker({
            icon: new AMap.Icon({
                size: new AMap.Size(48, 62),
                image: imgPath,
                imageSize: new AMap.Size(48, 62),
            }),
            position: [item.longitude, item.latitude],
            offset: new AMap.Pixel(-24, -62),
        });

        marker.on("click", (e) => {
            clickOpenDialog(item, type);
            window.map1.setZoomAndCenter(22, e.target._opts.position);
        });

        marker.setMap(window.map1);
    });
    window.map1.setFitView();
};

// 统一的弹窗打开处理
const clickOpenDialog = (data, type) => {
    proxy.$loading.show();

    if (type === 'bayonet') {
        equipmentId.value = data.name || data.equipment_id;
        bayonetId.value = data.id || data.name;
        bayonetDialogShow.value = true;
    } else if (type === 'video') {
        videoId.value = data.id || data.name;
        videoDialogShow.value = true;
    }
};

// 这些变量已经在上面声明了，删除重复声明

const putIcons = (_datas, img, _parent) => {
    console.log("添加视频icon");
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        console.log(data);
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        const entity = window.viewer.entities.add({
            name: data.id,
            // 参数顺序：经度、纬度
            id: data.name,
            longitude: Number(data.longitude) - 0.0062,
            latitude: Number(data.latitude) - 0.00085,
            position: Cesium.Cartesian3.fromDegrees(
                Number(data.longitude) - 0.0062,
                Number(data.latitude) - 0.00085,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 48,
                height: 62,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                pixelOffset: new Cesium.Cartesian2(0, -31),
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            type: "text", // 自定义属性
        });
        // window.viewer.zoomTo(entity);
        // 调整视野
    adjustField();
        console.log(999999999999999999999999999999999);
    }
};
// 调整Cesium视野范围
const adjustField = () => {
  if (positionData.value.length === 0) return;
  
  const arrs = []; // 经纬度数组
  const lonArr = []; // 经度数组
  const latArr = []; // 纬度数组

  // 收集所有点位的经纬度
  positionData.value.forEach(item => {
    arrs.push(item.longitude);
    lonArr.push(item.longitude);
    arrs.push(item.latitude);
    latArr.push(item.latitude);
  });

  // 处理区域数据
  if (areaDataThree.value.length > 0) {
    areaDataThree.value.forEach(item => {
      for (let i = 0; i < item.path.length; i++) {
        if (i % 3 === 0) {
          arrs.push(item.path[i]);
          lonArr.push(item.path[i]);
        } else if (i % 3 === 1) {
          arrs.push(item.path[i]);
          latArr.push(item.path[i]);
        }
      }
    });
  }

  // 计算中心点和视野范围
  const adr = Cesium.Cartesian3.fromDegreesArray(arrs);
  const polys = Cesium.BoundingSphere.fromPoints(adr).center;
  const surfacePoint = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polys);
  const ellipsoid = window.viewer.scene.globe.ellipsoid;
  const cartesian = new Cesium.Cartesian3(surfacePoint.x, surfacePoint.y, surfacePoint.z);
  const cartographic = ellipsoid.cartesianToCartographic(cartesian);

  // 计算中心点经纬度
  const centerPoint = {
    lat: Cesium.Math.toDegrees(cartographic.latitude),
    lng: Cesium.Math.toDegrees(cartographic.longitude),
    alt: cartographic.height
  };

  // 计算视野范围
  const maxLon = Math.max(...lonArr);
  const minLon = Math.min(...lonArr);
  const maxLat = Math.max(...latArr);
  const minLat = Math.min(...latArr);
  const latDiff = maxLat - minLat;
  const lonDiff = maxLon - minLon;
  let radius = Math.max(latDiff, lonDiff);
  
  // 根据范围大小调整高度
  let height;
  if (radius === 0) {
    radius = 0.002;
    height = radius * 1150000;
  } else if (radius > 1) {
    height = radius * 200000;
  } else if (radius < 0.004) {
    height = radius * 1150000;
  } else if (radius < 0.002) {
    height = radius * 9550000;
  } else {
    height = radius * 550000;
  }

  // 设置相机视角
  nextTick(() => {
    window.viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(centerPoint.lng, centerPoint.lat, height),
    });
  });
};
const closeDialog = (value) => {
    console.log(value);
    if (value == "bayonet") {
        bayonetDialogShow.value = false;
    } else if (value == "video") {
        videoDialogShow.value = false;
    } else if (value == "car") {
        carDialogShow.value = false;
    } else if (value == "depart") {
        departureShow.value = false;
    } else if (value == "carData") {
        carDataDialogShow.value = false;
    }
    proxy.$loading.hide();
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
const isTrue = (data) => {
    if (data.drivePlan.route != null) {
        if (
            data.speed > data.drivePlan.speedLimit ||
            !eval(data.drivePlan.route)
                .map((e) => dealWith(e[0]))
                .includes(dealWith(data.lng)) ||
            !eval(data.drivePlan.route)
                .map((e) => dealWith(e[1]))
                .includes(dealWith(data.lat))
        ) {
            return false;
        } else return true;
    } else {
        return true;
    }
};
proxy.$bus.on("cameraPersonLink_listIoc", (val) => {
    quitOnePost();
    //   console.log('test ', val);
});

const quitOnePost = () => {
    cameraPersonLink().then((res) => {
        console.log(res.data.data, "离岗人员数据");
        res.data.data.forEach((car) => {
            if (car != null) {
                console.log(car);
                let marker = new AMap.Marker({
                    icon: new AMap.Icon({
                        size: new AMap.Size(42, 62), // 图标尺寸
                        image:
                            car.alarmed == 0
                                ? "/static/poi/people_success.svg"
                                : "/static/poi/people_error.svg", //绝对路径
                        imageSize: new AMap.Size(42, 62),
                    }),
                    position: [car.lng, car.lat],
                    offset: new AMap.Pixel(-21, -62),
                });
                console.log(marker, "我是人员数据");
                marker.on("click", (e) => {
                    console.log(e);
                    console.log(car);
                    departureData.value = car;
                    departureShow.value = true;
                    proxy.$loading.show();
                    // window.map1.setZoomAndCenter(22, e.target._opts.position)
                    // console.log(carId.value);
                });
                marker.setMap(window.map1);
                window.map1.setFitView();
            }
        });
    });
    alarmInfoPersonRecord({
        pageNum: 1,
        pageSize: 999,
        alarmType: "illegalPark",
    }).then((res) => {
        alarmInfoPersonRecord({
            pageNum: 1,
            pageSize: 999,
            alarmType: "crossLine",
        }).then((res1) => {
            let data = res.data.data.records
                .map((a) => {
                    return {
                        ...a,
                        type: 1,
                    };
                })
                .concat(
                    res1.data.data.records.map((a) => {
                        return {
                            ...a,
                            type: 2,
                        };
                    }),
                );
            data.forEach((car) => {
                if (car != null) {
                    let marker = new AMap.Marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(42, 62), // 图标尺寸
                            image: "/static/poi/car_error.svg", //绝对路径
                            imageSize: new AMap.Size(42, 62),
                        }),
                        position: [Number(car.longitude), Number(car.latitude)],
                        offset: new AMap.Pixel(-21, -62),
                    });
                    marker.on("click", (e) => {
                        carData.value = car;
                        carDataDialogShow.value = true;
                        proxy.$loading.show();
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                    });
                    const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                    const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;
                    marker.on("mouseover", (e) => {
                        window.infoWindowone = new AMap.InfoWindow({
                            isCustom: true,
                            position: [
                                Number(car.longitude),
                                Number(car.latitude),
                            ],
                            offset: new AMap.Pixel(0, -60),
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${car.carPlate}</div>
                       </div>`,
                        });
                        window.infoWindowone.open(window.map1);
                    });
                    marker.on("mouseout", (e) => {
                        window.infoWindowone.close();
                    });
                    marker.setMap(window.map1);
                    // window.map1.setFitView();
                }
            });
        });
    });
};

const initCarMap = () => {
    if (window.map1 != null && window.toggle == 3) {
        carGpsPoint().then((res) => {
            res.data.data.forEach((car) => {
                if (car != null) {
                    let marker = new AMap.Marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(42, 62), // 图标尺寸
                            image: isTrue(car)
                                ? "/static/poi/car_success.svg"
                                : "/static/poi/car_error.svg", //绝对路径
                            imageSize: new AMap.Size(42, 62),
                        }),
                        position: [car.lng, car.lat],
                        offset: new AMap.Pixel(-21, -62),
                    });
                    marker.on("click", (e) => {
                        carId.value = car.carPlate;
                        carDialogShow.value = true;
                        proxy.$loading.show();
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                        console.log(carId.value);
                    });
                    const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                    const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;
                    marker.on("mouseover", (e) => {
                        window.infoWindowone = new AMap.InfoWindow({
                            isCustom: true,
                            position: [car.lng, car.lat],
                            offset: new AMap.Pixel(0, -60),
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${car.carPlate}</div>
                       </div>`,
                        });
                        window.infoWindowone.open(window.map1);
                    });
                    marker.on("mouseout", (e) => {
                        window.infoWindowone.close();
                    });
                    marker.setMap(window.map1);
                    // window.map1.setFitView();
                }
            });
        });

        window.clearInterval(timer.value);
        timer.value = window.setInterval(() => {
            quitOnePost();
            window.map1.clearMap();
            initBoundary();
            carGpsPoint().then((res) => {
                res.data.data.forEach((car) => {
                    if (car != null) {
                        let marker = new AMap.Marker({
                            icon: new AMap.Icon({
                                size: new AMap.Size(42, 62), // 图标尺寸
                                image: isTrue(car)
                                    ? "/static/poi/car_success.svg"
                                    : "/static/poi/car_error.svg", //绝对路径
                                imageSize: new AMap.Size(42, 62),
                            }),
                            position: [car.lng, car.lat],
                            offset: new AMap.Pixel(-21, -62),
                        });
                        marker.on("click", (e) => {
                            carId.value = car.carPlate;
                            carDialogShow.value = true;
                            proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                            console.log(carId.value);
                        });
                        const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                        const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;
                        marker.on("mouseover", (e) => {
                            window.infoWindowone = new AMap.InfoWindow({
                                isCustom: true,
                                position: [car.lng, car.lat],
                                offset: new AMap.Pixel(0, -60),
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${car.carPlate}</div>
                       </div>`,
                            });
                            window.infoWindowone.open(window.map1);
                        });
                        marker.on("mouseout", (e) => {
                            window.infoWindowone.close();
                        });
                        marker.setMap(window.map1);
                    }
                });
                //   window.map1.setFitView();
            });
        }, 60000);
    }
};
onMounted(() => {
    // 初始化基础状态
    showFlag.value = window.toggle;

    // 清理热力图
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }

    // 初始化边界
    initBoundary();

    // 监听地图切换事件
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
        clearMapEntities();
        initBoundary();
    });

    // 监听告警跳转事件
    proxy.$bus.on("clicJump1", (val) => {
        if (val.alarmObjectId != null && val.alarmObjectId != undefined) {
            console.log("告警跳转:", val);

            if (val.tag == "人员离岗告警") {
                // 人员离岗告警处理
                console.log("人员离岗告警", val.alarmObjectId);
                return;
            } else {
                // 车辆相关告警处理
                if (
                    val.acquisitionValue != undefined &&
                    val.acquisitionValue != null &&
                    val.alarmTime != undefined &&
                    val.alarmTime != null
                ) {
                    value2.value = val.alarmTime.split(" ")[0];
                    changeDate(value2.value);
                    carPlate.value = val.acquisitionValue;
                    changeCarPlate(carPlate.value);
                }
            }
        }
    });
});

onUnmounted(() => {
    // 清理定时器
    window.clearInterval(timer.value);
    timer.value = null;

    // 清理事件监听器
    proxy.$bus.off("change_toggle");
    proxy.$bus.off("clicJump1");
    proxy.$bus.off("cameraPersonLink_listIoc");
});
</script>

<style lang="less" scoped>
.nav-all-wrapper {
    height: 862px;
    position: absolute;
    z-index: 1000;
    top: 96px;
    left: 496px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: none;

    .input-wrapper {
        width: 300px;
        height: 40px;
        pointer-events: auto;
        .el-select {
                    width:180px;
                }
                .el-date-editor {
                    width: 210px;
                }
              :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }

        :deep(.el-input__wrapper) {
            background-color: transparent;
            box-shadow: 0 0 0 0;
            border-radius: 4px;
            border: 1px solid #30abe8;
            height: 22px;
            padding: 7px 12px;
            width: 180px;
            margin-bottom: 10px;
        }
        :deep(.el-input__wrapper .is_focus) {
            background-color: transparent;
            box-shadow: 0 0 0 0;
            border-radius: 4px;
            border: 1px solid #30abe8;
            height: 22px;
            padding: 7px 12px;
            width: 180px;
            margin-bottom: 10px;
        }
        :deep(.el-date-editor) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px; /* 157.143% */
            margin-bottom: 10px;
            width: 180px;
        }

        :deep(.el-input__inner) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px; /* 157.143% */
            width: 180px;
        }

        :deep(.el-range-input) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
        }

        :deep(.el-input__inner::placeholder) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px; /* 157.143% */
        }
    }

    .nav-all-btns {
        display: flex;
        flex-direction: column;
        gap: 24px;
        pointer-events: auto;

        .btn {
            width: 144px;
            height: 34px;
            background: url("@/assets/newImages/Nav/nav-btn-blue.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .active-btn {
            width: 144px;
            height: 34px;
            background: url("@/assets/newImages/Nav/nav-active-btn-yellow.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .icon {
            width: 24px;
            height: 24px;
            margin-left: 12px;
        }

        .text {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            vertical-align: middle;
            color: #FFFFFF;
            margin: auto 0;
            margin-left: 24px;
        }
    }
}
</style>
