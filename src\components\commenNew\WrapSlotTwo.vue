<template>
    <div class="wrap-slot-two">
        <div class="box-wrap">
            <div class="title">
                <p class="title-name">{{ title1 }}</p>
            </div>
            <slot name="content1"></slot>
        </div>
        <div class="box-wrap-big">
            <div class="title">
                <p class="title-name">{{ title2 }}</p>
            </div>
            <slot name="content2"></slot>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    title1: {
        type: String,
    },
    title2: {
        type: String,
    },
});
</script>

<style lang="less" scoped>
.wrap-slot-two {
    > div:not(:first-child) {
        margin-top: 24px;
    }
    .box-wrap {
        width: 400px;
        height: 304px;
        .title {
            width: 400px;
            height: 48px;
            background: url("@/assets/newImages/title-icon.svg") no-repeat;
            background-size: cover;
            background-position: center;
            margin-bottom: 12px;
        }
        .title-name {
            color: #fff;
            font-family: Noto Sans SC;
font-weight: 700;
font-size: 18px;
line-height: 48px;
text-align: center;
        }
    }
    .box-wrap-big {
        width: 400px;
        height: 632px;
        .title {
            width: 400px;
            height: 48px;
            margin-bottom: 12px;
            background: url("@/assets/newImages/title-icon.svg") no-repeat;
            background-size: cover;
            background-position: center;
        }
        .title-name {
            color: #fff;
            font-family: Noto Sans SC;
font-weight: 700;
font-size: 18px;
line-height: 48px;
text-align: center;
        }
    }
}
</style>
