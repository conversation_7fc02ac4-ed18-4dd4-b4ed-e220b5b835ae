<template>
    <Card title="今日值班值守">
        <template v-slot>
    <div class="bodys_box">
        <div class="content_box">
            <div class="left_box">
                <!--                <img class="img_box_1" @click="leftclick" src="../../../../assets/images/command/rightTop_1.svg"/>-->
            </div>
            <div class="center_box">
                <div class="list_box" v-for="(item, index) in dutyList">
                    <div>
                        <div class="image_box">
                            <!--                            <img class="img_size" src="../../../../assets/images/command/image_20.png"/>-->
                            <img
                                class="img_size"
                                :src="
                                    item.avatar
                                        ? item.avatar
                                        : '../../../../assets/images/command/None.png'
                                "
                            />
                        </div>
                        <div class="name_text">{{ item.userName }}</div>
                        <div class="zhiwu_text">{{ item.label }}</div>
                        <div class="zhiwu_text">{{ item.phone }}</div>
                    </div>
                </div>
            </div>
            <div class="right_box">
                <!--                <img @click="rightclick" class="img_box_2" src="../../../../assets/images/command/rightTop_2.svg"/>-->
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
// import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { getwatchList } from "../../../../assets/js/api/api";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
const pagesizeone = ref(0);
const pagesize = ref(3);
// const pagenum = ref(1)
// let p1 = require("../../../../assets/images/photo/photo1.jpg")
const dutyList = ref([
    {
        avatar: new URL(
            "../../../../assets/images/photo/photo1.jpg",
            import.meta.url,
        ).href, //绝对路径,
        userName: "冯云龙",
        label: "值班人员",
        phone: "19933311043",
    },
    {
        avatar: new URL(
            "../../../../assets/images/photo/photo2.jpg",
            import.meta.url,
        ).href, //绝对路径,
        userName: "崇米娜",
        label: "值班人员",
        phone: "19933310248",
    },
    {
        avatar: new URL(
            "../../../../assets/images/photo/photo3.jpg",
            import.meta.url,
        ).href, //绝对路径,
        userName: "刘培炎",
        label: "值班人员",
        phone: "19933311023",
    },
]);
const leftclick = () => {
    console.log("点击左");
    if (pagesizeone.value != 0 && pagesize.value != 3) {
        pagesizeone.value -= 3;
        pagesize.value -= 3;
        getList();
    }
};
const rightclick = () => {
    console.log("点击右");
    if (pagesize.value <= dutyList.value.length + 1) {
        pagesizeone.value += 3;
        pagesize.value += 3;
        getList();
    }
};
const getList = () => {
    getwatchList({}).then((res) => {
        console.log(res, "获取值班列表");
        console.log(pagesizeone.value, pagesize.value);
        dutyList.value = res.data.data.slice(pagesizeone.value, pagesize.value);
    });
};

onMounted(() => {
    // getList()
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.bodys_box {
    width: 416px;
    height: 224px;
    padding: 16px;
    .content_box {
        width: 100%;
        height: 100%;
        display: flex;
        .left_box {
            display: flex;
            align-items: center;
            margin-right: 8px;
            .img_box_1 {
                width: 24px;
                height: 24px;
                flex-shrink: 0;
            }
        }
        .center_box {
            flex: 1;
            display: flex;
            justify-content: space-between;
            .name_text {
                margin-top: 6px;
                color: #47ebeb;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                line-height: 24px; /* 150% */
            }
            .zhiwu_text {
                margin-top: 5px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 142.857% */
            }

            .list_box {
                display: flex;
                justify-content: space-between;
            }
            .image_box {
                display: flex;
                width: 106px;
                height: 136px;
                padding: 4px 3px 4px 4px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
                border: 1px solid #30abe8;
                background: rgba(48, 171, 232, 0.3);
                .img_size {
                    width: 98%;
                    height: 98%;
                }
            }
        }
        .right_box {
            display: flex;
            align-items: center;
            margin-left: 8px;
            .img_box_2 {
                width: 24px;
                height: 24px;
                flex-shrink: 0;
            }
        }
    }
}

.barwrap {
    width: 400px;
    height: 256px;
}
</style>
