<template>
    <Card title="事件上报">
        <template v-slot>
    <div class="Enterprise-consumption-rank">
        <!--    <div class="select-btns">-->
        <!--      <div :class="active === 1 ? 'active-btn' : 'btn'" @click="changeDoor(1)">-->
        <!--        电-->
        <!--      </div>-->
        <!--      <div :class="active === 2 ? 'active-btn' : 'btn'" @click="changeDoor(2)">-->
        <!--        水-->
        <!--      </div>-->
        <!--      <div :class="active === 3 ? 'active-btn' : 'btn'" @click="changeDoor(3)">-->
        <!--        天然气-->
        <!--      </div>-->
        <!--      <div :class="active === 4 ? 'active-btn' : 'btn'" @click="changeDoor(4)">-->
        <!--        蒸汽-->
        <!--      </div>-->
        <!--    </div>-->
        <!--    <div class="barwrap" ref="barwrap"></div>-->
        <table border="0" cellspacing="0" class="tables">
            <tr>
                <th class="th_1">事件类型</th>
                <th class="th_2">事件名称</th>
                <th class="th_3">上报人</th>
                <th class="th_4">上报时间</th>
            </tr>
            <tr v-for="(item, index) in peopleData" :key="index">
                <td>
                    <div
                        class="tdFontDiv"
                        style="width: 108px"
                        :title="item.event_name"
                    >
                        {{ item.event_name }}
                    </div>
                </td>
                <td>
                    <div
                        class="tdFontDiv"
                        style="width: 10px"
                        :title="item.eventTypeName"
                    >
                        {{ item.eventTypeName }}
                    </div>
                </td>
                <td>
                    <div class="tdFontDiv" style="width: 86px">
                        {{ item.submit_person }}
                    </div>
                </td>
                <td>
                    <div class="tdFontDiv" style="width: 86px">
                        {{ item.occurrence_time }}
                    </div>
                </td>
            </tr>
        </table>
        </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import { getescalationList } from "../../../../assets/js/api/api";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
const unit = ref("千瓦时");
const active = ref(1);
const peopleData = ref([]);

var echartData = ref([]);
let option = reactive({});
let barwrap = ref(null);

onMounted(() => {
    getescalationList({}).then((res) => {
        console.log(res, "获取事件上报");
        peopleData.value = res.data.data.slice(0, 5);
    });
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.Enterprise-consumption-rank {
    width: 416px;
    height: 224px;
    padding: 16px;
    .select-btns {
        width: 248px;
        height: 24px;
        display: flex;
        gap: 8px;
        margin-left: 152px;
        .active-btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.5px solid #ffc61a;
            background: rgba(255, 198, 26, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
        .btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.622px solid #1ab2ff;
            background: rgba(26, 178, 255, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
    }
    .barwrap {
        width: 400px;
        height: 232px;
        // margin-top: 16px;
        // background-color: aqua;
    }
    .tables {
        table-layout: fixed;
        width: 432px !important;
        // height: 232px;
    }
    tr:nth-child(1) {
        height: 38px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400 !important;
        line-height: 22px; /* 137.5% */
        letter-spacing: 0.96px;
        background: linear-gradient(
            90deg,
            rgba(22, 115, 139, 0) 3.2%,
            rgba(22, 115, 139, 0.5) 50.16%,
            rgba(22, 115, 139, 0) 100%
        );

        /* background: linear-gradient(270deg, #16738B 3.16%, rgba(22, 115, 139, 0.00) 100.2%); */
    }
    th {
        /*font-weight: 400 !important;*/
        /*font-size: 14px;*/
        color: #47ebeb;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px; /* 142.857% */
    }
    .th_1 {
        width: 80px;
    }
    .th_2 {
        width: 80px;
    }
    .th_3 {
        width: 80px;
    }
    .thFontDiv {
        width: 98px;
    }
    td {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 142.857% */
        text-align: center;
        /*color: #9DDCEA;*/
        /*text-align: center;*/
        /*font-size: 14px;*/
        /*font-style: normal;*/
        /*font-weight: 400;*/
        /*line-height: 22px; !* 157.143% *!*/
        letter-spacing: 0.84px;
        height: 38px;
        border-bottom: 1px solid rgba(14, 161, 176, 0.35);
    }
    .tdFontDiv {
        width: 100% !important;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
</style>
