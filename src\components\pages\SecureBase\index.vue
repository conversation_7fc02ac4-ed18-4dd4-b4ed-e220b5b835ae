<template>
    <div class="secure-base">
        <secure-base-left @openDialog="openDialog"></secure-base-left>
        <secure-base-right @openDialog="openDialog"></secure-base-right>
       <secure-base-nav></secure-base-nav>
        <!-- <test></test> -->
        <!-- 对话框组件 -->
        <template v-for="(value, key) in dialogStates" :key="key">
            <component
                :is="getDialogComponent(key)"
                v-if="dialogStates[key]"
                @closeDialog="closeDialog"
                :ref="key + 'Ref'"
                :currentValue="currentValue"
            ></component>
        </template>
    </div>
</template>
<script setup>
//园区信息
import ParkInformationDialog from "./Diaolog/parkInformationDialog/index.vue";
//安全管理机构
import securityManagementDialog from "./Diaolog/securityManagementDialog/index.vue";
//企业信息
import informationDialog from "./Diaolog/informationDialog/index.vue";
//人员档案
import personnelFileDialog from "./Diaolog/personnelFileDialog/index.vue";
// 第三方单位
import thirdPartyDialog from "./Diaolog/thirdPartyDialog/index.vue";
//事故事件
import accidentEventsDialog from "./Diaolog/accidentEventsDialog/index.vue";
//危险化学品禁限控
import hazardousChemicalsControlDialog from "./Diaolog/hazardousChemicalsControlDialog/index.vue";
//工艺禁限控
import workmanshipControlDialog from "./Diaolog/workmanshipControlDialog/index.vue";
//装置禁限控
import deviceControlDialog from "./Diaolog/deviceControlDialog/index.vue";
//安全生产行政许可
import productCertificateDialog from "./Diaolog/productCertificateDialog/index.vue";
//即将到期安全行政许可
import productCertificateExpireDialog from "./Diaolog/productCertificateExpireDialog/index.vue";
//装置开停车和大检修
import outageMaintenanceDialog from "./Diaolog/outageMaintenanceDialog/index.vue";
//监督检查
import supervisionDocumentDialog from "./Diaolog/supervisionDocumentDialog/index.vue";
//执法任务
import lawTaskDialog from "./Diaolog/lawTaskDialog/index.vue";
//重大危险源
import majorHazardDialog from "./Diaolog/majorHazardDialog/index.vue";
//危险化工工艺
import craftDialog from "./Diaolog/craftDialog/index.vue";
//值班值守
import dutyDialog from "./Diaolog/dutyDialog/index.vue";
//危险化学品
import dangerChemicalDialog from "./Diaolog/dangerChemicalDialog/index.vue";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import SecureBaseLeft from './SecureBaseLeft.vue';
import SecureBaseRight from './SecureBaseRight.vue';
import SecureBaseNav from './SecureBaseNav.vue'
// import { el } from "element-plus/es/locale";
// 对话框显示状态管理
const dialogStates = reactive({
    parkInformationDialogShow: false,         // 园区信息
    securityManagementDialogShow: false,     // 安全管理机构
    informationDialogShow: false,            // 企业信息
    personnelFileDialogShow: false,          // 人员档案
    thirdPartyDialogShow: false,             // 第三方单位
    accidentEventsDialogShow: false,         // 事故事件
    hazardousChemicalsControlDialogShow: false, // 危险化学品禁限控
    workmanshipControlDialogShow: false,     // 工艺禁限控
    deviceControlDialogShow: false,          // 装置禁限控
    productCertificateDialogShow: false,      // 安全生产行政许可
    outageMaintenanceDialogShow: false,      // 装置开停车和大检修
    supervisionDocumentDialogShow: false,     // 监督检查
    majorHazardDialogShow: false,             // 重大危险源
    craftDialogShow: false,// 危险化工工艺
    dutyDialogShow: false,// 值班值守
    dangerChemicalDialogShow: false,// 危险化学品
    lawTaskDialogShow: false,// 执法任务
    productCertificateExpireDialogShow: false,// 即将到期安全行政许可
});

// 对话框组件映射
const dialogComponents = {
    parkInformationDialogShow: ParkInformationDialog,
    securityManagementDialogShow: securityManagementDialog,
    informationDialogShow: informationDialog,
    personnelFileDialogShow: personnelFileDialog,
    thirdPartyDialogShow: thirdPartyDialog,
    accidentEventsDialogShow: accidentEventsDialog,
    hazardousChemicalsControlDialogShow: hazardousChemicalsControlDialog,
    workmanshipControlDialogShow: workmanshipControlDialog,
    deviceControlDialogShow: deviceControlDialog,
    productCertificateDialogShow: productCertificateDialog,
    outageMaintenanceDialogShow: outageMaintenanceDialog,
    supervisionDocumentDialogShow: supervisionDocumentDialog,
    majorHazardDialogShow: majorHazardDialog,
    craftDialogShow: craftDialog,
    dutyDialogShow: dutyDialog,
    dangerChemicalDialogShow: dangerChemicalDialog,
    lawTaskDialogShow: lawTaskDialog,
    productCertificateExpireDialogShow: productCertificateExpireDialog,
};

// 根据对话框名称获取对应的组件
const getDialogComponent = (dialogName) => {
    return dialogComponents[dialogName];
};
const childRef = ref();
let socket = null;
const audioOption = () => {
    socket && socket.close();
    socket = null;
    let hostname = window.location.hostname;
    if (window.location.protocol === "http:") {
        const wsuri = `ws://${hostname}:32362/security-v2/websocket/security_alarms`;

        socket = new WebSocket(wsuri);
    } else if (window.location.protocol === "https:") {
        socket = new WebSocket(
            "wss://city189.cn:2960/security-v2/websocket/security_alarms",
        );
    }

    //   socket = new WebSocket(wsuri);
    socket.addEventListener("message", function (event) {
        //   console.log(event);
        // console.log(event.data);
    });
};
const getChild = () => {
    // 调用子组件的方法或者变量，通过value
    childRef.value.hello("hello world！");
};
onMounted(() => {
    // getChild()
});
let currentValue = ref(null)
// 打开对话框
const openDialog = (dialogName,btnSelect) => {
    currentValue.value = btnSelect
    console.log(dialogName, '打开弹窗---------------',btnSelect);
    // 检查对话框名称是否有效
    if (dialogName in dialogStates) {
        dialogStates[dialogName] = true;
    } else {
        console.warn(`未知的对话框名称: ${dialogName}`);
    }
}

// 关闭对话框

const closeDialog = (dialogName) => {
    console.log(dialogName, '关闭弹窗');
    // 检查对话框名称是否有效
    if (dialogName in dialogStates) {
        dialogStates[dialogName] = false;
    } else {
        console.warn(`未知的对话框名称: ${dialogName}`);
    }
}
</script>

<style lang="less" scoped></style>
