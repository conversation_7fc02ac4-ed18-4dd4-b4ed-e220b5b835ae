<template>
    <div class="secure-base-right">
       <right-top @openDialog="openDialog"></right-top>
       <right-center @openDialog="openDialog"></right-center>
       <right-bottom  @openDialog="openDialog"></right-bottom>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";
import WrapSlot from "../../commen/WrapSlot.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightBottom from "./subgroup/RightBottom.vue";
import RightCenter from "./subgroup/RightCenter.vue";
import RightBottomSecond from "../EmergencyCommand/subgroup/RightTop.vue";
const emit = defineEmits(["openDialog"]);
const { proxy } = getCurrentInstance();

//打开下钻弹窗
const openDialog = (val,btnSelect) => {
    proxy.$emit('openDialog',val,btnSelect);
}
</script>

<style lang="less" scoped>
.secure-base-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
