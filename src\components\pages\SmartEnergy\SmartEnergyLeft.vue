<template>
    <div>
        <div class="smart-energy-left">
            <left-top @energy="energy"></left-top>
            <left-center></left-center>
            <left-bottom></left-bottom>
        </div>
        <energy-history-dialog
            v-if="energyDialogShow"
            :columnShow="columnShow"
            @closeDialog="closeDialog"
        ></energy-history-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";

import WrapSlot from "../../commen/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import energyHistoryDialog from "./subgroup/energyHistoryDialog.vue";
const { proxy } = getCurrentInstance();

const energyDialogShow = ref(false);
const columnShow = ref("Electricity");
const energy = (val) => {
    columnShow.value = val;
    console.log(val);
    proxy.$loading.show();

    energyDialogShow.value = true;
};
const closeDialog = () => {
    proxy.$loading.hide();
    energyDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.smart-energy-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
