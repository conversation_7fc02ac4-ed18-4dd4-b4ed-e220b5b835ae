<template>
    <Card title="企业单位产值能耗排行">
        <template v-slot>
            <div class="right-bottom-second">
                <div
                    class="progress-box"
                    v-for="(item, index) in peopleData"
                    :key="index"
                >
                    <div class="title-box">
                        <div class="title-box-left">
                            <div class="ranking">NO.{{ index + 1 }}</div>
                            <div class="name">&nbsp;{{ item.name }}</div>
                        </div>
                        <div class="title-box-right">
                            <div class="num">{{ item.value }}&nbsp;</div>
                            <div class="unit">tce/万元</div>
                        </div>
                    </div>
                    <el-progress
                        class="progress-height"
                        :stroke-width="16"
                        :percentage="item.percentage"
                    ></el-progress>
                </div>
            </div>
        </template>
        </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import { energy_unit_of_output_rank } from "../../../../assets/js/api/smartEnergy";

const peopleData = ref([]);
const serachEnergy = () => {
    energy_unit_of_output_rank({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            let max = res.data.data[0].result;
            peopleData.value = res.data.data.map((x) => {
                return {
                    name: x.enterprise_name,
                    value: x.result,
                    percentage: (x.result / max) * 100,
                };
            });
        }
    });
};
onMounted(() => {
    serachEnergy();
});
</script>

<style lang="less" scoped>
.right-bottom-second {
    width: 416px;
    height: 224px;
    padding: 16px;
}
.progress-box {
    width: 416px;
    height: 42px;
}
.title-box {
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    justify-content: space-between;
    .title-box-left {
        display: flex;
        .ranking {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 22px; /* 157.143% */
        }
    }
    .title-box-right {
        display: flex;
        .num {
            color: #fff;
            text-align: right;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 22px; /* 157.143% */
        }
        .unit {
            color: #fff;
            text-align: right;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 150% */
        }
    }
}

::v-deep .el-progress-bar__inner {
    background: linear-gradient(
        270deg,
        rgba(71, 235, 235, 0.6) 0%,
        rgba(71, 235, 235, 0.15) 100%
    );
    height: 12px;
    margin: 2px;
    border-image-source: linear-gradient(
        270deg,
        rgba(71, 235, 235, 0.6) 0%,
        rgba(71, 235, 235, 0.15) 100%
    );
    border-radius: 0%;
}
::v-deep .el-progress {
    height: 16px;
}
::v-deep .el-progress-bar__outer {
    width: 416px;
    height: 16px !important;
    background-color: rgba(255, 255, 255, 0);
    // border: 1px solid #4C94FF;
    border-radius: 0%;
}
::v-deep .el-progress__text {
    display: none;
}
.progress-height {
    height: 16px;
}
</style>
