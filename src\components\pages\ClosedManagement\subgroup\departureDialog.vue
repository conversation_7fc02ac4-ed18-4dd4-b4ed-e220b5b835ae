<template>
    <div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">大气详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    
                   <div class="container">
                        <div v-if="departureData.alarmed != 0" class="error_log_1">
                            <div class="log_content">{{ "人员离岗" }}</div>
                        </div>
                        <div v-if="departureData.alarmed == 0" class="success_log">
                            <div class="log_content">{{ "暂无违规" }}</div>
                        </div>
                        <div class="container_top">
                            <div class="contain_title">
                                <!-- <div class="title_top" :title="dialogData.goodsTypeName">{{ limitData.goodsTypeName || '' }}</div> -->
                            </div>
                            <div class="contain_content">
                                <!-- <div class="contain_name">{{ dialogData.goodsName || '' }}</div> -->
                                <el-row>
                                    <el-col :span="12" class="innner special">
                                        <div
                                            class="headLocation"
                                            :title="departureData.location"
                                        >
                                            {{ departureData.location }}
                                        </div>
                                        <!-- <div class="content" :title="dialogData.orderNumber">{{ dialogData.orderNumber || '' }}</div> -->
                                    </el-col>
                                </el-row>
                                <el-row v-for="(item, index) in departureData.patrolList">
                                    <el-col :span="12" class="innner special">
                                        <div class="headline">{{ item.personName }}</div>
                                        <div class="content">{{ item.phoneNumber }}</div>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" class="innner special">
                                        <div class="workTime">
                                            工作时间：{{
                                                departureData.workTimeStart +
                                                "--" +
                                                departureData.workTimeEnd
                                            }}
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <div class="container_con">
                            <div class="top-selects">
                                <div class="slect-tabs" ref="tabsRef">
                                    <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="changeContent(item.value)">
                                        <div class="tab-item-text" :class="{'active-tab-item':contentCheck === item.value}" >{{ item.label }}</div>
                                        <div class="tab-item-line" v-if="contentCheck === item.value"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="alermCon">
                                <div v-show="contentCheck == 1" class="img">
                                    <div v-if="videoGrey" class="img-no">暂无数据</div>
                                    <div ref="img" class="img" v-show="!videoGrey"></div>
                                </div>
                                <div class="iocList" v-show="contentCheck != 1" id="img">
                                    <div class="table" ref="table">
                                        <el-table
                                            class="tablebox"
                                            :data="tabData"
                                            style="width: 100%"
                                            @row-click="clickRow"
                                        >
                                            <el-table-column type="index" label="序号">
                                                <template #default="{ $index }">
                                                    {{
                                                        (pageNum - 1) * pageSize +
                                                        $index +
                                                        1
                                                    }}
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                prop="cameraName"
                                                label="设备名称"
                                                show-overflow-tooltip
                                            />
                                            <el-table-column
                                                prop="location"
                                                label="地点"
                                                show-overflow-tooltip
                                            />
                                            <el-table-column
                                                prop="alarmTime"
                                                label="告警时间"
                                                show-overflow-tooltip
                                            />
                                        </el-table>
                                        <el-pagination
                                            class="pagination"
                                            background
                                            v-model:currentPage="pageNum"
                                            :page-size="pageSize"
                                            @current-change="handleCurrentChange"
                                            layout="->,total, prev, pager, next"
                                            :total="total"
                                        />
                                    </div>
                                    <div class="pic" ref="pic">
                                        <div class="picheader">
                                            <div class="title">
                                                抓图
                                                <span
                                                    style="position: absolute; right: 0"
                                                    @click="closePic"
                                                    >X</span
                                                >
                                            </div>
                                            <div class="picCon">
                                                <img :src="clickRowData.alarmPic" alt="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
    
    
</template>

<script setup>
import {
    ref,
    watch,
    defineEmits,
    toRefs,
    onMounted,
    getCurrentInstance,
} from "vue";
import { findAlarm } from "../../../../assets/js/api/comprehensiveSupervision";
import { nj_equipment_info } from "../../../../assets/js/api/parkArchives";
import {
    carGpsLine,
    getDictByType,
} from "../../../../assets/js/api/comprehensiveSupervision";
import { getFlv } from "../../../../assets/js/api/safetySupervision";
import AMapLoader from "@amap/amap-jsapi-loader";
const { proxy } = getCurrentInstance();
const boundary = ref(false);
const speed = ref(false);
const props = defineProps({
    departureData: {
        type: [],
    },
});

const { departureData } = props;
console.log(departureData);

const contentCheck = ref(1);
const img = ref(null);
const videoGrey = ref(true);
watch(
    contentCheck,
    (a, b) => {
        console.log(a);
        videoGrey.value = true;
        if (
            a == 1 &&
            img.value &&
            departureData.cameraId != null &&
            departureData.cameraId != undefined
        ) {
            // videoGrey.value = false;

            playVideo();
            pageNum.value = 1;
        }
    },
    {
        immediate: true,
    },
);

//emit子传父
const emit = defineEmits(["closeDialog"]);
//关闭弹框
const closeDialog = () => {
    emit("closeDialog", "depart");
    destroy();
};
//顶部tab
let topTabs = ref([
    {
        value:1,
        label:'视频监控'
    },
    {
        value:2,
        label:'告警信息'
    }
]);
const tabsRef = ref(null);

//切换内容
const changeContent = (checkValue) => {
    contentCheck.value = checkValue;
};

onMounted(() => {
    getInitData();
    console.log(departureData);
    if (departureData.cameraId != null && departureData.cameraId != undefined) {
        console.log(departureData.cameraId);
        // videoGrey.value = false;
        playVideo();
    }
});

var jessibuca = null;
let tabData = ref([]);
let videolist = ref([]);
let initData = ref({});
const pageNum = ref(1);
const pageSize = ref(8);
const total = ref(0);
//点击tab右侧弹出框
const drawer = ref(false);
//pic dom元素
const pic = ref(null);
//table表格数据
const table = ref(null);
//选中table一行数据
const clickRowData = ref({});
//获取初始列表数据
const getInitData = () => {
    let params = {
        cameraId: departureData.cameraId,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    };
    findAlarm(params).then((res) => {
        if(res.data&&res.data.data&&res.data.data.records){
        tabData.value = res.data.data.records;
        console.log(res.data.data, "表格数据");
        total.value = Number(res.data.data.total);
        initData.value = res.data.data;
        }
    });
};
const getCaption = (obj, text) => {
    let index = obj.lastIndexOf(text) + text.length - 1;
    obj = obj.substring(index + 1, obj.length);
    console.log(obj);
    return obj;
};
const handleCurrentChange = (val) => {
    pageNum.value = val;
    getInitData();
};

//点击表格某行
const clickRow = (row, column, event) => {
    console.log(row, "点击表格某行");
    clickRowData.value = row;
    if (
        clickRowData.value.alarmPic != null &&
        clickRowData.value.alarmPic != undefined
    ) {
        var middlePic = getCaption(clickRowData.value.alarmPic, "/evo-apigw/");
        let font = window.location.protocol;
        let host = window.location.hostname;
        console.log(
            "hahahhahahhahahahhah",
            "/" +
                font +
                "//" +
                host +
                ":13005/" +
                clickRowData.value.alarmPic
                    .split("/")
                    .splice(3, clickRowData.value.alarmPic.split("/").length)
                    .join("/"),
        );

        Reflect.set(
            clickRowData.value,
            "alarmPic",
            font +
                "//" +
                host +
                ":13005/" +
                clickRowData.value.alarmPic
                    .split("/")
                    .splice(3, clickRowData.value.alarmPic.split("/").length)
                    .join("/"),
        );
    }
    drawer.value = true;
    console.log(pic.value);
    table.value.style.width = "50%";
    pic.value.style.width = "50%";
    pic.value.style.display = "block";
};
const closePic = () => {
    table.value.style.width = "100%";
    pic.value.style.display = "none";
    pic.value.style.width = "0%";
};
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
//默认展示视频
const playVideo = () => {
    destroy();
    videolist.value = departureData.cameraId;
    getFlv({
        equipmentIdList: [videolist.value],
    }).then((response) => {
        if (
            response.data.length > 0 &&
            response.data[0].flvAddress &&
            response.data[0].flvAddress != "离线"
        ) {
            console.log(response, "视频数据");
            jessibuca = new JessibucaPro({
                container: img.value,
                videoBuffer: 0.2, // 缓存时长
                isResize: false,
                decoder: "/Jessibuca/decoder-pro.js",
                text: "",
                loadingText: "加载中",
                showBandwidth: true, // 显示网速
                operateBtns: {
                    fullscreen: false,
                    screenshot: false,
                    play: false,
                    audio: false,
                    performance: false,
                },
                forceNoOffscreen: true,
                isNotMute: false,
                heartTimeout: 10,
                ptzClickType: "mouseDownAndUp",
            });
            jessibuca.play(proxy.$changeUrl(response.data[0].flvAddress));
            videoGrey.value = false;
        } else {
            console.log("zanwushipin");
            videoGrey.value = true;
        }
    });

    // jessibuca.play(res.data.data.records[0].alarmPic);
};
</script>

<style lang="less" scoped>
.title_top {
    color: #fff;
    text-align: center;
    font-family: "Noto Sans SC";
    font-size: 28px;
    font-style: normal;
    font-weight: 700;
    line-height: 166px;
    width: 180px;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap;
}

.contain_title {
    width: 166px;
    height: 166px;
    background: url("../../../../assets/images/closed/people_background.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
}

.infrastructure-dialog {
    width: 840px;
    height: 762px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}

.header {
    width: 100%;
    height: 52px;
    flex-shrink: 0;
    background: url("../../../../assets/images/dialog/header52.svg") no-repeat
        center center;
    background-size: cover;
    background-position: center;
    display: flex;

    .title {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 52px; /* 150% */
        margin-left: 32px;
    }

    .close {
        width: 24px;
        height: 24px;
        background: url("../../../../assets/images/dialog/close.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: auto 0;
        margin-left: 696px;
    }
}
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 762px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 762px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 666px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.top-selects{
            width: 8000px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 800px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
.container {
    // width: 792px;
    // height: 680px;
    // padding: 16px 24px;
    // background-color: aquamarine;
    display: flex;
    flex-direction: column;
    .bottom-title {
        width: 70px;
        height: 24px;
        display: flex;
        gap: 4px;
        // margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }

        .text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }

    .details {
        margin-top: 10px;
        width: 792px;
        height: 169px;
        // background-color: #47ebeb;
        .innner {
            display: flex;
            padding: 0 !important;

            .headline {
                width: 198px;
                height: 36px;
                color: #47ebeb;
                text-align: left;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 36px; /* 150% */
                border: 1px solid #30abe8;
                background: rgba(48, 171, 232, 0.1);
                padding: 0px 12px;
            }

            .content {
                width: 198px;
                height: 36px;
                border: 1px solid #30abe8;
                //width: 251px;
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 36px; /* 150% */
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 0px 12px;
            }
        }

        .special {
            margin-top: 0px;
        }
    }

    .video {
        width: 792px;
        height: 244px;
        flex-shrink: 0;
        background: rgba(48, 171, 232, 0.15);
        // margin-top: 17px;
    }
}

.select {
    z-index: 999;
    position: absolute;
}

.container_top {
    display: flex;
}
.container_con {
    flex: 1;
    display: flex;
    flex-direction: column;
    .checkBox {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        height: 50px;
        .checkbtn {
            width: 96px;
            height: 16px;
            border-radius: 16px;
            display: flex;
            padding: 6px 12px;
            justify-content: center;
            align-items: center;
            gap: 16px;
            color: #fff;
            font-size: 14px;
            background: url(../../../../assets/images/closed/blueBtn.svg)
                no-repeat center;
            background-size: 100% 100%;
            margin-left: 10px;
            margin-right: 10px;
            cursor: pointer;
        }
        .check {
            background: url(../../../../assets/images/closed/yellowBtn.svg)
                no-repeat center;
            background-size: 100% 100%;
        }
    }
    .alermCon {
        height: 400px;
        width: 100%;
        margin-top: 16px;
        .img {
            height: 400px;
            width: 100%;
            display: flex;
            justify-content: center;
            .img-no {
                margin: auto 0;
                color: #fff;
                text-align: center;
                font-family: "Noto Sans SC";
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 140% */
            }
        }
        .iocList {
            display: flex;
            height: 100%;
            .table {
                width: 100%;
                transition: all 0.5s;
            }
            .pic {
                width: 0;
                transition: all 0.5s;
                overflow: hidden;
                .picheader {
                    height: 50px;
                    color: #fff;
                    font-size: 14px;
                    .title {
                        text-align: center;
                        position: relative;
                    }
                    .picCon {
                        width: 100%;
                        img {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
    .checkBox::after {
        content: "";
        border-bottom: dashed 1px #30abe8;
        width: 30%;
        height: 30%;
    }
    .checkBox::before {
        content: "";
        border-bottom: dashed 1px #30abe8;
        width: 30%;
        height: 30%;
    }
}

.contain_content {
    .contain_name {
        color: #fff;
        text-align: left;
        font-family: "Noto Sans SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: 26px;
        width: 118px;
        padding: 0px 12px;
    }

    .innner {
        display: flex;
        padding: 0 !important;

        .headline {
            width: 128px;
            height: 36px;
            color: #47ebeb;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 150% */
            padding: 0px 12px;
        }
        .headLocation {
            width: 218px;
            height: 36px;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 150% */
            padding: 0px 12px;
            color: #fff;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .content {
            width: 300px;
            height: 36px;
            //width: 251px;
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 150% */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 0px 12px;
        }
        .workTime {
            width: 100%;
            height: 36px;
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 150% */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 0px 12px;
        }
    }
}
.success_log {
    width: 104px;
    height: 96px;
    background: url("../../../../assets/images/closed/title_success.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    right: 100px;
    top: 52px;
}
.error_log {
    width: 104px;
    height: 96px;
    background: url("../../../../assets/images/closed/title_error.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    right: 100px;
    top: 52px;
}
.error_log_1 {
    width: 104px;
    height: 96px;
    background: url("../../../../assets/images/closed/title_error.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    right: 100px;
    top: 52px;
}
.log_content {
    color: #fff;
    text-align: center;
    font-family: "Noto Sans SC";
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-top: 25px;
}

.tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }
        :deep(.el-pagination.is-background .btn-next:disabled) {
            background-color: transparent;
           
        }
        :deep(.el-pagination.is-background .btn-prev:disabled ) {
            background-color: transparent;
           
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
</style>
