<template>
    <div class="basic-archives">
            <div class="top-btns">
                <div
                    :class="innerActive === 1? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(1)"
                >
                    基础信息
                </div>
                <div
                    :class="innerActive === 2? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(2)"
                >
                    企业从业人员
                </div>
            </div>
        <div class="value-table" v-if="innerActive === 1">
            <table>
                <tr>
                    <td class="title1"><span class="title">企业名称</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.enterprise_name"
                        >
                            {{ enterpriseInfoData.enterprise_name }}
                        </div>
                    </td>
                    <td class="title1">
                        <span class="title">社会统一信用代码</span>
                    </td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.credit_code"
                        >
                            {{ enterpriseInfoData.credit_code }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1">
                        <span class="title">注册资金（万元）</span>
                    </td>
                    <td class="text1">
                        <div class="text" :title="enterpriseInfoData.capital">
                            {{ enterpriseInfoData.capital }}
                        </div>
                    </td>
                    <td class="title1"><span class="title">成立日期</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.establish_date"
                        >
                            {{ enterpriseInfoData.establish_date }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1"><span class="title">经营状态</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.sd_operate_status"
                        >
                            {{ enterpriseInfoData.sd_operate_status }}
                        </div>
                    </td>
                    <td class="title1">
                        <span class="title">法定代表人</span>
                    </td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.legal_person_name"
                        >
                            {{ enterpriseInfoData.legal_person_name }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1"><span class="title">行业</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.sd_business_scope"
                        >
                            {{ enterpriseInfoData.sd_business_scope }}
                        </div>
                    </td>
                    <td class="title1"><span class="title">规模情况</span></td>
                    <td class="text1">
                        <div class="text" :title="enterpriseInfoData.scale">
                            {{ enterpriseInfoData.scale }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1">
                        <span class="title">从业人员数量</span>
                    </td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.onjob_staff"
                        >
                            {{ enterpriseInfoData.onjob_staff }}
                        </div>
                    </td>
                    <td class="title1">
                        <span class="title">母公司名称</span>
                    </td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.parent_company"
                        >
                            {{ enterpriseInfoData.parent_company }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1">
                        <span class="title">是否国有企业</span>
                    </td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.is_state_owned"
                        >
                            {{
                                enterpriseInfoData.is_state_owned == 1
                                    ? "是"
                                    : "否"
                            }}
                        </div>
                    </td>
                    <td class="title1">
                        <span class="title">集团公司名称</span>
                    </td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.group_company"
                        >
                            {{ enterpriseInfoData.group_company }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1"><span class="title">隶属关系</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.member_ship"
                        >
                            {{ member_ship_data&&member_ship_data.find(i=>i.dictEncoding==enterpriseInfoData.member_ship)!=undefined?member_ship_data.find(i=>i.dictEncoding==enterpriseInfoData.member_ship).dictName:'-' }}
                        </div>
                    </td>
                    <td class="title1"><span class="title">联系方式</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.contact_way"
                        >
                            {{ enterpriseInfoData.contact_way }}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="title1"><span class="title">经营范围</span></td>
                    <td class="text1">
                        <div
                            class="text"
                            :title="enterpriseInfoData.business_scope"
                        >
                            {{ enterpriseInfoData.business_scope }}
                        </div>
                    </td>
                </tr>
                <!-- <tr>
          <td class="title1"><span class="title"></span></td>
          <td class="text1"><div class="text">{{}}</div></td>
          <td class="title1"><span class="title"></span></td>
          <td class="text1"><div class="text">{{}}</div></td>
        </tr> -->
            </table>
        </div>
        <div class="alarm" v-else>
            <el-table class="tablebox" :data="tableData" style="width: 100%">
                <el-table-column prop="num" label="序号" style="width: 5%" />
                <el-table-column
                    prop="employee_name"
                    label="员工名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="employee_code"
                    label="员工代码"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="sd_gender"
                    label="性别"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="phone"
                    label="联系电话"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="belong_depart"
                    label="所属部门"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="belong_post"
                    label="所属岗位"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="sd_is_onjob"
                    label="在职情况"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                layout="->,total, prev, pager, next"
                :total="total"
                v-model:currentPage="pageNum"
                :page-size="pageSize"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    toRefs,
} from "vue";
import {
    enterprise_info,
    enterprise_staff_files,
} from "../../../../assets/js/api/parkArchives";
import {
  parkIocApi_allList
} from "@/assets/js/api/secureBase";
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const tableData = ref([]);
const middleTableData = ref([]);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(3);
//隶属关系
let member_ship_data = ref([]);
const searchStaffFiles = () => {
    tableData.value = [];
    for (let i = 0; i < pageSize.value; i++) {
        let pageIndex = (pageNum.value - 1) * pageSize.value + i;
        console.log(pageIndex);
        if (pageIndex < total.value) {
            tableData.value[i] =
                middleTableData.value[(pageNum.value - 1) * pageSize.value + i];
        }
    }
};
const handleCurrentChange = (e) => {
    pageNum.value = e;
    searchStaffFiles();
};
const { pickId } = toRefs(props);
const innerActive = ref(1);
const selectInnerType = (type) => {
    innerActive.value = type;
    if (type == 1) {
    } else {
        middleTableData.value = [];
        enterprise_staff_files({
            enterprise_name: pickId.value,
        }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                total.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableData.value.push(item);
                });
            }
            searchStaffFiles();
        });
    }
};
const enterpriseInfoData = ref({
    enterprise_name: "",
    credit_code: "",
    capital: "",
    establish_date: "",
    sd_operate_status: "",
    legal_person_name: "",
    sd_business_scope: "",
    scale: "",
    onjob_staff: "",
    parent_company: "",
    is_state_owned: "",
    group_company: "",
    member_ship: "",
    contact_way: "",
});
const getType = () => {
  parkIocApi_allList({
    pid: 273
  }).then(res => {
    member_ship_data.value = res.data.data
  })
}
getType();
onMounted(() => {
    console.log("基础");
    if (pickId.value != null) {
        console.log(pickId.value);
        enterprise_info({
            enterprise_name: pickId.value,
        }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                console.log(res.data.data[0]);
                enterpriseInfoData.value = res.data.data[0];
            }
        });
    } else {
        console.log(22222);
    }
});
</script>

<style lang="less" scoped>
.basic-archives {
    width: 792px;
    height: 364px;
    margin-top: 16px;
    // background-color: #47EBEB;
   
    .top-btns{
        display: flex;
        gap:8px;
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 16px;
text-align: center;

        .active-top-btn{
           
border-radius: 2px;
padding: 4px 8px;
border: 1px solid #FFC61A;
color:#FFC61A;
        }
        .top-btn{
            border-radius: 2px;
padding: 4px 8px;
border: 1px solid #80EAFF;
color:#80EAFF;
        }
    }
    .value-table {
        width: 792px;
        height: 324px;
        margin-top: 12px;
        table {
            border-collapse: collapse;
        }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }

        .title1 {
            display: block;
            width: 112px;
            height: 36px;
            padding: 0 12px;
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 157.143% */
            background-color: rgba(48, 171, 232, 0.1);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .text1 {
            width: 236px;
            height: 36px;
            padding: 0 12px;
            color: #fff;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 157.143% */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .text {
                width: 236px;
                display: block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .alarm {
        width: 792px;
        height: 288px;
        margin-top: 12px;
        .tablebox {
            //表格四个边框的颜色
            border: 1px solid #30abe8 !important;
            th.el-table__cell {
                border: none !important;
            }
        }
        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            border-bottom: 1px solid #30abe8 !important;
            height: 36px;
            background-color: rgba(48, 171, 232, 0.1) !important;
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            // border-color: #30ABE8 !important;
        }
        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: 1px solid #30abe8 !important;
                border-right: 1px solid #30abe8 !important;
            }
        }
        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
            // border-color: #30ABE8 !important;
        }
        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
        :deep(.el-table__cell) {
            border-right: 1px solid #30abe8 !important ;
            border-bottom: 1px solid #30abe8 !important  ;
        }
        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }
        .pagination {
            margin-top: 16px;
        }
        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 2px;
            border: 1px solid #47ebeb;
            background: linear-gradient(
                180deg,
                rgba(71, 235, 235, 0) 50%,
                rgba(71, 235, 235, 0.45) 100%
            );
            color: #47ebeb;
            text-align: center;

            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 2px;
            border: 1px solid #47ebeb;
            background: linear-gradient(
                180deg,
                rgba(71, 235, 235, 0) 50%,
                rgba(71, 235, 235, 0.45) 100%
            );
            color: #47ebeb;
            text-align: center;

            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination .btn-next) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination .btn-prev) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination__total) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
}
</style>
