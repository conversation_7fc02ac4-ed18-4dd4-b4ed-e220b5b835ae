<template>
    <div>
    <div class="safety-supervision-left">
        <!-- <wrap-slot
            title1="重大危险源"
            title2="重点工艺"
            title3="重点监管化学品"
        >
            <template #content1>
                <left-top @major="major"></left-top>
            </template>
            <template #content2>
                <left-center></left-center>
            </template>
            <template #content3>
                <left-bottom></left-bottom>
            </template>
        </wrap-slot> -->
        <left-top @major="major"></left-top>
        <left-center></left-center>
        <left-bottom></left-bottom>
    </div>
    <major-hazard-enterprises-dialog  v-if="majorDialogShow" @closeDialog="closeMajorDialog" :title="title" :sourceLevel="sourceLevel"></major-hazard-enterprises-dialog>
</div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";
import WrapSlot from "../../commenNew/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import MajorHazardEnterprisesDialog from '../DoublePrevention/subgroup/MajorHazardEnterprisesDialog.vue';
const title = ref('')
const sourceLevel = ref('')
const major = (val) => {
    console.log(val, 'major');
    majorDialogShow.value = true;
    proxy.$loading.show();
    title.value = val + '重大危险源';
    sourceLevel.value = val;
}
const { proxy } = getCurrentInstance();

const majorDialogShow = ref(false);

const closeMajorDialog = () => {
    proxy.$loading.hide();
    majorDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.safety-supervision-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
