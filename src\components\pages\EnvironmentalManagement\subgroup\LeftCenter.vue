<template>
    <Card title="环境空气质量"> 
        <template v-slot>
            <div class="environmental-weather">
                <div class="select">
                    <el-select
                        filterable
                        v-model="selectValue"
                        class="m-2"
                        placeholder="请选择"
                        @change="changeMonitor"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <div class="today">
                    <img
                        class="img"
                        src="../../../../assets/images/card/weather.svg"
                        alt=""
                    />
                    <div class="top-liner-one"></div>
                    <div class="first">
                        <div class="big-text">{{ weather.temperature }}℃</div>
                        <div class="text">{{ weather.weather }}</div>
                    </div>
                    <div class="top-liner"></div>
                    <div class="second">
                        <div class="big-text">{{ nowWeather.aqi }}</div>
                        <div class="text">AQI</div>
                    </div>
                    <div class="top-liner"></div>
                    <div class="third">
                        <div class="text-right">
                            {{ weather.windDirection }}{{ weather.windPower }}级
                        </div>
                        <div class="text-right">湿度{{ weather.humidity }}%</div>
                    </div>
                    <div class="top-liner"></div>
                    <div class="forth">
                        <div class="text-right">空气质量</div>
                        <div class="text-right">{{ nowWeather.category }}</div>
                    </div>
                </div>
                <div class="interval"></div>
                <div class="prediction">
                    <div
                        class="prediction-wrapper"
                        v-for="(item, index) in predictionList"
                        :key="index"
                    >
                        <div class="show">
                            <div class="date">{{ item.factor_name }}</div>
                            <!-- <img class="img" src="../../../../assets/images/card/weather.svg" alt=""> -->
                            <div class="temperature">{{ item.monitor_value }}</div>
                        </div>
                        <div
                            class="bottom-liner"
                            v-if="index < predictionList.length - 1"
                        ></div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import {
    monitoring_stations,
    eair_quality,
    now,
    aqi,
} from "../../../../assets/js/api/environmentalManagement";
const predictionList = ref([
    {
        factor_name: "CO",
        monitor_value: "",
        field_name: "a21005",
    },
    {
        factor_name: "PM2.5",
        monitor_value: "",
        field_name: "a34004",
    },
    {
        factor_name: "PM10",
        monitor_value: "",
        field_name: "a34002",
    },
    {
        factor_name: "O3",
        monitor_value: "",
        field_name: "a05024",
    },
    {
        factor_name: "SO2",
        monitor_value: "",
        field_name: "a21026",
    },
    {
        factor_name: "NO2",
        monitor_value: "",
        field_name: "a21004",
    },
]);
const selectValue = ref();
const weather = ref({
    humidity: "",
    temperature: "",
    weather: "",
    windDirection: "",
    windPower: "",
});
const nowWeather = ref({
    aqi: "",
    category: "",
});
const options = ref([[]]);
const getLocation = () => {
    AMap.plugin("AMap.CitySearch", function () {
        var citySearch = new AMap.CitySearch();
        citySearch.getLocalCity(function (status, result) {
            if (status === "complete" && result.info === "OK") {
                // address.value = result.city;
                AMap.plugin("AMap.Weather", function () {
                    var wea = new AMap.Weather();

                    wea.getLive(result.city, function (err, data) {
                        // self.handleWeatherInfo(data);
                        console.log("test", data);
                        weather.value = data;
                    });
                });
            }
        });
    });
};
const changeMonitor = (val) => {
    selectValue.value = val;
    searchEairQuality();
};
const searchEairQuality = () => {
    predictionList.value = [
        {
            factor_name: "CO",
            monitor_value: "",
            field_name: "a21005",
        },
        {
            factor_name: "PM2.5",
            monitor_value: "",
            field_name: "a34004",
        },
        {
            factor_name: "PM10",
            monitor_value: "",
            field_name: "a34002",
        },
        {
            factor_name: "O3",
            monitor_value: "",
            field_name: "a05024",
        },
        {
            factor_name: "SO2",
            monitor_value: "",
            field_name: "a21026",
        },
        {
            factor_name: "NO2",
            monitor_value: "",
            field_name: "a21004",
        },
    ];
    eair_quality({ id: selectValue.value }).then((re) => {
        if (re.data.success && re.data.data && re.data.data.length) {
            //   predictionList.value = re.data.data;
            predictionList.value.forEach((item, index) => {
                let find = re.data.data.find(
                    (x) => x.field_name == item.field_name,
                );
                if (find) {
                    Reflect.set(
                        item,
                        "monitor_value",
                        Number(find.monitor_value).toFixed(2),
                    );
                }
            });
        }
    });
};
onMounted(() => {
    getLocation();
    aqi({}).then((res) => {
        if (res.data.now != null) {
            nowWeather.value = res.data.now;
        }
    });
    //     now({ location: '115.14,37.59', key: '618694ec377941278647ba1fcb7c7d51' }).then((res) => {
    //         console.log(res.data.now);
    //         if (res.data.now != null) {
    //             nowWeather.value = res.data.now
    //         }
    //   })
    monitoring_stations({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            options.value = res.data.data.map((x) => {
                return {
                    value: x.id,
                    label: x.name,
                };
            });
            selectValue.value = options.value[0].value;
            searchEairQuality();
        }
    });
});
</script>

<style lang="less" scoped>
.environmental-weather {
    // width: 400px;
    // height: 256px;
    width: 416px;
    height: 224px;
    padding: 16px;
    .select {
        width: 416px;
        height: 24px;
        margin-left: 278px;
        :deep(.el-select) {
            width: 122px;
            height: 24px !important;
            color: #ffffff;
        }
        :deep(.el-input__wrapper) {
            background-color: rgba(26, 178, 255, 0.3);
            // border: 1px solid #1A9FFF !important;
            // border-radius: 4px;
            height: 24px !important;
        }
        :deep(.el-input__inner) {
            // background-color: rgba(26, 178, 255, 0.30);
            color: #ffffff;
            height: 24px !important;
        }
    }
    .today {
        width: 372px;
        height: 56px;
        margin: 0 auto;
        margin-top: 8px;
        display: flex;
        .img {
            width: 48px;
            height: 48px;
        }
        .top-liner-one {
            width: 2px;
            height: 48px;
            background: url("../../../../assets/images/card/top-liner-one.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            margin: auto 16px;
        }
        .top-liner {
            width: 2px;
            height: 48px;
            background: url("../../../../assets/images/card/top-liner-one.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            margin: auto 12px;
        }
        .big-text {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
        }
        .text {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .text-right {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 27px; /* 157.143% */
        }
    }
    .interval {
        width: 416px;
        height: 24px;
        margin-top: 8px;
        background: url("../../../../assets/images/card/interval.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
    }
    .prediction {
        width: 416px;
        height: 108px;
        margin-top: 8px;
        // background-color: aqua;
        display: flex;
        gap: 8px;
        .prediction-wrapper {
            display: flex;
            gap: 8px;
            .show {
                width: 52px;
                height: 80px;
                .date {
                    color: #fff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 56px; /* 157.143% */
                }
                .img {
                    width: 48px;
                    height: 48px;
                    margin: 0 auto;
                    margin-top: 16px;
                }
                .temperature {
                    margin-top: 16px;
                    color: #fff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 38px; /* 150% */
                }
            }
            .bottom-liner {
                width: 2px;
                height: 108px;
                background: url("../../../../assets/images/card/weather-liner.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
    }
}
</style>
