<template>
    <div :class="`two-keynote${size}`">
        <div class="top-btns">
                <div
                    :class="innerActive === 1? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(1)"
                >
                    重点监管的危险化工工艺
                </div>
                <div
                    :class="innerActive === 2? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(2)"
                >
                    重点监管的危险化学品
                </div>
                <div
                    :class="innerActive === 3? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(3)"
                >
                    重大危险源
                </div>
            </div>
        
        <div class="technology alarm" v-if="innerActive === 1">
            <el-table
                class="tablebox"
                :data="tableDataFirst"
                style="width: 100%"
            >
                <el-table-column prop="num" label="序号" style="width: 5%" />
                <el-table-column
                    prop="workshop_name"
                    label="车间名称"
                    width="100"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="product_line_name"
                    label="生产线名称"
                    width="180"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="craft_name"
                    label="生产工艺名称"
                    width="110"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="response_type"
                    label="反应类型"
                    width="100"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="monitor_unit"
                    label="重点监控区域"
                    width="120"

                    show-overflow-tooltip
                />
                <el-table-column
                    prop="craft_intro"
                    label="工艺简介"
                    width="100"

                    show-overflow-tooltip
                />
                <el-table-column
                    prop="craft_danger"
                    label="工艺危险点"
                    width="100"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="typical_craft"
                    label="典型工艺"
                    width="100"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="monitor_craft_param"
                    label="重点监控工艺参数"
                    width="140"

                    show-overflow-tooltip
                />
                <el-table-column
                    prop="control_mode"
                    label="控制方式"
                    width="160"

                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                layout="->,total, prev, pager, next"
                :total="totalFirst"
                v-model:currentPage="pageNumFirst"
                :page-size="pageSizeFirst"
                @current-change="handleCurrentChangeFirst"
            />
        </div>
        <div class="chemical alarm" v-if="innerActive === 2">
            <el-table
                class="tablebox"
                :data="tableDataSecond"
                style="width: 100%"
            >
                <el-table-column prop="num" label="序号" style="width: 5%" />
                <el-table-column
                    prop="chemical_name"
                    label="化学品名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="chemical_nature"
                    label="别名"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="cas_num"
                    label="cas号"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="storage_type"
                    label="存储方式"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="physical_statue"
                    label="物理状态"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                layout="->,total, prev, pager, next"
                :total="totalSecond"
                v-model:currentPage="pageNumSecond"
                :page-size="pageSizeSecond"
                @current-change="handleCurrentChangeSecond"
            />
        </div>
        <div class="major-hazard" v-if="innerActive === 3">
            <div class="top-select">
                <el-select
                    v-model="value"
                    class="m-2"
                    placeholder="请选择"
                    @change="changeMajorHazard"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="value-table">
                <table>
                    <tr>
                        <!-- <td class="title1"><span class="title">统一编码</span></td> -->
                        <td class="title1">
                            <span class="title">重大危险源名称</span>
                        </td>
                        <td class="title1">
                            <span class="title">企业名称</span>
                        </td>
                        <td class="title1">
                            <span class="title">投用时间</span>
                        </td>
                        <td class="title1">
                            <span class="title">场外可能暴露人员数量</span>
                        </td>
                    </tr>
                    <tr>
                        <!-- <td class="value1">
              <span class="value">{{ dialogData.source_sn }}</span>
            </td> -->
                        <td class="value1">
                            <div class="value" :title="dialogData.source_name">
                                {{ dialogData.source_name }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.company">
                                {{ dialogData.company }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.use_date">
                                {{ dialogData.use_date }}
                            </div>
                        </td>
                        <td class="value1">
                            <div
                                class="value"
                                :title="dialogData.people_number"
                            >
                                {{ dialogData.people_number }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="title1"><span class="title">R值</span></td>
                        <td class="title1">
                            <span class="title">重大危险源等级</span>
                        </td>
                        <td class="title1"><span class="title"></span></td>
                        <td class="title1"><span class="title"></span></td>
                    </tr>
                    <tr>
                        <td class="value1">
                            <div class="value" :title="dialogData.r_value">
                                {{ dialogData.r_value }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.source_level">
                                {{ dialogData.source_level }}
                            </div>
                        </td>
                        <td class="value1">
                            <div
                                class="value"
                                :title="dialogData.danger_description"
                            ></div>
                        </td>
                        <td class="value1">
                            <div class="value"></div>
                        </td>
                    </tr>
                </table>
            </div>
            <!-- <div class="bottom-title">
        <div class="icon"></div>
        <div class="text">采集信息</div>
      </div>
      <div class="device">
        <div class="device-btns">
          <div class="btns">
            <div
              :class="deviceActive == 1 ? 'device-active-btn' : 'device-btn'"
              @click="selectDevice(1)"
            >
              实时数据
            </div>
            <div
              :class="deviceActive == 2 ? 'device-active-btn' : 'device-btn'"
              @click="selectDevice(2)"
            >
              数据分析
            </div>
          </div>
        </div>
        <div class="real-time" v-if="deviceActive == 1">
          <div class="real-time-table">
          </div>
        </div>
        <div class="data-analysis" v-if="deviceActive == 2">
          <div class="monitor-btns">
            <div class="center">
              <div
                class="left-arrow"
                @click="prev()"
                :class="{ hide: startIndex <= 0 }"
              ></div>
              <div class="center-content">
                <transition-group
                  name="list-animate"
                  tag="div"
                  style="display: flex; justify-content: space-between"
                >
                  <div
                    class="event-btn-wrap list-animate-item"
                    :class="{ selectActive: selectValue == item.risk_type }"
                    v-for="item in showList"
                    :key="item.risk_type"
                    style="cursor: pointer"
                    @click="clickSelect(item.risk_type)"
                  >
                    {{ item.risk_type }}
                  </div>
                </transition-group>
              </div>
              <div
                class="right-arrow"
                @click="next()"
                :class="{ hide: startIndex + limit >= list.length }"
              ></div>
            </div>
            <div class="linewrap" ref="linewrap"></div>
          </div>
        </div>
      </div> -->
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
    computed,
} from "vue";
import {
    ei_danger_chemical_process,
    ei_danger_chemical,
} from "../../../../assets/js/api/parkArchives";
import {
    getEquipmentInfo,
    getFlv,
    major_danger_source_info,
    major_danger_source_info_all,
    factor,
    ei_major_danger_source,
} from "../../../../assets/js/api/safetySupervision";
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const emit = defineEmits(["sizeChange"]);
const size = ref(2);
const innerActive = ref(1);
watch(innerActive, (a, b) => {
    // console.log(a,b);
    if (a == 3) {
        emit("sizeChange", 2);
    } else {
        emit("sizeChange", 1);
    }
    //   if (a == 1) {
    //     emit("sizeChange", 2);
    //   } else if (a == 2) {
    //     emit("sizeChange", 3);
    //   } else if (a == 3) {
    //     emit("sizeChange", 2);
    //   }
});
const tableDataFirst = ref([]);
const middleTableDataFirst = ref([]);
const totalFirst = ref(0);
const pageNumFirst = ref(1);
const pageSizeFirst = ref(3);
const tableDataSecond = ref([]);
const middleTableDataSecond = ref([]);
const totalSecond = ref(0);
const pageNumSecond = ref(1);
const pageSizeSecond = ref(3);
const value = ref("");
let downLimit = null;
let upLimit = null;
const options = ref([]);
const dialogData = ref({
    source_sn: "",
    source_name: "",
    company: "",
    use_date: "",
    people_number: "",
    r_value: "",
    source_level: "",
    danger_description: "",
});
const changeMajorHazard = (x) => {
    console.log(x);
    major_danger_source_info({ id: x }).then((res1) => {
        let result = res1.data.data[0];
        if (result.people_number == 0) {
            result.people_number = "0人";
        } else if (result.people_number == 1) {
            result.people_number = "1~29人";
        } else if (result.people_number == 2) {
            result.people_number = "30~49人";
        } else if (result.people_number == 3) {
            result.people_number = "50~99人";
        } else {
            result.people_number = "100人以上";
        }
        dialogData.value = result;
    });
};
//重大危险源-数据分析
const showList = computed(() => {
    let computedList = [];
    if (startIndex.value + limit.value > list.value.length) {
        computedList = list.value.slice(startIndex.value, list.value.length);
    } else {
        computedList = list.value.slice(
            startIndex.value,
            startIndex.value + limit.value,
        );
    }

    return computedList;
});
const list = ref([]);
let startIndex = ref(0);
const selectValue = ref();
const limit = ref(6);
const next = () => {
    if (startIndex.value + limit.value < list.value.length) {
        startIndex.value += 1;
    }
};
const prev = () => {
    if (startIndex.value > 0) {
        startIndex.value -= 1;
    }

    //   else {
    //     startIndex.value = list.value.length - 1;
    //   }
};

const clickSelect = (x) => {
    selectValue.value = x;
    xData = [];
    yData = [];
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
    audioOption();
};
let socket = null;
const audioOption = () => {
    socket && socket.close();
    socket = null;
    let hostname = window.location.hostname;
    if (window.location.protocol === "http:") {
        const wsuri = `ws://${hostname}:32362/safety-v2/ioc/pushGasData/${selectValue.value}/${eval(dialogData.value.relate_danger)[0].storageName}/${dialogData.value.company}`;

        socket = new WebSocket(wsuri);
    } else if (window.location.protocol === "https:") {
        socket = new WebSocket(
            `wss://city189.cn:2960/safety-v2/ioc/pushGasData/${selectValue.value}/${eval(dialogData.value.relate_danger)[0].storageName}/${dialogData.value.company}`,
        );
    }

    // Connection opened
    //   socket.addEventListener("open", function (event) {
    //     let obj = {
    //       company: dialogData.value.company,
    //       monitorFactor: selectValue.value,
    //       storageName: eval(dialogData.value.relate_danger)[0].storageName,
    //     };
    //     // let obj = {
    //     //   company: "河北华栋化工有限责任公司",
    //     //   gasName: "液位",
    //     //   storageName: "氯甲烷储罐b",
    //     // };
    //     socket.send(JSON.stringify(obj));
    //   });
    // Listen for messages
    socket.addEventListener("message", function (event) {
        console.log(event);
        console.log(JSON.parse(event.data));
        let result = JSON.parse(event.data);
        console.log(result);
        console.log(result.standardRange);
        console.log(result.gasMonitors);
        if (result.standardRange != null) {
            downLimit = result.standardRange.downLimit;
            upLimit = result.standardRange.upLimit;
            console.log(upLimit);
        }
        xData = [];
        yData = [];
        if (result.gasMonitors && result.gasMonitors.length > 0) {
            result.gasMonitors[0].forEach((item) => {
                xData.push(item.createTime);
            });
            console.log(xData);
            for (let i = 0; i < result.gasMonitors.length; i++) {
                console.log(i);
                let middle = [];
                for (let j = 0; j < result.gasMonitors[i].length; j++) {
                    console.log(result.gasMonitors[i][j].currentValue);
                    middle.push(result.gasMonitors[i][j].currentValue);
                }
                console.log(middle);
                yData.push(middle);
            }
            console.log(yData);
            if (lineChart) {
                // setTimeout(() => {
                lineChart.dispose();
                // }, 5000)
            }
            lineChart = echarts.init(linewrap.value);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        }
    });
};
const selectInnerType = (type) => {
    // size.value =type

    innerActive.value = type;
    if (type == 2) {
        size.value = 2;
    } else {
        size.value = 1;
    }
    if (type == 1) {
        middleTableDataFirst.value = [];
        ei_danger_chemical_process({ enterprise_name: pickId.value }).then(
            (res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    totalFirst.value = res.data.data.length;
                    res.data.data.forEach((item, index) => {
                        Reflect.set(item, "num", index + 1);
                        middleTableDataFirst.value.push(item);
                    });
                    searchEiDangerChemicalProcess();
                    console.log(middleTableDataFirst.value);
                }
            },
        );
    } else if (type == 2) {
        middleTableDataSecond.value = [];
        ei_danger_chemical({ company: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalSecond.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableDataSecond.value.push(item);
                });
                searchEiDangerChemical();
                console.log(middleTableDataSecond.value);
            }
        });
    } else {
        ei_major_danger_source({ company: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                options.value = res.data.data.map((x) => {
                    return {
                        value: x.id,
                        label: x.source_name,
                    };
                });
                value.value = options.value[0].value;
                changeMajorHazard(value.value);
            }
        });
    }
};
const deviceActive = ref(1);
const selectDevice = (x) => {
    deviceActive.value = x;
    if (x == 2) {
        factor({}).then((res) => {
            list.value = res.data.data;
            selectValue.value = res.data.data[0].risk_type;
            //   initWebSocket();
            audioOption();
        });
    }
};
const tableData = [
    {
        date: "2016-05-03",
        name: "Tom",
        address: "No. 189, Grove St, Los Angeles",
    },
    {
        date: "2016-05-02",
        name: "Tom",
        address: "No. 189, Grove St, Los Angeles",
    },
    {
        date: "2016-05-04",
        name: "Tom",
        address: "No. 189, Grove St, Los Angeles",
    },
    {
        date: "2016-05-01",
        name: "Tom",
        address: "No. 189, Grove St, Los Angeles",
    },
];
const searchEiDangerChemicalProcess = () => {
    tableDataFirst.value = [];
    for (let i = 0; i < pageSizeFirst.value; i++) {
        let pageIndex = (pageNumFirst.value - 1) * pageSizeFirst.value + i;
        console.log(pageIndex);
        if (pageIndex < totalFirst.value) {
            tableDataFirst.value[i] =
                middleTableDataFirst.value[
                    (pageNumFirst.value - 1) * pageSizeFirst.value + i
                ];
        }
    }
};
const searchEiDangerChemical = () => {
    tableDataSecond.value = [];
    for (let i = 0; i < pageSizeSecond.value; i++) {
        let pageIndex = (pageNumSecond.value - 1) * pageSizeSecond.value + i;
        console.log(pageIndex);
        if (pageIndex < totalSecond.value) {
            tableDataSecond.value[i] =
                middleTableDataSecond.value[
                    (pageNumSecond.value - 1) * pageSizeSecond.value + i
                ];
        }
    }
};
const handleCurrentChangeFirst = (e) => {
    pageNumFirst.value = e;
    searchEiDangerChemicalProcess();
};
const handleCurrentChangeSecond = (e) => {
    pageNumSecond.value = e;
    searchEiDangerChemical();
};
onMounted(() => {
    middleTableDataFirst.value = [];
    ei_danger_chemical_process({ enterprise_name: pickId.value }).then(
        (res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalFirst.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableDataFirst.value.push(item);
                });
                searchEiDangerChemicalProcess();
                console.log(middleTableDataFirst.value);
            }
        },
    );
});
</script>

<style lang="less" scoped>
.two-keynote1 {
    width: 792px;
    height: 568px;
    // background-color: aqua;
    margin-top: 16px;
}
.two-keynote2 {
    width: 792px;
    height: 380px;
    // background-color: aqua;
    margin-top: 16px;
    // background-color: #47EBEB;
}
 .top-btns{
        display: flex;
        gap:8px;
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 16px;
text-align: center;

        .active-top-btn{
           
border-radius: 2px;
padding: 4px 8px;
border: 1px solid #FFC61A;
color:#FFC61A;
        }
        .top-btn{
            border-radius: 2px;
padding: 4px 8px;
border: 1px solid #80EAFF;
color:#80EAFF;
        }
    }
.technology {
    margin-top: 12px;
    .value-table {
        width: 792px;
        height: 332px;
        // margin-top: 12px;
        table {
            border-collapse: collapse;
        }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }

        .title1 {
            width: 240px;
            height: 36px;
            padding: 0 12px;
            color: #47ebeb;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            background-color: rgba(48, 171, 232, 0.1);
        }
        .value1 {
            width: 240px;
            height: 36px;
            padding: 0 12px;
            color: #fff;
            text-align: justify;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            .value {
                width: 240px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .bottom-title {
        width: 70px;
        height: 24px;
        display: flex;
        gap: 4px;
        // margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }
        .text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
    .device {
        margin-top: 8px;
        width: 792px;
        height: 164px;
        // background-color: #47EBEB;
        .text {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
}
.chemical {
    margin-top: 12px;
    .value-table {
        width: 792px;
        height: 144px;
        // margin-top: 12px;
        table {
            border-collapse: collapse;
        }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }

        .title1 {
            width: 240px;
            height: 36px;
            padding: 0 12px;
            color: #47ebeb;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            background-color: rgba(48, 171, 232, 0.1);
        }
        .value1 {
            width: 240px;
            height: 36px;
            padding: 0 12px;
            color: #fff;
            text-align: justify;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
    .bottom-title {
        width: 70px;
        height: 24px;
        display: flex;
        gap: 4px;
        margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }
        .text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
    .device {
        margin-top: 8px;
        width: 792px;
        height: 164px;
        // background-color: #47EBEB;
        .text {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
}
.alarm {
    width: 792px;
    height: 288px;
    margin-top: 12px;
    .tablebox {
        //表格四个边框的颜色
        border: 1px solid #30abe8 !important;
        th.el-table__cell {
            border: none !important;
        }
    }
    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        border-bottom: 1px solid #30abe8 !important;
        height: 36px;
        width: 108px;
        background-color: rgba(48, 171, 232, 0.1) !important;
        color: #47ebeb;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        // border-color: #30ABE8 !important;
    }
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: 1px solid #30abe8 !important;
            border-right: 1px solid #30abe8 !important;
        }
    }
    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
        // border-color: #30ABE8 !important;
    }
    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    :deep(.el-table__cell) {
        border-right: 1px solid #30abe8 !important ;
        border-bottom: 1px solid #30abe8 !important  ;
    }
    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }
    .pagination {
        margin-top: 16px;
    }
    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 2px;
        border: 1px solid #47ebeb;
        background: linear-gradient(
            180deg,
            rgba(71, 235, 235, 0) 50%,
            rgba(71, 235, 235, 0.45) 100%
        );
        color: #47ebeb;
        text-align: center;

        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 2px;
        border: 1px solid #47ebeb;
        background: linear-gradient(
            180deg,
            rgba(71, 235, 235, 0) 50%,
            rgba(71, 235, 235, 0.45) 100%
        );
        color: #47ebeb;
        text-align: center;

        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination .btn-next) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination .btn-prev) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination__total) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}
.major-hazard {
    margin-top: 12px;
    .top-select {
        width: 792px;
        height: 40px;
        // background-color: #47ebeb;
        .el-select {
            width: 122px;
        }
        :deep(.el-input__wrapper) {
            background-color: transparent;
            box-shadow: 0 0 0 0;
            border-radius: 4px;
            border: 1px solid #30abe8;
            height: 22px;
            padding: 7px 12px;
        }
        :deep(.el-input__inner) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-input__inner::placeholder) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
    .value-table {
        width: 792px;
        height: 144px;
        margin-top: 12px;
        table {
            border-collapse: collapse;
        }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }

        .title1 {
            width: 174px;
            height: 36px;
            padding: 0 12px;
            color: #47ebeb;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            background-color: rgba(48, 171, 232, 0.1);
        }
        .value1 {
            width: 174px;
            height: 36px;
            padding: 0 12px;
            color: #fff;
            text-align: justify;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            .value {
                width: 174px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .bottom-title {
        width: 70px;
        height: 24px;
        display: flex;
        gap: 4px;
        margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }
        .text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
    .device {
        margin-top: 8px;
        width: 792px;
        // height: 164px;
        // background-color: #47EBEB;
        .device-btns {
            width: 792px;
            height: 28px;
            background: url("../../../../assets/images/dialog/two-keynote-liner-device.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;

            .btns {
                width: 208px;
                height: 28px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                color: #fff;
                text-align: center;
                text-shadow: 0px 0px 8px rgba(8, 48, 69, 0.6);
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 133.333% */
                .device-active-btn {
                    width: 96px;
                    height: 28px;
                    background: url("../../../../assets/images/dialog/device-active-btn.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
                .device-btn {
                    width: 96px;
                    height: 28px;
                    background: url("../../../../assets/images/dialog/device-btn.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
            }
        }
        .real-time {
            width: 792px;
            height: 296px;
            margin-top: 12px;
            // background-color: #47EBEB;
        }
        .data-analysis {
            width: 792px;
            height: 296px;
            margin-top: 12px;
            // background-color: #47EBEB;
            .monitor-btns {
                width: 792px;
                // height: 28px;
                //   background: url("../../../../assets/images/dialog/emergency-archives-liner.svg")
                //     no-repeat center center;
                //   background-size: cover;
                //   background-position: center;
                .center {
                    width: 615px;
                    height: 22px;
                    margin: 16px auto 12px;
                    // background-color: antiquewhite;
                    display: flex;
                    justify-content: space-between;
                    .left-arrow {
                        width: 16px;
                        height: 16px;
                        margin: auto 0;
                        background: url("../../../../assets/images/left-arrow1.svg")
                            no-repeat;
                        background-size: cover;
                        background-position: center;
                    }
                    .center-content {
                        width: 551px;
                        height: 22px;
                        //   background-color: aqua;
                        .event-btn-wrap {
                            width: 87px;
                            color: #fff;
                            text-align: center;
                            font-family: Noto Sans SC;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 22px; /* 157.143% */
                        }
                        .selectActive {
                            color: #47ebeb;
                            text-align: center;
                            font-family: Noto Sans SC;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 22px; /* 157.143% */
                        }
                    }
                    .right-arrow {
                        width: 16px;
                        height: 16px;
                        margin: auto 0;
                        background: url("../../../../assets/images/right-arrow1.svg")
                            no-repeat;
                        background-size: cover;
                        background-position: center;
                    }
                }
                .linewrap {
                    width: 792px;
                    height: 230px;
                    margin-top: 16px;
                }
            }
        }
    }
    .hide {
        visibility: hidden;
    }
}
</style>
