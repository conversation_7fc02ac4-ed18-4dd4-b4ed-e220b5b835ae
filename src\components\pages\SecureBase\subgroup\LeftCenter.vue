<template>
    <CardBig title="两重点一重大">
        
        <template v-slot>
            <div class="risk-level-wrapper">
                <div class="risk-level-inner" style="cursor: pointer;" @click="openDialog('majorHazardDialogShow')">
                    <div class="risk-level-inner-item">
                        <img src="@/assets/newImages/SecureBase/risk-level-icon-1.svg" class="left-img"/>
                        <div>
                            <div class="right-text">重大危险源</div>
                            <div class="right-num">{{ obj1.record_count }}</div>
                        </div>
                    </div>
                    <div class="risk-level-inner-item">
                        <img src="@/assets/newImages/SecureBase/risk-level-icon-2.svg" class="left-img"/>
                        <div>
                            <div class="right-text">涉及企业</div>
                            <div class="right-num">{{obj1.enterprise_count}}</div>
                        </div>
                    </div>
                </div>
                <div class="risk-level-inner" style="cursor: pointer;" @click="openDialog('dangerChemicalDialogShow')">
                    <div class="risk-level-inner-item">
                        <img src="@/assets/newImages/SecureBase/risk-level-icon-3.svg" class="left-img"/>
                        <div>
                            <div class="right-text">危险化学品</div>
                            <div class="right-num">{{ obj2.record_count }}</div>
                        </div>
                    </div>
                    <div class="risk-level-inner-item">
                        <img src="@/assets/newImages/SecureBase/risk-level-icon-2.svg" class="left-img"/>
                        <div>
                            <div class="right-text">涉及企业</div>
                            <div class="right-num">{{ obj2.enterprise_count }}</div>
                        </div>
                    </div>
                </div>
                <div class="risk-level-inner" style="cursor: pointer;" @click="openDialog('craftDialogShow')">
                    <div class="risk-level-inner-item">
                        <img src="@/assets/newImages/SecureBase/risk-level-icon-4.svg" class="left-img"/>
                        <div>
                            <div class="right-text">危险化工工艺</div>
                            <div class="right-num">{{ obj3.record_count }}</div>
                        </div>
                    </div>
                    <div class="risk-level-inner-item">
                        <img src="@/assets/newImages/SecureBase/risk-level-icon-2.svg" class="left-img"/>
                        <div>
                            <div class="right-text">涉及企业</div>
                            <div class="right-num">{{ obj3.enterprise_count }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </CardBig>
</template>

<script setup>
import CardBig from "@/components/commenNew/CardBig.vue"
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance  } from "vue";
import { major_danger } from "@/assets/js/api/secureBase";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val) => {
    proxy.$emit('openDialog',val);
}
//重大危险源
let obj1 = ref({
    record_count: 0,
    enterprise_count: 0,
})
//危险化学品
let obj2 = ref({
    record_count: 0,
    enterprise_count: 0,
})
//危险化工工艺
let obj3 = ref({
    record_count: 0,
    enterprise_count: 0,
})
const getData = (val) => {
    major_danger({}).then(res =>{
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            let result = res.data.data;
            obj1.value = result.find(item => item.table_name === 'major_danger_source');
            obj2.value = result.find(item => item.table_name === 'danger_chemical');
            obj3.value = result.find(item => item.table_name === 'danger_chemical_process');
        }
    })
}
onMounted(() => {
    getData();
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>

    .risk-level-wrapper{
        width: 416px;
    height: 272px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
   .risk-level-inner{
    width: 416px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    .risk-level-inner-item{
        width: 176px;
height: 56px;
padding: 12px;
background: url("@/assets/newImages/SecureBase/risk-level-wrapper.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
        display: flex;
        gap: 8px;
        .left-img{
            width: 56px;
            height: 56px;

        }
        .right-text{
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
text-align: left;
color: rgba(255, 255, 255, 1);
        }
        .right-num{
            font-family: DINPro;
font-weight: 500;
font-size: 24px;
line-height: 32px;
text-align: left;
color: rgba(255, 255, 255, 1);

        }
    }
   }
    }
</style>
