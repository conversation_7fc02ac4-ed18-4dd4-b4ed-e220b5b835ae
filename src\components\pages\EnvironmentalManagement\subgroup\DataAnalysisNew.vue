<template>
    <div class="data-analysis">
        <div class="left">
            <div class="left-inner-one">
                <div class="title">危废种类</div>
                <div class="num">{{ wasteType }}</div>
                <div class="unit">种</div>
            </div>
            <div class="left-inner-two">
                <div class="title">贮存量</div>
                <div class="num">{{ storageCapacity }}</div>
                <div class="unit">吨</div>
            </div>
        </div>
        <div class="liner"></div>
        <div class="right">
            <div class="title">危废产生量排行</div>
            <div class="progress" v-show="!nonFlag">
                <div
                    class="progress-box"
                    v-for="(item, index) in peopleData"
                    :key="index"
                >
                    <div class="title-box">
                        <div class="title-box-left">
                            <div class="ranking">NO.{{ index + 1 }}</div>
                            <div class="name" :title="item.name">
                                &nbsp;{{ item.name }}
                            </div>
                        </div>
                        <div class="title-box-right">
                            <div class="num">{{ item.value }}&nbsp;</div>
                            <div class="unit">吨</div>
                        </div>
                    </div>
                    <el-progress
                        class="progress-height"
                        :stroke-width="16"
                        :percentage="item.percentage"
                    ></el-progress>
                </div>
            </div>
            <div class="all-wrapper" v-show="nonFlag">
                <div class="img-no">暂无数据</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import {
    hazardous_waste_total,
    hazardous_waste_storage,
    hazardous_waste_rank,
} from "../../../../assets/js/api/environmentalManagement";
const wasteType = ref(0);
const storageCapacity = ref(0);
const peopleData = ref([]);
//暂无数据标志
const nonFlag = ref(true);
onMounted(() => {
    hazardous_waste_total().then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            wasteType.value = res.data.data[0].total_type;
        }
    });
    hazardous_waste_storage().then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            storageCapacity.value = res.data.data[0].total_storage;
        }
    });
    peopleData.value = [];
    hazardous_waste_rank().then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            nonFlag.value = false;

            let max = Number(res.data.data[0].total_storage);
            peopleData.value = res.data.data.map((x) => {
                return {
                    name: x.type,
                    value: x.total_storage,
                    percentage: (Number(x.total_storage) / max) * 100,
                };
            });
        } else {
            nonFlag.value = true;
        }
    });
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.data-analysis {
    width: 416px;
    height: 196px;
    // background-color: aqua;
    display: flex;
    gap: 12px;
    .left {
        width: 96px;
        height: 196px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .left-inner-one {
            width: 96px;
            height: 106px;
            background: url("../../../../assets/images/card/waste-type.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            .title {
                color: #ff791a;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
            }
        }
        .left-inner-two {
            width: 96px;
            height: 106px;
            // margin-top: 12px;
            background: url("../../../../assets/images/card/storage-capacity.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            .title {
                color: #30abe8;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
            }
        }
        .num {
            margin-top: 8px;
            color: #fff;
            text-align: center;
            font-family: DINPro;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
        }
        .unit {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
    .liner {
        width: 2px;
        height: 196px;
        background: url("../../../../assets/images/card/liner-long.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
    }
    .right {
        width: 278px;
        height: 196px;
        .title {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .all-wrapper {
            width: 278px;
            height: 160px;
            display: flex;
            align-items: center;
            justify-content: center;
            .img-no {
                margin: auto 0;
                color: #fff;
                text-align: center;
                font-family: "Noto Sans SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }
        }
        .progress {
            width: 278px;
            height: 160px;
            margin-top: 8px;
            .progress-box {
                width: 278px;
                height: 33px;
            }
            .title-box {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                display: flex;
                justify-content: space-between;
                .title-box-left {
                    // background-color: #30ABE8;
                    display: flex;
                    .name {
                        width: 145px;
                        // background-color: #30ABE8;

                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                    .ranking {
                        color: #30abe8;
                        font-family: Noto Sans SC;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 22px; /* 157.143% */
                    }
                }
                .title-box-right {
                    display: flex;
                    .num {
                        color: #fff;
                        text-align: right;
                        font-family: Noto Sans SC;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 22px; /* 157.143% */
                    }
                    .unit {
                        color: #fff;
                        text-align: right;
                        font-family: Noto Sans SC;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 150% */
                    }
                }
            }

            ::v-deep .el-progress-bar__inner {
                background: linear-gradient(
                    270deg,
                    #30abe8 0%,
                    rgba(48, 171, 232, 0.15) 100%
                );
                height: 6px;
                margin: 2px;
                border-image-source: linear-gradient(
                    270deg,
                    #30abe8 0%,
                    rgba(48, 171, 232, 0.15) 100%
                );
                border-radius: 6px;
            }
            ::v-deep .el-progress {
                height: 10px;
            }
            ::v-deep .el-progress-bar__outer {
                width: 278px;
                height: 10px !important;
                background-color: rgba(255, 255, 255, 0);
                border: 1px solid #30abe8;
                border-radius: 6px;
            }
            ::v-deep .el-progress__text {
                display: none;
            }
            .progress-height {
                height: 10px;
            }
        }
    }
}
</style>
