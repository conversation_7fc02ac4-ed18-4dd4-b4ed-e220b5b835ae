<svg width="120" height="28" viewBox="0 0 120 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" y="0.5" width="119" height="27" fill="#05202E" fill-opacity="0.3"/>
<rect x="0.5" y="0.5" width="119" height="27" fill="url(#paint0_linear_46_16)"/>
<rect x="0.5" y="0.5" width="119" height="27" stroke="url(#paint1_linear_46_16)"/>
<defs>
<linearGradient id="paint0_linear_46_16" x1="0" y1="28" x2="120" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC61A" stop-opacity="0"/>
<stop offset="0.510417" stop-color="#FFC61A" stop-opacity="0.6"/>
<stop offset="1" stop-color="#FFC61A" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_46_16" x1="60" y1="0" x2="60" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC61A" stop-opacity="0"/>
<stop offset="0.5" stop-color="#FFC61A"/>
<stop offset="1" stop-color="#FFC61A" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
