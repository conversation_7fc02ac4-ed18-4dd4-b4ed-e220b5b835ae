<template>
<div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">视频详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    
                   <div class="container">
            <div class="bottom-title">
                <div class="icon"></div>
                <div class="text">设备信息</div>
            </div>
            <div class="details">
                <el-row :gutter="10">
                    <el-col :span="12" class="innner special">
                        <div class="headline">设备名称：</div>
                        <div class="content" :title="dialogData.equipment_name">
                            {{ dialogData.equipment_name }}
                        </div>
                    </el-col>
                    <el-col :span="12" class="innner special">
                        <div class="headline">设备编号：</div>
                        <div class="content" :title="dialogData.equipment_id">
                            {{ dialogData.equipment_id }}
                        </div>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12" class="innner">
                        <div class="headline">设备状态：</div>
                        <div class="content">
                            {{
                                dialogData.equipment_status == 1
                                    ? "在线"
                                    : "离线"
                            }}
                        </div>
                    </el-col>
                    <el-col :span="12" class="innner">
                        <div class="headline">设备位置：</div>
                        <div class="content">
                            {{ dialogData.longitude }}，{{
                                dialogData.latitude
                            }}
                        </div>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12" class="innner">
                        <div class="headline">购买日期：</div>
                        <div class="content">{{ dialogData.buy_date }}</div>
                    </el-col>
                    <el-col :span="12" class="innner">
                        <div class="headline">安装日期：</div>
                        <div class="content">{{ dialogData.install_date }}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12" class="innner">
                        <div class="headline">维保开始日期：</div>
                        <div class="content">
                            {{ dialogData.maintenance_start_date }}
                        </div>
                    </el-col>
                    <el-col :span="12" class="innner">
                        <div class="headline">维保结束日期：</div>
                        <div class="content">
                            {{ dialogData.maintenance_finish_date }}
                        </div>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12" class="innner">
                        <div class="headline">设备厂商：</div>
                        <div class="content">{{ dialogData.vendor_name }}</div>
                    </el-col>
                </el-row>
            </div>
            <div class="video">
                <div ref="img" id="imgCCC" class="img">
                    <div v-if="videoGrey" class="img-no">暂无数据</div>
                </div>
            </div>
        </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
    
</template>

<script setup>
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
import {
    getEquipmentInfo,
    getFlv,
    tank_farm_info,
} from "../../../../assets/js/api/safetySupervision";
import { nj_equipment_info } from "../../../../assets/js/api/parkArchives";
const { proxy } = getCurrentInstance();
var jessibuca = null;
const img = ref(null);
const clickId = ref();
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const dialogData = ref({
    equipment_name: "",
    equipment_id: "",
    equipment_status: "",
    longitude: "",
    latitude: "",
    maintenance_finish_date: "",
    maintenance_start_date: "",
    install_date: "",
    buy_date: "",
    vendor_name: "",
});
const videoGrey = ref(false);
watch(
    pickId,
    (a, b) => {
        console.log(a, b);

        clickId.value = a;
        if (a != undefined) {
            nj_equipment_info({ id: a }).then((res1) => {
                if (
                    res1.data.success &&
                    res1.data.data &&
                    res1.data.data.length
                ) {
                    let result = res1.data.data[0];
                    dialogData.value = res1.data.data[0];
                    if (
                        result.equipment_id == null ||
                        result.equipment_id == "离线" ||
                        result.equipment_id == undefined
                    ) {
                        videoGrey.value = true;
                    }
                    getFlv({
                        equipmentIdList: [result.equipment_id],
                    }).then((res3) => {
                        // hazardDialog.value = true
                        // myDiv.style.display = "block";
                        jessibuca = new JessibucaPro({
                            container: img.value,
                            videoBuffer: 0.2, // 缓存时长
                            isResize: false,
                            decoder: "/Jessibuca/decoder-pro.js",
                            text: "",
                            loadingText: "加载中",
                            showBandwidth: true, // 显示网速
                            operateBtns: {
                                fullscreen: false,
                                screenshot: false,
                                play: false,
                                audio: false,
                                performance: false,
                            },
                            forceNoOffscreen: true,
                            isNotMute: false,
                            heartTimeout: 10,
                            ptzClickType: "mouseDownAndUp",
                        });
                        console.log(res3.data[0].flvAddress);
                        if (res3.data[0].flvAddress)
                            jessibuca.play(
                                proxy.$changeUrl(res3.data[0].flvAddress),
                            );
                    });
                }
            });
        }
    },
    {
        immediate: true,
    },
);
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
const emit = defineEmits(["closeDialog"]);
const typeActive = ref(1);
const closeDialog = () => {
    destroy();
    emit("closeDialog", "video");
};
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 760px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 760px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 644px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.container {
    
    .bottom-title {
        width: 70px;
        height: 24px;
        display: flex;
        gap: 4px;
        // margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }
        .text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
    .details {
        margin-top: 12px;
        width: 792px;
        height: 169px;
        // background-color: #47ebeb;
        .innner {
            display: flex;
            margin-top: 8px;

            .headline {
                width: 140px;
                color: #47ebeb;
                text-align: right;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }
            .content {
                width: 251px;
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
        .special {
            margin-top: 0px;
        }
    }
    .video {
        width: 792px;
        height: 444px;
        flex-shrink: 0;
        background: rgba(48, 171, 232, 0.15);
        // margin-top: 17px;
        .img {
            width: 792px;
            height: 444px;
            margin-top: 16px;
            background: rgba(48, 171, 232, 0.15);
            display: flex;
            justify-content: center;
            .img-no {
                margin: auto 0;
                color: #fff;
                text-align: center;
                font-family: "Noto Sans SC";
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 140% */
            }
        }
    }
}
</style>
