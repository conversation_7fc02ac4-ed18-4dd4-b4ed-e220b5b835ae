import { fetch } from "../request";
//设备总数
export const njdevice = (params) =>
    fetch("/api-v2/njdevice", { ...params }, "POST");
//设备数量
export const njdevice_type = (params) =>
    fetch("/api-v2/njdevice_type", { ...params }, "POST");
//企业概况
export const njenterprise_state = (params) =>
    fetch("/api-v2/njenterprise_state", { ...params }, "POST");
//车辆统计
export const cars_today = (params) =>
    fetch("/api-v2/cars_today", { ...params }, "POST");
//大门列表
export const gate_video = (params) =>
    fetch("/api-v2/gate_video", { ...params }, "POST");
//新-大门列表
export const nj_iocGateC = (params) =>
    fetch("/api-v2/nj_iocGateC", { ...params }, "POST");
//电子围栏-全部数据
export const electronic_select_all = (params) =>
    fetch("/information/electronic-fence/select-all", { ...params }, "GET");
//电子围栏-新增
export const electronic_add = (params) =>
    fetch("/information/electronic-fence/add", { ...params }, "POST");
//电子围栏-修改
export const electronic_update = (params) =>
    fetch("/information/electronic-fence/update", { ...params }, "POST");
//实时告警
export const security_alarms = (params) =>
    fetch("/api-v2/security_alarms", { ...params }, "POST");
//区域列表
export const listAreas = (params) =>
    fetch("/security/carAccessType/listAreas", { ...params }, "GET");
//区域通行记录
export const getPassRecord = (params) =>
    fetch("/security/carPassRecord/getPassRecord ", { ...params }, "POST");
//设备类型
export const select = (params) =>
    fetch("/equipment/type/select ", { ...params }, "GET");
//区域列表
export const getChildren = (params) =>
    fetch("/equipment/maintain-standing-book/getArea", { ...params }, "GET");
//设备清单
export const selectDevice = (params) =>
    fetch(
        "/equipment/maintain-standing-book/select-page",
        { ...params },
        "POST",
    );
//设备清单
export const selectAllClosed = (params) =>
    fetch("/equipment/maintain-standing-book/selectAll", { ...params }, "POST");
//设备清单
// export const selectAllEnterprise = params => fetch('/equipment/maintain-standing-book/selectByQuery', { ...params }, 'GET')
export const selectAllEnterprise = (params) =>
    fetch(
        "/api-v2/maintain-standing-book/selectByQuery",
        { ...params },
        "POST",
    );

//封闭化点位
export const carGpsPoint = (params) =>
    fetch("/security/carGpsLink/carGpsPoint", { ...params }, "GET");
//告警点位
export const alarmInfoPersonRecord = (params) =>
    fetch("/security/alarmInfo/record", { ...params }, "POST");
//车辆信息
export const carGpsLine = (params) =>
    fetch("/security/carGpsLink/carGpsLine", { ...params }, "GET");
//车辆信息
export const getDictByType = (params) =>
    fetch("/security/dict/getDictByType", { ...params }, "GET");
//人员点位
// export const cameraPersonLink = (params) =>
//     fetch("/security/ioc/cameraPersonLink/listIoc", { ...params }, "GET");
export const cameraPersonLink = (params) =>
    fetch("/security/ioc/alarm/person/listIoc", { ...params }, "GET");
//根据时间段获取车牌号
export const gpsCarPlate = (params) =>
    fetch("/api-v2/gpsCarPlate", { ...params }, "POST");
//时间车牌号获取路线
export const gpsLineByTimeCarPlate = (params) =>
    fetch("/api-v2/gpsLineByTimeCarPlate", { ...params }, "POST");
//根据时间段获取车牌号
export const se_gpsCarPlate = (params) =>
    fetch("/security/ioc/gpsCarPlate", { ...params }, "POST");
//时间车牌号获取路线
export const se_gpsLineByTimeCarPlate = (params) =>
    fetch("/security/ioc/gpsLineByCarPlate  ", { ...params }, "POST");
//获取人员离岗告警信息
export const findAlarm = (params) =>
    fetch("/security/ioc/alarm/person/findAlarm", { ...params }, "GET");
//获取告警信息
export const unified_alarms = (params) =>
    fetch("/api-v2/unified_alarms", { ...params }, "POST");
//获取告警信息
export const detail = (params) =>
    fetch("/security/alarmInfoPerson/detail", { ...params }, "GET");
