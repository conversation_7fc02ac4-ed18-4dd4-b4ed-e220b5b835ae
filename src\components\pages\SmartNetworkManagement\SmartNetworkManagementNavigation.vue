<template>
    <div class="smart-network-management-navigation" v-if="showFlag == 2">
        <div :class="active === 1 ? 'active-btn' : 'btn'" @click="setPage(1)">
            电力管
        </div>
        <div :class="active === 2 ? 'active-btn' : 'btn'" @click="setPage(2)">
            燃气管
        </div>
        <div :class="active === 3 ? 'active-btn' : 'btn'" @click="setPage(3)">
            热力管
        </div>
        <div :class="active === 4 ? 'active-btn' : 'btn'" @click="setPage(4)">
            给水管
        </div>
        <div :class="active === 5 ? 'active-btn' : 'btn'" @click="setPage(5)">
            雨水管
        </div>
        <div :class="active === 6 ? 'active-btn' : 'btn'" @click="setPage(6)">
            污水管
        </div>
        <div :class="active === 7 ? 'active-btn' : 'btn'" @click="setPage(7)">
            中水管
        </div>
        <div :class="active === 8 ? 'active-btn' : 'btn'" @click="setPage(8)">
            电信电缆
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import * as Cesium from "cesium";

const active = ref(0);
const setPage = (type) => {
    if (active.value == type) {
        active.value = 0;
        window.map1.clearMap();
        window.viewer.entities.removeAll(); //删除所有
        if (window.mouseTool) {
            window.mouseTool.close(true); //关闭，并清除覆盖物
        }
        if (window.heatmap != null) {
            window.heatmap.setMap(null);
        }
    } else {
        active.value = type;
        window.map1.clearMap();
        window.viewer.entities.removeAll(); //删除所有
        if (window.mouseTool) {
            window.mouseTool.close(true); //关闭，并清除覆盖物
        }
        initBoundary();
        if (type == 1) {
            if (window.toggle == 2) {
                console.log(1111111111111);
                electricPipeRed.value.forEach((item, index) => {
                    console.log(item);
                    // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                    const entity = viewer.entities.add({
                        id: "red" + index,
                        name: "线",
                        show: true,
                        polyline: {
                            positions:
                                Cesium.Cartesian3.fromDegreesArrayHeights(item),
                            width: 3,
                            material: new Cesium.PolylineDashMaterialProperty({
                                color: Cesium.Color.RED,
                                dashLength: 20, //短划线长度
                            }),
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        },
                    });
                });
                electricPipeGreen.value.forEach((item, index) => {
                    console.log(item);
                    // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                    let a = "green" + index;
                    console.log(a);
                    const entity = viewer.entities.add({
                        id: a,
                        name: "线",
                        show: true,
                        polyline: {
                            positions:
                                Cesium.Cartesian3.fromDegreesArrayHeights(item),
                            width: 3,
                            material: new Cesium.PolylineDashMaterialProperty({
                                color: Cesium.Color.GREEN,
                                dashLength: 20, //短划线长度
                            }),
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        },
                    });
                });
                // viewer.zoomTo(entity)
            }
        } else if (type == 2) {
            gasPipe.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "red" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.PURPLE,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        } else if (type == 3) {
            heatPipeBlue.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "blue" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.BLUE,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
            heatPipeRed.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "red" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.RED,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        } else if (type == 4) {
            WaterSupplyPipe.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "green" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.YELLOW,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        } else if (type == 5) {
            stormSewer.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "red" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.PURPLE,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        } else if (type == 6) {
            sewagePipeRed.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "red" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.RED,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
            sewagePipeBlack.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "black" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.ORANGE,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        } else if (type == 7) {
            intermediateWaterPipe.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "black" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.ORANGE,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        } else if (type == 8) {
            cableConduit.value.forEach((item, index) => {
                console.log(item);
                // const path = [115.138752,37.606265,30,115.170926,37.605995,30];
                const entity = viewer.entities.add({
                    id: "green" + index,
                    name: "线",
                    show: true,
                    polyline: {
                        positions:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item),
                        width: 3,
                        material: new Cesium.PolylineDashMaterialProperty({
                            color: Cesium.Color.PINK,
                            dashLength: 20, //短划线长度
                        }),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    },
                });
            });
        }
    }
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
const showFlag = ref(1);
onMounted(() => {
    showFlag.value = window.toggle;
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
});
//经度小数点第三位减6，纬度小数点第四位减7
const electricPipe = ref([
    [115.132752, 37.605565],
    [115.146439, 37.605518],
    [115.164926, 37.605295], //纬一路
    [115.133372, 37.593907],
    [115.146091, 37.593697],
    [115.163178, 37.593377], //纬二路

    [115.133863, 37.587655],
    [115.145875, 37.587655],
    [115.162403, 37.587655], //工业路

    [115.134733, 37.583151],
    [115.145684, 37.583151],
    [115.161475, 37.583151], //纬三路

    [115.135765, 37.577326],
    [115.145605, 37.577326],
    [115.153064, 37.577326], //纬四路

    [115.135765, 37.574157],
    [115.145605, 37.574157],
    [115.153064, 37.574157], //纬五路
    [115.153064, 37.583151], //纬三经七

    [115.146356, 37.610561], //郑昔线中间
    [115.146356, 37.608838], //郑昔线中间偏下

    [115.132254, 37.611152], //郑昔线左
    //特殊点位
    [115.137749, 37.60583], //1
    [115.153551, 37.60568], //2
    [115.137654, 37.600507], //3
    [115.139357, 37.600507], //3右侧点
    [115.15374, 37.600912], //4
    [115.139357, 37.59376], //5
    [115.153456, 37.593685], //6
    [115.138979, 37.587761], //7
    [115.153456, 37.587686], //8
    [115.139168, 37.583151], //9
    [115.153267, 37.582543], //10
    [115.132254, 37.593697], //纬二偏左
    [115.160303, 37.593697], //纬二偏右
]);
const eleData = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30],
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30],
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30],
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //横向
    [115.146439, 37.605518, 30, 115.145605, 37.574157, 30], //竖向中间
    [115.132752, 37.605565, 30, 115.134733, 37.583151, 30],
    [115.134733, 37.583151, 30, 115.135765, 37.574157, 30], //竖向左
    [115.164926, 37.605295, 30, 115.161475, 37.583151, 30],
    [115.153064, 37.583151, 30, 115.153064, 37.574157, 30],
]);
//电信电缆
const cableConduit = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //纬二
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
    [115.146356, 37.610561, 30, 115.145605, 37.574157, 30], //竖中间
    [115.137749, 37.60583, 30, 115.137654, 37.600507, 30], //1,3
    [115.139357, 37.600507, 30, 115.139168, 37.583151, 30], //3右侧点,9
    [115.153551, 37.60568, 30, 115.153064, 37.574157, 30], //2,纬五
]);
//中水管
const intermediateWaterPipe = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //纬二
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
    [115.146356, 37.608838, 30, 115.145605, 37.574157, 30], //竖中间
]);
//污水管
const sewagePipeRed = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //纬二
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.146356, 37.608838, 30, 115.145605, 37.574157, 30], //竖中间
]);
const sewagePipeBlack = ref([
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
]);
const WaterSupplyPipe = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //纬二
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
    [115.137749, 37.60583, 30, 115.137654, 37.600507, 30], //1,3
    [115.139357, 37.600507, 30, 115.139168, 37.583151, 30], //3右侧点,9
    [115.153551, 37.60568, 30, 115.153064, 37.574157, 30], //2,纬五
    [115.146356, 37.608838, 30, 115.145605, 37.574157, 30], //竖中间
    [115.132254, 37.611152, 30, 115.134733, 37.583151, 30],
    [115.134733, 37.583151, 30, 115.135765, 37.574157, 30], //竖向左
    [115.164926, 37.605295, 30, 115.161475, 37.583151, 30], //竖向右侧
]);
const heatPipeBlue = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.137749, 37.60583, 30, 115.137654, 37.600507, 30], //1,3
    // [115.153551, 37.60568, 30, 115.15374, 37.600912, 30],//2,4
    // [115.137654, 37.600507, 30, 115.139357, 37.59376, 30],//3,5
    // [115.15374, 37.600912, 30, 115.153456, 37.593685, 30],//4,6
    [115.132254, 37.593697, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.160303, 37.593697, 30], //纬二
    // [115.139357, 37.59376, 30, 115.138979, 37.587761, 30],//5,7
    // [115.153456, 37.593685, 30, 115.153456, 37.587686, 30],//6,8
    [115.139357, 37.600507, 30, 115.139168, 37.583151, 30], //3右侧点,9
    [115.153551, 37.60568, 30, 115.153064, 37.574157, 30], //2,纬五
    [115.146356, 37.610561, 30, 115.145605, 37.574157, 30], //竖中间
    [115.132254, 37.611152, 30, 115.134733, 37.583151, 30],
    [115.134733, 37.583151, 30, 115.135765, 37.574157, 30], //竖向左
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
]);
const heatPipeRed = ref([
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
]);
const electricPipeRed = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30], //横一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30], //横二
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //横三
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //横四
    [115.132752, 37.605565, 30, 115.134733, 37.583151, 30],
    [115.134733, 37.583151, 30, 115.135765, 37.574157, 30], //竖向左
    [115.146091, 37.593697, 30, 115.145605, 37.574157, 30], //竖向中间
    // [115.145684, 37.583151, 30, 115.161475, 37.583151, 30],//竖向右上
    [115.153064, 37.583151, 30, 115.153064, 37.574157, 30],
    [115.163178, 37.593377, 30, 115.161475, 37.583151, 30], //竖向右上
]);
const electricPipeGreen = ref([
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //横一
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //横二
    [115.146439, 37.605518, 30, 115.145684, 37.583151, 30], //竖向中间
    [115.164926, 37.605295, 30, 115.163178, 37.593377, 30],
]);

const gasPipe = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //纬二
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
    [115.146356, 37.610561, 30, 115.145605, 37.574157, 30],
]);
const stormSewer = ref([
    [115.132752, 37.605565, 30, 115.146439, 37.605518, 30],
    [115.146439, 37.605518, 30, 115.164926, 37.605295, 30], //纬一
    [115.133372, 37.593907, 30, 115.146091, 37.593697, 30],
    [115.146091, 37.593697, 30, 115.163178, 37.593377, 30], //纬二
    [115.133863, 37.587655, 30, 115.145875, 37.587655, 30],
    [115.145875, 37.587655, 30, 115.162403, 37.587655, 30], //工业路
    [115.134733, 37.583151, 30, 115.145684, 37.583151, 30],
    [115.145684, 37.583151, 30, 115.161475, 37.583151, 30], //纬三
    [115.135765, 37.577326, 30, 115.145605, 37.577326, 30],
    [115.145605, 37.577326, 30, 115.153064, 37.577326, 30], //纬四
    [115.135765, 37.574157, 30, 115.145605, 37.574157, 30],
    [115.145605, 37.574157, 30, 115.153064, 37.574157, 30], //纬五
    [115.146356, 37.6095395, 30, 115.145605, 37.572157, 30], //竖向
]);
</script>

<style lang="less" scoped>
.smart-network-management-navigation {
    width: 130px;
    height: 92px;
    z-index: 1000;
    position: absolute;
    top: 528px;
    left: 496px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 157.143% */
    text-align: center;

    > div:not(:first-child) {
        margin-top: 24px;
    }
    .btn {
        width: 130px;
        height: 32px;
        background: url("../../../assets/images/jump-btn/self-nav.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }
    .active-btn {
        width: 130px;
        height: 32px;
        background: url("../../../assets/images/jump-btn/self-nav-active.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }
}
</style>
