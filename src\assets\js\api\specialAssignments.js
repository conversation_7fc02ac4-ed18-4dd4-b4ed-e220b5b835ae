import { fetch } from "../request";
//特殊作业行业类别
export const type = (params) =>fetch("/api-v2/special/type", { ...params }, "POST");
//特殊作业趋势
export const trend = (params) =>fetch("/safety-v2/special-work/new-trend", { ...params }, "GET");
//重点关注特殊作业
export const focus = (params) =>fetch("/api-v2/special/focus", { ...params }, "POST");
//特殊作业分类（当日）
export const count = (params) =>fetch("/api-v2/special/count", { ...params }, "POST");
//特殊作业报备分页查询
export const getSpecialWork = (params) =>fetch("/safety-v2/special-work/select-page", { ...params }, "GET");
//特殊作业类型
export const getWorkType = (params) => fetch("/safety-v2/special-work/work-type", { ...params }, "GET");
//特殊作业报备详情
export const specialWorkDetail = (params) => fetch(`/safety-v2/special-work/select-detail/${params}`, {}, "GET");
//抽查检查统计
export const check = (params) =>fetch("/api-v2/special-work/check", { ...params }, "POST");
//企业作业报备排名
export const company_trend = (params) =>fetch("/api-v2/special-work/company-trend", { ...params }, "POST");
//作业类型排名
export const type_trend = (params) =>fetch("/api-v2/special-work/type-trend", { ...params }, "POST");
//特殊作业报备趋势
export const report_trend = (params) =>fetch("/safety-v2/special-work/report-trend", { ...params }, "GET");
//特殊作业总览
export const overview = (params) =>fetch("/api-v2/special-work/overview", { ...params }, "POST");
//特殊作业总览--底部
export const overview_sum = (params) =>fetch("/api-v2/special-work/sum", { ...params }, "POST");
