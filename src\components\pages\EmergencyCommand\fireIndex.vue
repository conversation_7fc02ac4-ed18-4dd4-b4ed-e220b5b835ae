<template>
    <div>
        <div class="command_1" v-if="reporttype">
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                事故报告
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click="reportcloseDialog"
                />
            </div>
            <div class="command_content">
                <div class="content1_title">
                    <div class="line_box"></div>
                    <div>基本信息</div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">事件名称:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.eventName }}
                        </div>
                    </div>
                    <div class="command_hang1">
                        <div class="command_hang1_title">上报人:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.eventLabel }}
                        </div>
                    </div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">联系电话:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.contactNumber }}
                        </div>
                    </div>
                </div>

                <div class="content1_title">
                    <div class="line_box"></div>
                    <div>事件详情</div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">事件描述:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.eventDescription }}
                        </div>
                    </div>
                    <div class="command_hang1">
                        <div class="command_hang1_title">发生时间:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.occurrenceTime }}
                        </div>
                    </div>
                </div>
                <div class="command_hang">
                    <!--        <div class="command_hang1">-->
                    <!--          <div class="command_hang1_title">事件附件:</div>-->
                    <!--          <div class="command_hang1_contet">-->
                    <!--            {{ fireEvent.longitude }},{{ fireEvent.latitude }}-->
                    <!--          </div>-->
                    <!--        </div>-->
                </div>
                <div class="content1_title">
                    <div class="line_box"></div>
                    事件报告
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">事件类型:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.eventTypeName }}
                        </div>
                    </div>
                    <div class="command_hang1">
                        <div class="command_hang1_title">事件等级:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.eventLevel }}
                        </div>
                    </div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">经济损失:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.economicLoss }}
                        </div>
                    </div>
                    <div class="command_hang1">
                        <div class="command_hang1_title">受灾面积:</div>
                        <div class="command_hang1_contet">
                            {{
                                reporList.disasterArea
                                    ? reporList.disasterArea + "公顷"
                                    : ""
                            }}
                        </div>
                    </div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">死亡人数:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.deathNumber }}
                        </div>
                    </div>
                    <div class="command_hang1">
                        <div class="command_hang1_title">受伤人数:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.injuredNumber }}
                        </div>
                    </div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">失踪人数:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.missingNumber }}
                        </div>
                    </div>
                    <div class="command_hang1">
                        <div class="command_hang1_title">受困人数:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.trappedNumber }}
                        </div>
                    </div>
                </div>
                <div class="command_hang">
                    <div class="command_hang1">
                        <div class="command_hang1_title">事件标签:</div>
                        <div class="command_hang1_contet">
                            {{ reporList.eventLabel }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="impactDialog" v-if="impact">
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                影响因子
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click.stop="closeDialogImpact"
                />
            </div>
            <div class="impact_content">
                <el-form
                    ref="formImpact1"
                    :model="formImpact"
                    :rules="rulesImpact"
                >
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="分析类型" prop="analysisType">
                                <el-select
                                    v-model="formImpact.analysisType"
                                    placeholder="请选择分析类型"
                                    :popper-append-to-body="false"
                                    popper-class="select-popper"
                                    @change="selectType"
                                >
                                    <el-option
                                        v-for="dict in optionsList"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="dict.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="温度(T)" prop="temperature">
                                <el-input
                                    v-model="formImpact.temperature"
                                    placeholder="请输入温度"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="湿度(h)" prop="humidity">
                                <el-input
                                    v-model="formImpact.humidity"
                                    placeholder="请输入湿度"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="风级(W)" prop="windScale">
                                <el-input
                                    v-model="formImpact.windScale"
                                    placeholder="请输入风级"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="风速(m/min)" prop="windSpeed">
                                <el-input
                                    v-model="formImpact.windSpeed"
                                    placeholder="请输入风速"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="地形坡度(W)" prop="slope">
                                <el-input
                                    v-model="formImpact.slope"
                                    placeholder="请输入小于等于60的地形坡度"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="风向" prop="windDirection">
                                <el-select
                                    v-model="formImpact.windDirection"
                                    placeholder="请选择风向"
                                    style="width: 10vw"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in dicts.fire_wind"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 1">
                            <el-form-item label="时间" prop="time">
                                <el-input
                                    v-model="formImpact.time"
                                    placeholder="请输入时间"
                                >
                                    <template #suffix>
                                        <div class="text">min</div>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 2">
                            <el-form-item
                                label="爆炸物料总质量(Wf)"
                                prop="weight"
                            >
                                <el-input
                                    v-model="formImpact.weight"
                                    placeholder="爆炸物料总质量"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 2">
                            <el-form-item label="爆炸物料名称" prop="kd">
                                <el-select
                                    filterable
                                    class="text-case-type-select"
                                    v-model="formImpact.matterId"
                                    placeholder="请选择爆炸物料名称"
                                >
                                    <el-option
                                        v-for="dict in materialNameList"
                                        :key="dict.id"
                                        :label="dict.name"
                                        :value="dict.id"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 3">
                            <el-form-item label="容积(m³)" prop="weight">
                                <el-input
                                    v-model="formImpact.weight"
                                    placeholder="请输入小于10000的容积"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 3">
                            <el-form-item label="温度(T)" prop="temperature">
                                <el-input
                                    v-model="formImpact.temperature"
                                    placeholder="请输入温度"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 3">
                            <el-form-item label="物料名称" prop="matterId">
                                <el-select
                                    filterable
                                    v-model="formImpact.matterId"
                                    placeholder="请选择物料名称"
                                >
                                    <el-option
                                        v-for="dict in materialNameListone"
                                        :key="dict.id"
                                        :label="dict.name"
                                        :value="dict.id"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 3">
                            <el-form-item label="风向" prop="windDirection">
                                <el-select
                                    v-model="formImpact.windDirection"
                                    placeholder="请选择风向"
                                    style="width: 10vw"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in dicts.fire_wind"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 3">
                            <el-form-item label="风速(m/min)" prop="windSpeed">
                                <el-input
                                    v-model="formImpact.windSpeed"
                                    placeholder="请输入风速"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formImpact.analysisType == 3">
                            <el-form-item label="时间" prop="time">
                                <el-input
                                    v-model="formImpact.time"
                                    placeholder="请输入时间"
                                    ><template #suffix>
                                        <div class="text">min</div>
                                    </template></el-input
                                >
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <div class="impactButton">
                    <div
                        class="impactbutton_little_1"
                        @click="
                            impact = false;
                            $loading.hide();
                        "
                    >
                        取消
                    </div>
                    <div
                        class="impactbutton_little"
                        @click="submitImpact(formImpact1)"
                    >
                        确定
                    </div>
                </div>
            </div>
        </div>
        <div class="mitigateDialog" v-if="mitigate">
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                疏解人员
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click.stop="closeDialogMitigate"
                />
            </div>
            <div class="impact_content mitigate_content">
                <el-form
                    ref="formMitigate1"
                    :model="formMitigate"
                    :rules="rulesMitigate"
                >
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="疏解人数" prop="personCount">
                                <el-input
                                    v-model="formMitigate.personCount"
                                    placeholder="请输入疏解人数"
                                    ><template #suffix>
                                        <div class="text">人</div>
                                    </template></el-input
                                >
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <div class="impactButton">
                    <div
                        class="impactbutton_little_1"
                        @click="
                            mitigate = false;
                            $loading.hide();
                        "
                    >
                        取消
                    </div>
                    <div class="impactbutton_little" @click="submitMitigate()">
                        确定
                    </div>
                </div>
            </div>
        </div>
        <div
            :class="{
                addtaskDialog: chooseDispatchtask,
                addtaskDialog1: !chooseDispatchtask,
            }"
            v-if="addTask"
            ref="addtaskDialogRef"
        >
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                增派临时任务
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click.stop="closeDialogtask"
                />
            </div>
            <div class="top_button_addtask" v-if="showTopButton">
                <div
                    :class="{
                        top_button_1: !chooseDispatchtask,
                        top_button_2: chooseDispatchtask,
                    }"
                    @click.stop="chooseTeamtask"
                >
                    队伍调度
                </div>
                <div
                    :class="{
                        top_button_1: chooseDispatchtask,
                        top_button_2: !chooseDispatchtask,
                    }"
                    @click.stop="chooseSuppliestask"
                >
                    物资调度
                </div>
            </div>
            <div class="sendrankFrom">
                <el-form
                    ref="formaddtask1"
                    :model="sendrankFrom"
                    :rules="sendrankRules"
                    v-if="chooseDispatchtask == true"
                >
                    <el-form-item
                        key="rankId"
                        label="指派队伍"
                        prop="contingentId"
                    >
                    
                        <el-select
                            v-model="sendrankFrom.contingentId"
                            placeholder="请选择指派队伍"
                            :popper-append-to-body="false"
                            popper-class="select-popper"
                            :disabled="rankDisabled"
                        >
                            <el-option
                                v-for="(item, index) in ranksOptions"
                                :key="index"
                                :label="item.contingentName"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        key="rankRemark"
                        label="任务备注"
                        prop="taskRemark"
                    >
                        <el-input
                            type="textarea"
                            v-model="sendrankFrom.taskRemark"
                            style="width: 60%"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="是否即刻完成" prop="taskDescription">
                        <el-radio v-model="ranksRadio" label="1">是</el-radio>
                        <el-radio v-model="ranksRadio" label="2">否</el-radio>
                    </el-form-item>
                    <el-form-item
                        key="rankFinish"
                        label="期望完成时间"
                        prop="finishTime"
                        v-if="ranksRadio != 1"
                    >
                        <el-date-picker
                            v-model="sendrankFrom.finishTime"
                            type="datetime"
                            placeholder="选择日期时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            format="YYYY-MM-DD HH:mm:ss"
                            :popper-append-to-body="false"
                            popper-class="datepopper1"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item
                        key="rankisIn"
                        label="短信通知负责人"
                        prop="isInform"
                    >
                        <el-radio-group v-model="sendrankFrom.isInform">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
                <el-form
                    v-if="chooseDispatchtask == false"
                    :model="sendgoodFrom"
                    :rules="sendgoodRules"
                    ref="formaddtask2"
                    label-width="147px"
                >
                    <el-form-item
                        key="goodDepot"
                        label="调度仓库"
                        prop="depotId"
                    >
                        <el-select
                            v-model="sendgoodFrom.depotId"
                            placeholder="请选择调度仓库"
                            @change="depotChange"
                            :disabled="goodDisabled"
                            :popper-append-to-body="false"
                            popper-class="select-popper"
                        >
                            <el-option
                                v-for="(item, index) in depotOptions"
                                :key="index"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <div class="goodFromTitle">物资调度</div>
                    <div class="goodFromtableClass">
                        <table frame="void" cellspacing="0">
                            <tr>
                                <th>物资名称</th>
                                <th>物资类型</th>
                                <th>库存量</th>
                                <th>调度量</th>
                            </tr>
                            <tr v-for="(item, index) in goodsData" :key="index">
                                <td>
                                    <div class="tdFontDiv1">
                                        {{ item.materialName }}
                                    </div>
                                </td>
                                <!-- <td  ><div class="tdFontDiv" style="width:227px">{{dict.type.materiel_type.find(
                    (ele) => ele.value == item.materialType
                  ).label}}</div></td> -->
                                <td>
                                    <div class="tdFontDiv1">
                                        {{ item.materialType }}
                                    </div>
                                </td>
                                <td>
                                    <div class="tdFontDiv1">
                                        {{ item.inventory }}
                                    </div>
                                </td>
                                <td>
                                    <div class="tdFontDiv1">
                                        <el-input
                                            v-model="item.dispatchQuantity"
                                            type="number"
                                        ></el-input>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <el-form-item
                        key="goodRemark"
                        label="任务备注"
                        prop="taskRemark"
                    >
                        <el-input
                            type="textarea"
                            v-model="sendgoodFrom.taskRemark"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="是否即刻完成" prop="taskDescription">
                        <el-radio v-model="goodsRadio" label="1">是</el-radio>
                        <el-radio v-model="goodsRadio" label="2">否</el-radio>
                    </el-form-item>
                    <el-form-item
                        key="goodFinish"
                        label="期望完成时间"
                        prop="finishTime"
                        v-if="goodsRadio == '2'"
                    >
                        <el-date-picker
                            v-model="sendgoodFrom.finishTime"
                            type="datetime"
                            placeholder="选择日期时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            format="YYYY-MM-DD HH:mm:ss"
                            :popper-append-to-body="false"
                            popper-class="datepopper1"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item
                        key="goodisIn"
                        label="是否短信通知"
                        prop="isInform"
                    >
                        <el-radio-group v-model="sendgoodFrom.isInform">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
            <div class="impactButton" v-if="chooseDispatchtask == true">
                <div
                    class="impactbutton_little_1"
                    @click="
                        addTask = false;
                        $loading.hide();
                    "
                >
                    取消
                </div>
                <div
                    class="impactbutton_little"
                    @click="submitaddtask(formaddtask1)"
                >
                    确定
                </div>
            </div>
            <div class="impactButton" v-if="chooseDispatchtask == false">
                <div
                    class="impactbutton_little_1"
                    @click="
                        addTask = false;
                        $loading.hide();
                    "
                >
                    取消
                </div>
                <div
                    class="impactbutton_little"
                    @click="submitaddtask(formaddtask2)"
                >
                    确定
                </div>
            </div>
        </div>
        <div class="recordDialog" v-if="record">
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                执行记录
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click.stop="closeDialogRecord"
                />
            </div>
            <div class="record_content">
                <div
                    class="recordList"
                    v-if="
                        recordList.eventReport &&
                        Object.keys(recordList.eventReport).length != 0
                    "
                >
                    <div style="display: flex; position: relative">
                        <div class="round"></div>
                        <div class="recordTitle">事件接报</div>
                    </div>
                    <div class="subTitle">
                        <div>
                            上报时间：{{
                                recordList.eventReport.reportTime
                                    ? recordList.eventReport.reportTime
                                    : ""
                            }}
                        </div>
                        <div>
                            上报人：{{ recordList.eventReport.submitPerson }}
                            {{ recordList.eventReport.contactNumber }}
                        </div>
                        <div>
                            审核时间：{{ recordList.eventReport.auditTime }}
                        </div>
                        <div>
                            审核人：{{ recordList.eventReport.auditPerson }}
                        </div>
                    </div>
                </div>
                <div
                    class="recordList"
                    v-if="
                        recordList.planStarts &&
                        recordList.planStarts.length != 0
                    "
                >
                    <div style="display: flex; position: relative">
                        <div class="round"></div>
                        <div class="recordTitle">预案启动</div>
                    </div>
                    <div class="subTitle">
                        <div
                            v-for="(item, index) in recordList.planStarts"
                            :key="index"
                            style="margin: 10px 0"
                        >
                            <div>预案名称：{{ item.planName }}</div>
                            <div>开始时间：{{ item.startTime }}</div>
                            <div>处理人员：{{ item.handler }}</div>
                        </div>
                    </div>
                </div>
                <div
                    class="recordList"
                    v-if="
                        recordList.continuousDispatch &&
                        recordList.continuousDispatch.length != 0
                    "
                >
                    <div style="display: flex; position: relative">
                        <div class="round"></div>
                        <div class="recordTitle">持续调度</div>
                    </div>
                    <div class="subTitle">
                        <div
                            v-for="(
                                item, index
                            ) in recordList.continuousDispatch"
                            :key="index"
                            style="margin: 10px 0"
                        >
                            <div>
                                任务开始时间：{{
                                    item.startTime ? item.startTime : ""
                                }}
                            </div>
                            <div
                                :style="
                                    item.assignTaskType == 5012901
                                        ? 'color:red'
                                        : 'color:green'
                                "
                            >
                                {{
                                    dicts.assign_task_type.find(
                                        (ele) =>
                                            ele.code == item.assignTaskType,
                                    )
                                        ? dicts.assign_task_type.find(
                                              (ele) =>
                                                  ele.code ==
                                                  item.assignTaskType,
                                          ).name
                                        : ""
                                }}
                            </div>
                            <div>指派队伍：{{ item.contingentName }}</div>
                            <div>任务结束时间：{{ item.endTime }}</div>
                        </div>
                    </div>
                </div>
                <div
                    class="recordList"
                    v-if="
                        recordList.endRescue &&
                        Object.keys(recordList.endRescue).length != 0
                    "
                >
                    <div style="display: flex; position: relative">
                        <div class="round"></div>
                        <div class="recordTitle">结束救援</div>
                    </div>
                    <div class="subTitle">
                        <div>
                            结束时间：{{
                                recordList.endRescue
                                    ? recordList.endRescue.endTime
                                    : ""
                            }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="textMessageDialog" v-if="textMessage">
            <div class="command_title">
                短信通知
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click="closeDialogtextMessage"
                />
            </div>
            <div class="sliderClasstextMessage">
                <div style="text-align: left">请选择通知范围</div>
                <div style="text-align: left">
                    当前半径:{{ radiustextMessage }}米
                </div>
                <el-slider
                    v-model="radiustextMessage"
                    :min="10"
                    :max="1000"
                ></el-slider>
                <div class="sliderText">
                    <div>10米</div>
                    <div>1000米</div>
                </div>
            </div>
            <div class="textTitle">通知信息</div>
            <el-form
                ref="formtextMessage1"
                :model="formtextMessage"
                :rules="textMessageRules"
            >
                <el-form-item key="rankId" label="" prop="contingentId">
                    <el-select
                        v-model="formtextMessage.textMessageId"
                        placeholder="请选择模板"
                    >
                        <el-option
                            v-for="(item, index) in textMessageOptions"
                            :key="index"
                            :label="item.contingentName"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item key="rankRemark" label="" prop="taskRemark">
                    <el-input
                        type="textarea"
                        v-model="formtextMessage.taskRemark"
                        :rows="5"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div class="textBottom">
                <div class="bottomLeft">
                    共发送3000用户，成功发送2980名，失败20名
                </div>
                <div class="submitText">发送</div>
            </div>
        </div>
        <div class="dispatchDialog" v-if="dispatch">
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                指挥调度
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click.stop="closedispatchDialog"
                />
            </div>
            <div class="dispatchContent">
                <div class="dispatchContentChoose">
                    <div class="top_button_addtask" v-show="false">
                        <div
                            :class="{
                                top_button_1: !chooseplanResponse,
                                top_button_2: chooseplanResponse,
                            }"
                            @click="planResponse"
                        >
                            预案响应
                        </div>
                        <div
                            :class="{
                                top_button_1: chooseplanResponse,
                                top_button_2: !chooseplanResponse,
                            }"
                            @click="temporaryAssignments"
                        >
                            临时任务
                        </div>
                    </div>
                </div>
                <div v-if="chooseplanResponse">
                    <div class="dispatchButton" @click="toStartPlan">
                        启动预案
                    </div>
                    <div class="chooseplanResponseContent">
                        <div class="dispatchCardList">
                            <div
                                class="dispatchCard"
                                v-for="(item, index) in dispatchCardList"
                                :key="index"
                                @click="toShowProcess(item)"
                            >
                                <div
                                    class="diapatchTitle"
                                    :title="item.planName"
                                >
                                    {{ item.planName }}
                                </div>
                                <div class="responseLevel">
                                    {{
                                        dicts.response_level.find(
                                            (ele) =>
                                                ele.code == item.responseLevel,
                                        ) == undefined
                                            ? ""
                                            : dicts.response_level.find(
                                                  (ele) =>
                                                      ele.code ==
                                                      item.responseLevel,
                                              ).name
                                    }}
                                </div>
                                <div class="dispatchTime">
                                    启动时间 &nbsp;{{ item.createTime }}
                                </div>
                            </div>
                        </div>
                        <img
                            src="../../../assets/images/card/fire_12.png"
                            alt=""
                            srcset=""
                            class="imgStyle"
                        />
                        <div
                            class="dispatchMid"
                            v-if="dispatchMidList.length != 0"
                        >
                            <div class="dispatchrecord_content">
                                <div
                                    class="dispatchMidList"
                                    v-for="(item, index) in dispatchMidList"
                                    :key="index"
                                >
                                    <div
                                        style="
                                            display: flex;
                                            position: relative;
                                        "
                                    >
                                        <div class="round"></div>
                                        <div class="dispatchMidTitle">
                                            <div class="dispatchName">
                                                <span class="keynote">*</span>
                                                响应名称:<span
                                                    class="dispatchNamelittle"
                                                    :title="item.responseName"
                                                    >{{
                                                        item.responseName
                                                            .length > 4
                                                            ? item.responseName.slice(
                                                                  0,
                                                                  4,
                                                              ) + "..."
                                                            : item.responseName.slice(
                                                                  0,
                                                                  item
                                                                      .responseName
                                                                      .length,
                                                              )
                                                    }}</span
                                                >
                                            </div>
                                            <div
                                                :class="{
                                                    dispatchtag1:
                                                        item.processStatus !=
                                                        5011801,
                                                    dispatchtag2:
                                                        item.processStatus ==
                                                        5011801,
                                                }"
                                            >
                                                {{
                                                    dicts.step_status.find(
                                                        (ele) =>
                                                            ele.code ==
                                                            item.processStatus,
                                                    ).name
                                                }}
                                            </div>
                                            <div
                                                class="dispatchStart"
                                                v-if="
                                                    item.processStatus ==
                                                    5011801
                                                "
                                                @click="
                                                    processUpdate(
                                                        item.id,
                                                        '5011802',
                                                    )
                                                "
                                            >
                                                开始
                                            </div>
                                            <div
                                                class="dispatchEnd"
                                                v-if="
                                                    item.processStatus ==
                                                    5011802
                                                "
                                                @click="
                                                    processUpdate(
                                                        item.id,
                                                        '5011803',
                                                    )
                                                "
                                            >
                                                结束
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dispatchsubTitle">
                                        <div class="dispatchNote">
                                            注意事项:{{ item.announcement }}
                                        </div>
                                        <div
                                            class="dispatchTeam"
                                            v-for="(
                                                ele, ind
                                            ) in item.contingentList"
                                            :key="ind"
                                        >
                                            <div
                                                class="dispatchTeamName"
                                                :title="ele.contingentName"
                                            >
                                                响应队伍:
                                                {{ ele.contingentName }}
                                            </div>
                                            <div>
                                                {{
                                                    dicts.contingent_status.find(
                                                        (el) =>
                                                            el.code ==
                                                            ele.contingentStatus,
                                                    ).name
                                                }}
                                            </div>
                                            <div
                                                class="dispatchStart"
                                                v-if="
                                                    ele.contingentStatus ==
                                                        5011901 &&
                                                    item.processStatus ==
                                                        5011802
                                                "
                                                @click="useTask(ele)"
                                            >
                                                指派
                                            </div>
                                            <div
                                                class="dispatchStart"
                                                v-if="
                                                    ele.contingentStatus ==
                                                        5011902 &&
                                                    item.processStatus ==
                                                        5011802
                                                "
                                                @click="finishTask(ele)"
                                            >
                                                完成
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <img
                            src="../../../assets/images/card/fire_12.png"
                            alt=""
                            srcset=""
                            class="imgStyle"
                        />
                        <div
                            class="dispatchLeft"
                            v-if="dispatchMidRightList.length != 0"
                        >
                            <div class="dispatchrecord_content_left">
                                <div
                                    class="dispatchMidList"
                                    v-for="(
                                        item, index
                                    ) in dispatchMidRightList"
                                    :key="index"
                                >
                                    <div
                                        style="
                                            display: flex;
                                            position: relative;
                                        "
                                    >
                                        <div class="round">1</div>
                                        <div class="dispatchMidTitle">
                                            <div class="dispatchName">
                                                <span class="keynote">*</span>
                                                响应名称:<span
                                                    class="dispatchNamelittle_left"
                                                    :title="item.responseName"
                                                    >{{
                                                        item.responseName
                                                            .length > 4
                                                            ? item.responseName.slice(
                                                                  0,
                                                                  4,
                                                              ) + "..."
                                                            : item.responseName.slice(
                                                                  0,
                                                                  item
                                                                      .responseName
                                                                      .length,
                                                              )
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dispatchsubTitle">
                                        <div
                                            class="dispatch_left"
                                            v-if="item.startTime != ''"
                                        >
                                            开始时间:{{ item.startTime }}
                                        </div>
                                        <div
                                            v-for="(
                                                ite, ind
                                            ) in item.contingentList"
                                            :key="ind"
                                        >
                                            <div class="dispatch_left">
                                                指派队伍:
                                                {{ ite.contingentName }}
                                            </div>
                                            <div class="dispatch_left">
                                                任务描述:
                                                {{ ite.taskDescription }}
                                            </div>
                                            <div
                                                class="dispatch_left"
                                                v-if="
                                                    ite.contingentStatus ==
                                                    '5011902'
                                                "
                                            >
                                                预期完成时间:
                                                {{ ite.finishTime }}
                                            </div>
                                            <div
                                                class="dispatch_left"
                                                v-if="
                                                    ite.contingentStatus ==
                                                    '5011903'
                                                "
                                            >
                                                完成时间：{{ ite.endTime }}
                                            </div>
                                        </div>

                                        <div
                                            :class="{
                                                dispatch_left: true,
                                                dispatchtag1:
                                                    item.processStatus !=
                                                    5011801,
                                                dispatchtag2:
                                                    item.processStatus ==
                                                    5011801,
                                            }"
                                        >
                                            {{
                                                dicts.step_status.find(
                                                    (ele) =>
                                                        ele.code ==
                                                        item.processStatus,
                                                ).name
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="!chooseplanResponse">
                    <div class="dispatchButton2" @click="toaddTask">
                        增派临时任务
                    </div>
                    <div class="dispatchtableClass">
                        <table frame="void" cellspacing="0">
                            <tr>
                                <th>时间</th>
                                <th>负责人</th>
                                <th>任务描述</th>
                                <th>期望完成时间</th>
                                <th>任务状态</th>
                                <th>操作</th>
                            </tr>
                            <tr
                                v-for="(item, index) in temporaryData"
                                :key="index"
                            >
                                <td>
                                    <div class="tdFontDiv">
                                        {{ item.updateTime }}
                                    </div>
                                </td>
                                <td>
                                    <div class="tdFontDiv">
                                        {{ item.leader }}
                                    </div>
                                </td>
                                <td>
                                    <div class="tdFontDiv">
                                        {{ item.taskRemark }}
                                    </div>
                                </td>
                                <td>
                                    <div class="tdFontDiv" s>
                                        {{ item.finishTime }}
                                    </div>
                                </td>
                                <td>
                                    <div class="tdFontDiv">
                                        {{
                                            dicts.task_status.find(
                                                (ele) =>
                                                    ele.code == item.taskStatus,
                                            )
                                                ? dicts.task_status.find(
                                                      (ele) =>
                                                          ele.code ==
                                                          item.taskStatus,
                                                  ).name
                                                : ""
                                        }}
                                    </div>
                                </td>
                                <td>
                                    <div
                                        :class="{
                                            tdFontDiv: true,
                                            tdFontDiv1: true,
                                        }"
                                    >
                                        <!-- <div>详情</div> -->
                                        <div
                                            v-if="item.taskStatus == 5012601"
                                            @click="changeJob(item, '5012602')"
                                        >
                                            完成
                                        </div>
                                        <div
                                            v-if="item.taskStatus == 5012601"
                                            @click="changeJob(item, '5012603')"
                                        >
                                            取消
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="dispatchbottom">
                    <div class="dispatchtip" v-if="showTip">
                        有开始未完成的任务不能结束
                    </div>
                    <div class="dispatchButton" @click="toendRescue">
                        结束演练
                    </div>
                </div>
            </div>
        </div>
        <!-- 预案响应点出来的临时任务 -->
        <div
            :class="{
                addtaskDialog: true,
            }"
            ref="TaskandGoods"
            v-if="addTask1"
        >
            <div class="command_title">
                增派临时任务
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click.stop="closeDialogtask"
                />
            </div>

            <div class="sendrankFrom">
                <el-form
                    ref="formaddtask3"
                    :model="sendrankFromPai"
                    :rules="sendrankRulesPai"
                >
                    <el-form-item
                        key="rankId"
                        label="指派队伍"
                        prop="contingentId"
                    >
                        <el-select
                            v-model="sendrankFromPai.contingentId"
                            placeholder="请选择指派队伍"
                            :popper-append-to-body="false"
                            popper-class="select-popper"
                            :disabled="rankDisabled"
                        >
                            <el-option
                                v-for="(item, index) in ranksOptions"
                                :key="index"
                                :label="item.contingentName"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        key="taskDescription"
                        label="任务描述"
                        prop="taskDescription"
                    >
                        <el-input
                            type="textarea"
                            v-model="sendrankFromPai.taskDescription"
                            style="width: 60%"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        key="rankFinish"
                        label="期望完成时间"
                        prop="finishTime"
                    >
                        <el-date-picker
                            v-model="sendrankFromPai.finishTime"
                            type="datetime"
                            placeholder="选择日期时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            format="YYYY-MM-DD HH:mm:ss"
                            :popper-append-to-body="false"
                            popper-class="datepopper1"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item
                        key="rankisIn"
                        label="短信通知负责人"
                        prop="isSmsNotice"
                    >
                        <el-radio-group v-model="sendrankFromPai.isSmsNotice">
                            <el-radio :label="true">是</el-radio>
                            <el-radio :label="false">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
            <div class="impactButton">
                <div
                    class="impactbutton_little"
                    @click="submitaddtask1(formaddtask3)"
                >
                    确定
                </div>
                <div
                    class="impactbutton_little"
                    @click="
                        addTask1 = false;
                        $loading.hide();
                    "
                >
                    取消
                </div>
            </div>
        </div>
        <div
            :class="{
                addtaskDialog: true,
            }"
            ref="startPrePlan"
            v-if="PrePlan"
        >
            <div class="command_title">
                <div>
                    <img
                        class="title_image"
                        src="../../../assets/images/card/title_left.svg"
                    />
                </div>
                启动预案
                <img
                    src="../../../assets/images/card/index2_7.png"
                    alt=""
                    srcset=""
                    class="closeImg"
                    @click="
                        PrePlan = false;
                        $loading.hide();
                    "
                />
            </div>

            <div class="sendrankFrom">
                <el-form
                    ref="formPrePlan1"
                    :model="formPrePlan"
                    :rules="rulePrePlan1"
                >
                    <el-form-item key="planId" label="预案名称" prop="planId">
                        <el-select
                            v-model="formPrePlan.planId"
                            placeholder="请选择预案名称"
                            :popper-append-to-body="false"
                            popper-class="select-popper"
                            @change="changePlan"
                        >
                            <el-option
                                v-for="(item, index) in planList"
                                :key="index"
                                :label="item.planName"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        key="responseLevelId"
                        label="响应等级"
                        prop="responseLevelId"
                    >
                        <el-select
                            v-model="formPrePlan.responseLevelId"
                            placeholder="请选择响应等级"
                            :popper-append-to-body="false"
                            popper-class="select-popper"
                        >
                            <el-option
                                v-for="(item, index) in responseLevelList"
                                :key="index"
                                :label="item.responseName"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <div class="impactButton">
                <div
                    class="impactbutton_little_1"
                    @click="
                        PrePlan = false;
                        $loading.hide();
                    "
                >
                    取消
                </div>
                <div
                    class="impactbutton_little"
                    @click="submitaddtask2(formPrePlan1)"
                >
                    确定
                </div>
            </div>
        </div>
        <infrastructure-dialog
            :pickId="videoId"
            v-if="videoDialogShow"
            @closeDialog="closeDialog1"
        ></infrastructure-dialog>
        <!-- <div
      class="hide"
      v-if="
      impact || record || dispatch || addTask || power || textMessage || door
    "
  ></div> -->
    </div>
</template>

<script setup>
import smallModuleVue from "./smallModule.vue";

import {
    getDict,
    emergencyEvent,
    dictionary,
    hxGetCurrentFireWatch,
    overviewList,
    TaskOption,
    supplyOption,
    listOfDepot,
    saveTask,
    recordDetile,
    showPlan,
    showProcess,
    showProcessRight,
    updatePlanStatus,
    updateTaskStatus,
    temporaryList,
    updatetemporaryJob,
    togetplanList,
    getLevelList,
    saveResponse,
    saveRecordEnd,
    analysisdecision,
    getArea,
    reportDetails,
    getMatterMsds,
    dispatchPerson,
    getPath,
} from "../../../assets/js/api/fire.js";
import {
    ref,
    onMounted,
    reactive,
    nextTick,
    defineEmits,
    watch,
    getCurrentInstance,
    onUnmounted,
} from "vue";
import infrastructureDialog from "./subgroup/infrastructureDialog.vue";
const { proxy } = getCurrentInstance();
// const props = defineProps({
//   itemList: {
//     type: [Object]
//   },
// });
const videoId = ref(null);
const videoDialogShow = ref(false);
const propslist = defineProps(["itemList"]);
//队伍调度是否即刻完成
const ranksRadio = ref("1");
//物资调度是否即刻完成
const goodsRadio = ref("1");
const optionsList = ref([
    {
        label: "火灾影响分析",
        value: 1,
    },
    {
        label: "爆炸影响分析",
        value: 2,
    },
    {
        label: "有毒有害物质影响分析",
        value: 3,
    },
]);
const dispatchCardList = ref([{}, {}, {}, {}, {}, {}, {}, {}]);
const dispatchMidList = ref([]);
// const zIndex=ref(9999)
const choosePlan = ref(null);
const dispatchMidRightList = ref([]);
const showTopButton = ref(true);
const radius = ref(0);
const radiustextMessage = ref(500);
const checkList = ref(["BNCS", "FHMB", "ZBJK"]);
const chooseDispatch = ref(true);
const chooseDispatchtask = ref(true);
const impact = ref(false);
const reporttype = ref(false);
const dispatch = ref(false);
const command = ref(false);
const record = ref(false);
const power = ref(false);
const textMessage = ref(false);
const formImpact1 = ref(null);
const emit = defineEmits(["clickListButton"]);
const formImpact = ref({});
const testmap = ref(null);
const materialNameList = ref(null);
const materialNameListone = ref(null);
const addTask = ref(false);
const KS = ref([]);
const KW = ref([]);
const formaddtask1 = ref(null);
const formaddtask2 = ref(null);
const isopen = ref(null);
const sendrankFrom = ref({});
const recordList = ref({});
const textMessageOptions = ref([{}, {}]);
const doorKey = ref(null);
const rankDisabled = ref(false);
const goodDisabled = ref(false);
const itemisshow = ref(false);
const temporaryData = ref([]);
const area = ref();
const sendrankRules = {
    contingentId: [
        { required: true, message: "请选择指派队伍", trigger: "change" },
    ],
    taskRemark: [
        { required: true, message: "请输入任务备注", trigger: "blur" },
    ],
    finishTime: [
        { required: true, message: "请选择期望时间", trigger: "change" },
    ],
    isInform: [
        { required: true, message: "请选择是否通知", trigger: "change" },
    ],
};
const sendgoodFrom = ref({});
const sendgoodRules = {
    depotId: [{ required: true, message: "请选择调度仓库", trigger: "change" }],
    taskRemark: [
        { required: true, message: "请输入任务备注", trigger: "blur" },
    ],
    finishTime: [
        { required: true, message: "请选择期望时间", trigger: "change" },
    ],
    isInform: [
        { required: true, message: "请选择是否通知", trigger: "change" },
    ],
};
const textMessageRules = {
    textMessageId: [{ required: true, message: "请选择", trigger: "change" }],
    taskRemark: [{ required: true, message: "请输入内容", trigger: "blur" }],
};
const addtaskDialogRef = ref();
const formtextMessage = ref({});
const goodsData = ref([]);
const ranksOptions = ref([]);
const depotOptions = ref([]);
const rulesImpact = {
    // temperature: [{ required: true, message: "请输入", trigger: "blur" }],
    // humidity: [{ required: true, message: "请输入", trigger: "blur" }],
    // ks: [{ required: true, message: "请选择", trigger: "select" }],
    // windScale: [{ required: true, message: "请输入", trigger: "blur" }],
    // slope: [{ required: true, message: "请输入", trigger: "blur" }],
    // windSpeed: [{ required: true, message: "请选择", trigger: "select" }],
    // kd: [{ required: true, message: "请输入", trigger: "blur" }],
};
const addTask1 = ref(false);
const sendrankFromPai = ref({});
const formaddtask3 = ref(null);
const sendrankRulesPai = {
    contingentId: [
        { required: true, message: "请选择指派队伍", trigger: "change" },
    ],
    taskDescription: [
        { required: true, message: "请输入任务描述", trigger: "blur" },
    ],
    finishTime: [
        { required: true, message: "请选择期望时间", trigger: "change" },
    ],
    isSmsNotice: [
        { required: true, message: "请选择是否通知", trigger: "change" },
    ],
};
const showTip = ref(false);
const chooseplanResponse = ref(false);
const TaskandGoods = ref();

const fireEvent = ref({});
const reporList = ref({});
const PrePlan = ref(false);
const formPrePlan1 = ref();
const formPrePlan = ref({});
const planList = ref([]);
const responseLevelList = ref([]);
const startPrePlan = ref();
const rulePrePlan1 = {
    planId: [{ required: true, message: "请选择预案名称", trigger: "change" }],
    responseLevelId: [
        { required: true, message: "请选择响应等级", trigger: "change" },
    ],
};
const dicts = reactive({
    plan_deduction: [],
    response_level: [],
    step_status: [],
    contingent_status: [],
    materiel_type: [],
    task_type: [],
    task_status: [],
    assign_task_type: [],
    event_level: [],
    emergency_dispatch_state: [],
    fire_wind: [],
});
const door = ref(false);
const touetask = ref([]);
const touegoods = ref([]);
const overViewData = reactive({
    // 001 物资 002 队伍
    5013001: [],
    5013002: [],
    5013003: [],
    5013004: [],
    5013005: [],
    5013006: [],
    5013007: [],
    5013008: [],
    5013009: [],
});
// const circle = ref(null);

const fireEvent1 = ref(null);

let tetsList = ref({
    BNCS: [],
    YJGB: [],
    ZBJK: [],
    FHMB: [],
    YLJG: [],
    FXYH: [],
    TXBZ: [],
});
onMounted(() => {
    //   console.log(propslist.itemList,"测试传参")
    //   itemisshow.value = isWithin10Minutes(propslist.itemList.createTime)
    //   console.log(isWithin10Minutes(propslist.itemList.createTime),"显示隐藏")
    //   console.log(impact.value,"impact.value参数")
    // proxy.$bus.off("fireEvent");

    proxy.$bus.on("fireEvent", (val) => {
        removeLine();
        console.log("fireEvent----------------------", val);
        fireEvent.value = val;
        console.log(fireEvent.value);
        itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);
        if (
            window.circletwo != [] &&
            window.circletwo != undefined &&
            window.circletwo != null
        ) {
            window.circletwo.forEach((r) => {
                window.map1.remove(r);
            });
            window.circletwo = [];
        }
        if (
            window.circletwoone != [] &&
            window.circletwoone != undefined &&
            window.circletwoone != null
        ) {
            window.circletwoone.forEach((r) => {
                window.map1.remove(r);
            });
            window.circletwoone = [];
        }
        if (
            window.circleone != [] &&
            window.circleone != undefined &&
            window.circleone != null
        ) {
            window.circleone.forEach((r) => {
                window.map1.remove(r);
            });
            window.circleone = [];
        }
        if (window.overlays) {
            window.map1.remove(window.overlays);
        }
        console.log("zaiciceshi------", window.massAllPoints);
        if (window.massAllPoints) {
            console.log("ceshi", window.massAllPoints);
            window.map1.remove(window.massAllPoints);
            window.massAllPoints.setMap(null);
            window.massAllPoints = null;
        }
    });
    nowWeather();
    dictGetDict();
    getDictionary();
    getFirePeople();
    getoverviewList();
    toUeInit();

    // checkList.value =[]

    clearFlags();
    getmaterialName();
});
watch(checkList, (newValue, oldValue) => {
    var obj = {
        BNCS: 5013003,
        YJGB: 5013005,
        ZBJK: 5013004,
        FHMB: 5013008,
        YLJG: 5013009,
        FXYH: 5013006,
        TXBZ: 5013007,
    };
    if (newValue) {
        console.log(newValue, oldValue, "jianting");
    }
    proxy.$bus.on("fireEvent", (val) => {
        if (propslist.itemList != undefined && propslist.itemList != null) {
            Reflect.set(
                ranksRouteForm.value,
                "destination",
                +propslist.itemList.longitude +
                    "," +
                    propslist.itemList.latitude,
            );
        } else {
            Reflect.set(
                ranksRouteForm.value,
                "destination",
                +fireEvent.value.longitude + "," + fireEvent.value.latitude,
            );
        }
        removeLine();
        console.log("fireEvent----------------------", val);
        fireEvent.value = val;
        console.log(fireEvent.value);
        itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);
        if (
            window.circletwo != [] &&
            window.circletwo != undefined &&
            window.circletwo != null
        ) {
            window.circletwo.forEach((r) => {
                window.map1.remove(r);
            });
            window.circletwo = [];
        }
        if (
            window.circletwoone != [] &&
            window.circletwoone != undefined &&
            window.circletwoone != null
        ) {
            window.circletwoone.forEach((r) => {
                window.map1.remove(r);
            });
            window.circletwoone = [];
        }
        if (
            window.circleone != [] &&
            window.circleone != undefined &&
            window.circleone != null
        ) {
            window.circleone.forEach((r) => {
                window.map1.remove(r);
            });
            window.circleone = [];
        }
        if (window.overlays) {
            window.map1.remove(window.overlays);
        }
        console.log("zaiciceshi------", window.massAllPoints);
        if (window.massAllPoints) {
            console.log("ceshi", window.massAllPoints);
            window.map1.remove(window.massAllPoints);
            window.massAllPoints.setMap(null);
            window.massAllPoints = null;
        }
    });
});
const selectType = (e) => {
    //   console.log(e,"ceshi")
    if (e == 1) {
        formImpact.value = {
            temperature: "",
            humidity: "",
            windScale: "",
            windSpeed: "",
            slope: "",
            windDirection: "",
            time: "",
            analysisType: 1,
            longitude: propslist.itemList.longitude,
            latitude: propslist.itemList.latitude,
            analysisModel: 2,
        };
        Reflect.set(formImpact.value, "temperature", nowWea.value.temperature);
        Reflect.set(formImpact.value, "humidity", nowWea.value.humidity);
        Reflect.set(formImpact.value, "windScale", nowWea.value.windScale);
        if (nowWea.value && nowWea.value.windDirection) {
            let xDir = nowWea.value.windDirection + "风";
            console.log(dicts.fire_wind.find((i) => i.name == xDir));
            if (dicts.fire_wind.find((i) => i.name == xDir) != undefined) {
                let dir = dicts.fire_wind.find((i) => i.name == xDir).code;
                console.log(dir);
                Reflect.set(formImpact.value, "windDirection", dir);
            }
        }
    } else if (e == 2) {
        formImpact.value = {
            weight: "",
            matterId: "",
            analysisType: 2,
            longitude: propslist.itemList.longitude,
            latitude: propslist.itemList.latitude,
            analysisModel: 3,
        };
    } else {
        formImpact.value = {
            weight: "",
            temperature: "",
            matterId: "",
            windDirection: "",
            windSpeed: "",
            time: "",
            analysisType: 3,
            longitude: propslist.itemList.longitude,
            latitude: propslist.itemList.latitude,
            analysisModel: 1,
        };
        Reflect.set(formImpact.value, "temperature", nowWea.value.temperature);
        if (nowWea.value && nowWea.value.windDirection) {
            let xDir = nowWea.value.windDirection + "风";
            console.log(dicts.fire_wind.find((i) => i.name == xDir));
            if (dicts.fire_wind.find((i) => i.name == xDir) != undefined) {
                let dir = dicts.fire_wind.find((i) => i.name == xDir).code;
                console.log(dir);
                Reflect.set(formImpact.value, "windDirection", dir);
            }
        }
    }
};
const isWithin10Minutes = (dateStr) => {
    const now = new Date(); // 获取当前时间

    const targetDate = new Date(dateStr); // 将要比较的时间转换为 Date 对象

    const diff = targetDate.getTime() - now.getTime(); // 计算时间差

    if (diff <= 10 * 60 * 1000 && diff >= 0) {
        return true;
    } else {
        return false;
    }
};
const toUeInit = () => {
    // emergencyEvent({
    //   current: 1,
    //   size: 1,
    // }).then((res) => {
    //   fireEvent1.value = res.data.data[0];
    // });
    fireEvent1.value = propslist.itemList;
    //   console.log(window.markers,"第二次弹窗进入111")
    if (window.markers) {
        window.markers.setMap(null);
        window.map1.remove(window.markers);
        // console.log(window.markers,"第二次弹窗进入")
    }
    //获取资源列表
    for (let i in overViewData) {
        // console.log(i);
        overviewList({
            resourceType: i,
        }).then((res) => {
            overViewData[i] = res.data.data.list;
        });
    }
    // ue4({ Command: "XFHZ", val: "radius", radius: "10" });
    //   setTimeout(()=>{

    //     changeRadius(500);

    //   },3000)
};

// 获取两点之间距离
const getDistance = (elng, elat, lng, lat) => {
    var p1 = [elng, elat];
    var p2 = [lng, lat];
    // 返回 p1 到 p2 间的地面距离，单位：米
    var dis = AMap.GeometryUtil.distance(p1, p2);
    return dis;
};

const getmaterialName = () => {
    getMatterMsds({ analysisType: 1 }).then((res) => {
        materialNameList.value = res.data.data;
    });
    getMatterMsds({ analysisType: 2 }).then((res) => {
        materialNameListone.value = res.data.data;
    });
};
// 添加圆形覆盖物 找到正确半径内的覆盖物 然后重新调用打点方法打点
const changeRadius = (e) => {
    // ue4({ Command: "XFHZ", val: "radius", radius: e });
    console.log(e, propslist.itemList, "测试");
    //   if (e != 500) {
    //     checkList.value = []
    //   }

    //   window.map1.clearMap();
    if (window.circle) {
        // window.map1.remove(circle);

        window.circle.setMap(null);
    }
    if (window.markers) {
        window.markers.setMap(null);
    }

    window.circle = new AMap.Circle({
        center: [propslist.itemList.longitude, propslist.itemList.latitude], // 圆心位置
        radius: e, // 半径（单位：米）
        fillColor: "#d2d8de", // 填充颜色
        fillOpacity: 0.5, // 填充透明度
        strokeColor: "#87c6e2", // 边框颜色
        strokeWeight: 1, // 边框宽度
    });
    window.circle.setMap(window.map1);
    //   console.log(overViewData[5013003],"overViewData[5013003]")
    let oneList = {
        5013001: [],
        5013002: [],
        5013003: [],
        5013004: [],
        5013005: [],
        5013006: [],
        5013007: [],
        5013008: [],
        5013009: [],
    };
    console.log(overViewData[5013001], "overViewData");
    // tetsList.value.BNCS.forEach((element) => {
    overViewData[5013001].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        // console.log(element.distance,"element.distance",element)
        if (element.distance < e) {
            oneList[5013001].push(element);
            //   console.log(element.longitude,element.latitude)
        }
    });
    // overViewData[5013001] = oneList[5013001]
    overViewData[5013002].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        // console.log(element.distance,"element.distance",element)
        if (element.distance < e) {
            oneList[5013002].push(element);
            console.log(element.longitude, element.latitude);
        }
    });
    // overViewData[5013002] = oneList[5013002]
    overViewData[5013003].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        console.log(element.distance, "element.distance", element);
        if (element.distance < e) {
            oneList[5013003].push(element);
        }
    });
    // overViewData[5013003] = oneList[5013003]
    overViewData[5013004].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        if (element.distance < e) {
            oneList[5013004].push(element);
        }
    });
    // overViewData[5013004] = oneList[5013004]
    overViewData[5013005].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        if (element.distance < e) {
            oneList[5013005].push(element);
            console.log(element.longitude, element.latitude);
        }
    });
    // overViewData[5013005] = oneList[5013005]
    overViewData[5013006].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        console.log(element.distance, "element.distance", element);
        if (element.distance < e) {
            oneList[5013006].push(element);
            console.log(element.longitude, element.latitude);
        }
    });
    // overViewData[5013006] = oneList[5013006]
    overViewData[5013007].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        console.log(element.distance, "element.distance", element);
        if (element.distance < e) {
            oneList[5013007].push(element);
            console.log(element.longitude, element.latitude);
        }
    });
    // overViewData[5013007] = oneList[5013007]
    overViewData[5013008].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        console.log(element.distance, "element.distance", element);
        if (element.distance < e) {
            oneList[5013008].push(element);
            console.log(element.longitude, element.latitude);
        }
    });
    // overViewData[5013008] = oneList[5013008]
    overViewData[5013009].forEach((element) => {
        element.distance = getDistance(
            element.longitude,
            element.latitude,
            propslist.itemList.longitude,
            propslist.itemList.latitude,
        );
        console.log(element.distance, "element.distance", element);
        if (element.distance < e) {
            oneList[5013009].push(element);
            console.log(element.longitude, element.latitude);
        }
    });

    overViewData[5013001] = oneList[5013001];
    overViewData[5013002] = oneList[5013002];
    overViewData[5013003] = oneList[5013003];
    overViewData[5013004] = oneList[5013004];
    overViewData[5013005] = oneList[5013005];
    overViewData[5013006] = oneList[5013006];
    overViewData[5013007] = oneList[5013007];
    overViewData[5013008] = oneList[5013008];
    overViewData[5013009] = oneList[5013009];
    // overViewData = Object.assign({}, oneList);
    console.log(oneList, "oneList遍历后oneList");
    console.log(overViewData, "overViewData[5013003]遍历后");
    console.log(
        overViewData[5013004],
        "overViewData==============================================",
    );
    changeCheck(checkList.value);
};
//资源打点
const changeCheck = (e) => {
    console.log(e, "选中的事件");
    let test = ["BNCS", "YJGB", "ZBJK", "FHMB", "YLJG", "FXYH", "TXBZ"];
    // checkList.value = e
    var obj = {
        BNCS: 5013003,
        YJGB: 5013005,
        ZBJK: 5013004,
        FHMB: 5013008,
        YLJG: 5013009,
        FXYH: 5013006,
        TXBZ: 5013007,
    };
    // if (markers.value){
    //   console.log(markers,"markers")
    //   // markers.value.forEach((marker) => {
    //   //   marker.setMap(null); // 从地图上移除标记点
    //   // });
    //   window.markers.value.setMap(null)
    // }
    // if (window.markers){
    //   window.markers.setMap(null)
    // }
    console.log(
        overViewData[obj["ZBJK"]],
        "overViewData==============================================",
    );
    if (e.indexOf("BNCS") != -1) {
        setmaekers(overViewData[obj["BNCS"]], "BNCS");
    } else {
        tetsList.value.BNCS.forEach((item) => {
            item.setMap(null);
        });
        console.log("删除标记点避难场所");
        // window.markers.setMap(tetsList["BNCS"])
        // window.markers.remove(tetsList["BNCS"])
    }
    if (e.indexOf("ZBJK") != -1) {
        console.log(overViewData[obj["ZBJK"]]);
        setmaekers(overViewData[obj["ZBJK"]], "ZBJK");
    } else {
        tetsList.value.ZBJK.forEach((item) => {
            item.setMap(null);
        });
    }
    if (e.indexOf("FHMB") != -1) {
        setmaekers(overViewData[obj["FHMB"]], "FHMB");
    } else {
        tetsList.value.FHMB.forEach((item) => {
            item.setMap(null);
        });
    }
    if (e.indexOf("YLJG") != -1) {
        setmaekers(overViewData[obj["YLJG"]], "YLJG");
    } else {
        tetsList.value.YLJG.forEach((item) => {
            item.setMap(null);
        });
    }
    if (e.indexOf("FXYH") != -1) {
        setmaekers(overViewData[obj["FXYH"]], "FXYH");
    } else {
        tetsList.value.FXYH.forEach((item) => {
            item.setMap(null);
        });
    }
    if (e.indexOf("TXBZ") != -1) {
        setmaekers(overViewData[obj["TXBZ"]], "TXBZ");
    } else {
        tetsList.value.TXBZ.forEach((item) => {
            item.setMap(null);
        });
    }

    if (!e.length) {
        console.log(tetsList.value, "tetsList.value");
        if (tetsList.value.BNCS) {
            tetsList.value.BNCS.forEach((item) => {
                item.setMap(null);
            });
        }
        if (tetsList.value.ZBJK) {
            tetsList.value.ZBJK.forEach((item) => {
                item.setMap(null);
            });
        }
        if (tetsList.value.FHMB) {
            tetsList.value.FHMB.forEach((item) => {
                item.setMap(null);
            });
        }
        if (tetsList.value.YLJG) {
            tetsList.value.YLJG.forEach((item) => {
                item.setMap(null);
            });
        }
        if (tetsList.value.FXYH) {
            tetsList.value.FXYH.forEach((item) => {
                item.setMap(null);
            });
        }
        if (tetsList.value.TXBZ) {
            tetsList.value.TXBZ.forEach((item) => {
                item.setMap(null);
            });
        }
        console.log(tetsList.value, "tetsList.value----结束");
    }

    // }else {
    //   console.log("没有选中的了")
    //   if (window.markers){
    //     window.markers.setMap(null)
    //   }
    // }

    console.log(overViewData, "所有数据");
};
const setmaekers = (list, type) => {
    console.log(list, "list---------------");
    let images;
    if (type == "BNCS") {
        // images = require("../../../assets/images/poi/shelter.png")
        images = new AMap.Icon({
            size: new AMap.Size(44, 44), // 图标尺寸
            image: new URL(
                "../../../assets/images/poi/shelter.png",
                import.meta.url,
            ).href, //绝对路径
            imageSize: new AMap.Size(44, 44),
        });
    } else if (type == "ZBJK") {
        // images = require("../../../assets/images/poi/monitor.png")
        images = new AMap.Icon({
            size: new AMap.Size(44, 44), // 图标尺寸
            image: new URL(
                "../../../assets/images/poi/monitor.png",
                import.meta.url,
            ).href, //绝对路径
            imageSize: new AMap.Size(44, 44),
        });
    } else if (type == "FHMB") {
        // images = require("../../../assets/images/poi/protection.png")
        images = new AMap.Icon({
            size: new AMap.Size(44, 44), // 图标尺寸
            image: new URL(
                "../../../assets/images/poi/protection.png",
                import.meta.url,
            ).href, //绝对路径
            imageSize: new AMap.Size(44, 44),
        });
    } else if (type == "YLJG") {
        // images = require("../../../assets/images/poi/medical.png")
        images = new AMap.Icon({
            size: new AMap.Size(44, 44), // 图标尺寸
            image: new URL(
                "../../../assets/images/poi/medical.png",
                import.meta.url,
            ).href, //绝对路径
            imageSize: new AMap.Size(44, 44),
        });
    } else if (type == "FXYH") {
        // images = require("../../../assets/images/poi/riskPosition.png")
        images = new AMap.Icon({
            size: new AMap.Size(44, 44), // 图标尺寸
            image: new URL(
                "../../../assets/images/poi/riskPosition.png",
                import.meta.url,
            ).href, //绝对路径
            imageSize: new AMap.Size(44, 44),
        });
    } else if (type == "TXBZ") {
        // images = require("../../../assets/images/poi/communication.png")
        images = new AMap.Icon({
            size: new AMap.Size(44, 44), // 图标尺寸
            image: new URL(
                "../../../assets/images/poi/communication.png",
                import.meta.url,
            ).href, //绝对路径
            imageSize: new AMap.Size(44, 44),
        });
    }
    tetsList.value[type].forEach((r) => {
        window.map1.remove(r);
    });
    //   tetsList.value[type] =[]
    console.log(list, "打印list");
    if (list.length) {
        console.log("nhhhhhhhhhhhhhhhhhhhhh");
        list.forEach((item) => {
            console.log("ceshizhege", item);
            if (type == "ZBJK") {
                if (item.isOnline == 1) {
                    window.markers = new AMap.Marker({
                        // content: '<div class="two-icon-infrastructure"></div>',
                        // icon: new AMap.Icon({
                        //   size: new AMap.Size(48, 88), // 图标尺寸
                        //   image: images,//绝对路径
                        //   imageSize: new AMap.Size(48, 88)
                        // }),
                        icon: new AMap.Icon({
                            size: new AMap.Size(44, 44), // 图标尺寸
                            image: new URL(
                                "../../../assets/images/poi/monitor.png",
                                import.meta.url,
                            ).href, //绝对路径
                            imageSize: new AMap.Size(44, 44),
                        }),
                        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-22, -44),
                        extData: type,
                    });
                } else {
                    window.markers = new AMap.Marker({
                        // content: '<div class="two-icon-infrastructure"></div>',
                        // icon: new AMap.Icon({
                        //   size: new AMap.Size(48, 88), // 图标尺寸
                        //   image: images,//绝对路径
                        //   imageSize: new AMap.Size(48, 88)
                        // }),
                        icon: new AMap.Icon({
                            size: new AMap.Size(44, 44), // 图标尺寸
                            image: new URL(
                                "../../../assets/images/poi/monitor1.png",
                                import.meta.url,
                            ).href, //绝对路径
                            imageSize: new AMap.Size(44, 44),
                        }),
                        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-22, -44),
                        extData: type,
                    });
                }
            } else {
                window.markers = new AMap.Marker({
                    // content: '<div class="two-icon-infrastructure"></div>',
                    // icon: new AMap.Icon({
                    //   size: new AMap.Size(48, 88), // 图标尺寸
                    //   image: images,//绝对路径
                    //   imageSize: new AMap.Size(48, 88)
                    // }),
                    icon: images,
                    // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                    position: [item.longitude, item.latitude],
                    offset: new AMap.Pixel(-22, -44),
                    extData: type,
                });
            }

            let infoWindow = new AMap.InfoWindow({
                autoMove: true,
                // 设置信息窗口的偏移量
                offset: new AMap.Pixel(0, -55),
            });
            window.markers.content = iconScreen(item).text;
            console.log(iconScreen(item).text);
            // 添加鼠标移入事件监听器
            window.markers.on("mouseover", (e) => {
                infoWindow.open(window.map1, e.target.getPosition());
                infoWindow.setContent(e.target.content);
                infoWindow.open(window.map1, e.target.getPosition());
                console.log(e);
                if (e.target._opts.extData == "BNCS") {
                    setTimeout((_) => {
                        toDetile();
                    }, 500);
                }
                if (e.target._opts.extData == "ZBJK") {
                    setTimeout((_) => {
                        toVideo();
                    }, 500);
                }
            });

            // 添加鼠标移出事件监听器
            // window.markers.on("mouseout", (e) => {
            //   infoWindow.close();
            // });
            tetsList.value[type].push(window.markers);
            window.markers.setMap(window.map1);
        });
    }

    console.log(tetsList.value["ZBJK"], "保存的数据");
};
// 点位详情
const iconScreen = (item) => {
    console.log(item);
    const boxStyle = `
                padding:8px;
                border: 2px solid #47EBEB;
                text-align: left;
                background-color: rgba(0, 0, 0, 0.5);
                border-radius: 10px;
                line-height: 16px;
                color: #FFFFFF;
                font-size: 12px;
                `;
    const sizeColor = `
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #FFFFFF;
                font-size: 14px;
                `;
    switch (item.resourceType) {
        case "5013002":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>队伍名称：${item.name || ""}</p><p>队伍类型：${
                            this.dict.type.team_type.find(
                                (i) => i.value == item.contingentType,
                            ) == undefined
                                ? "-"
                                : this.dict.type.team_type.find(
                                      (i) => i.value == item.contingentType,
                                  ).label
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>队伍人数：${item.headcount || ""}</p><p>责任人：${
                            item.liabilityUser || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>联系方式：${item.phone || ""}</p>
                      </div>
                    </div>`,
            };
        case "5013003":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>避难所名称：${item.name || ""}</p><p>场所面积：${
                            item.refugeArea || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>容纳人数：${item.holdsNumber || ""}</p><p>负责人：${
                            item.liabilityUser || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        </p><p>联系方式：${item.phone || ""}</p>
                      </div>
                      <div class="detailShelter" data='${JSON.stringify(
                          item,
                      )}'>疏解人员 ></div>
                    </div>`,
            };
        case "5013004":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>设备名称：${item.name || ""}</p><p>设备编号：${
                            item.deviceId || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                        <i class="el-icon-view"></i>
                         <div style="display:${
                             item.isOnline ? "block" : "none"
                         }" class="detailVideo" data='${JSON.stringify(
                             item,
                         )}'>查看视频 ></div>
                      </div>
                    </div>

                                </div>`,
            };
        case "5013005":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>广播设备：${item.name || ""}</p><p>设备编号：${
                            item.deviceId || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                      </div>
                    </div>`,
            };
        case "5013006":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>单位名称：${item.name || ""}</p><p>风险类型：${
                            this.dict.type.risk_type.find(
                                (ele) => ele.value == item.riskType,
                            ) == undefined
                                ? ""
                                : this.dict.type.risk_type.find(
                                      (ele) => ele.value == item.riskType,
                                  ).label
                        }</p>

                      </div>
                      <div class='markerLabel'>
                        <p>风险等级：${
                            this.dict.type.risk_level.find(
                                (ele) => ele.value == item.riskGrade,
                            ) == undefined
                                ? ""
                                : this.dict.type.risk_level.find(
                                      (ele) => ele.value == item.riskGrade,
                                  ).label
                        }</p><p>主要因素：${item.riskFactors || ""}</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                      </div>
                    </div>`,
            };
        case "5013007":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>机构名称：${item.name || ""}</p><p>能力情况：${
                            item.capacityStatus || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                      </div>
                    </div>`,
            };
        case "5013008":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>防护目标名称：${item.name || ""}</p><p>类型：${
                            this.dict.type.target_type.find(
                                (ele) => ele.value == item.targetType,
                            ) == undefined
                                ? ""
                                : this.dict.type.target_type.find(
                                      (ele) => ele.value == item.targetType,
                                  ).label
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                      </div>
                      <div class='markerLabel'>
                        <p>基本情况：${item.basicSituation || ""}</p>
                      </div>
                    </div>`,
            };
        case "5013009":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>机构名称：${item.name || ""}</p><p>能力情况：${
                            item.capacityStatus || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                      </div>
                    </div>`,
            };
        case "5013001":
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>物资库名称：${item.name || ""}</p><p>物资库说明：${
                            item.illustrate || ""
                        }</p>
                      </div>
                      <div class='markerLabel'>
                        <p>责任人：${
                            item.liabilityUser || ""
                        }</p><p>联系方式：${item.phone || ""}</p>
                      </div>
                    </div>`,
            };
        default:
            return {
                text: `<div style="${boxStyle}">
                      <div class='markerLabel'>
                        <p>监测站名称：${item.name || ""}</p>
                      </div>
                      <div class='markerLabel'>
                        <p>经度：${item.longitude || ""}</p><p>纬度：${
                            item.latitude || ""
                        }</p>
                      </div>
                    </div>`,
            };
    }
};
//疏解弹窗
const mitigate = ref(false);
const formMitigate = ref({});
const change = () => {
    proxy.$forceUpdate();
};
const rulesMitigate = ref({});
const toDetile = () => {
    const windowDomList = document.querySelectorAll(".detailShelter");
    console.log(windowDomList);
    windowDomList.forEach((i) => {
        i.onclick = (e) => {
            console.log(e.target.getAttribute("data"));
            const data = JSON.parse(e.target.getAttribute("data"));
            Reflect.set(formMitigate.value, "id", data.id);
            if (propslist.itemList != undefined && propslist.itemList != null) {
                Reflect.set(
                    formMitigate.value,
                    "drillId",
                    propslist.itemList.id,
                );
                Reflect.set(
                    accidentPoint.value,
                    "longitude",
                    propslist.itemList.longitude,
                );
                Reflect.set(
                    accidentPoint.value,
                    "latitude",
                    propslist.itemList.latitude,
                );
                Reflect.set(
                    routeForm.value,
                    "origin",
                    +propslist.itemList.longitude +
                        "," +
                        propslist.itemList.latitude,
                );
            } else {
                Reflect.set(formMitigate.value, "drillId", fireEvent.value.id);
                Reflect.set(
                    accidentPoint.value,
                    "longitude",
                    fireEvent.value.longitude,
                );
                Reflect.set(
                    accidentPoint.value,
                    "latitude",
                    fireEvent.value.latitude,
                );
                Reflect.set(
                    routeForm.value,
                    "origin",
                    +fireEvent.value.longitude + "," + fireEvent.value.latitude,
                );
            }
            Reflect.set(
                routeForm.value,
                "destination",
                +data.longitude + "," + data.latitude,
            );
            console.log("================", data);
            mitigate.value = true;
            proxy.$loading.show();
        };
    });
};
const accidentPoint = ref({
    longitude: null,
    latitude: null,
});
const toVideo = () => {
    const windowDomList1 = document.querySelectorAll(".detailVideo");
    console.log(windowDomList1);
    windowDomList1.forEach((i) => {
        i.onclick = (e) => {
            const data = JSON.parse(e.target.getAttribute("data"));
            console.log(data);
            videoId.value = data.deviceId;
            videoDialogShow.value = true;
            proxy.$loading.show();
        };
    });
};
//疏解路线
const routeForm = ref({
    type: 1,
});
// 疏解人数
const submitMitigate = () => {
    console.log(formMitigate.value);

    dispatchPerson({ ...formMitigate.value }).then((res) => {
        mitigate.value = false;
        proxy.$loading.hide();
        // this.$message.success('疏解成功')
        console.log(routeForm.value);
        // console.log(this.routeForm);
        getPath({ ...routeForm.value }).then((res) => {
            console.log(res.data);
            if (res.data.data.steps && res.data.data.steps.length > 0) {
                console.log("路线", res.data.data.steps);

                let middlePath = [
                    [
                        Number(accidentPoint.value.longitude),
                        Number(accidentPoint.value.latitude),
                    ],
                ];
                res.data.data.steps.forEach((item) => {
                    console.log(item.polyline.split(";"));
                    let resA = [];
                    resA = item.polyline.split(";");
                    for (let i = 0; i < resA.length; i++) {
                        resA[i] = resA[i].split(",");
                        middlePath.push([
                            Number(resA[i][0]),
                            Number(resA[i][1]),
                        ]);
                    }
                    console.log(resA);
                    // middlePath.push(resA)
                });
                let middleP = [
                    Number(accidentPoint.value.longitude),
                    Number(accidentPoint.value.latitude),
                ];
                console.log(middlePath);
                runpolyline(middlePath, middleP, res.data.data.duration);
            }
        });

        // this.mitigateDialog = false
    });
};
const removeLine = () => {
    console.log("window.runMarker", window.runMarker);
    console.log("window.runPolyline", window.runPolyline);
    if (window.runMarker) {
        window.map1.remove(window.runMarker);
        window.runMarker.setMap(null);
        window.runMarker = null;
    }
    if (window.runPolyline) {
        window.map1.remove(window.runPolyline);
        window.runPolyline.setMap(null);
        window.runPolyline = null;
    }
};
//逃生路线
const runpolyline = (lineArr, point, time) => {
    console.log(point);
    console.log(lineArr);

    if (window.runMarker) {
        window.map1.remove(window.runMarker);
    }
    AMap.plugin("AMap.MoveAnimation", function () {});
    window.runMarker = new AMap.Marker({
        map: window.map1,
        position: point,
        icon: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
        offset: new AMap.Pixel(-13, -26),
    });
    window.runMarker.setzIndex(999);
    if (time) {
        var time_s = Number(time);
        var minute = Math.floor(time_s / 60);
        var rest_seconds = time_s % 60;
        window.runMarker.setLabel(null);
        window.runMarker.setLabel({
            content: `预计到达时间:${minute}分钟${rest_seconds}秒`,
            offset: new AMap.Pixel(20, 20),
        });
    }

    // 绘制轨迹
    //   var lineArr = [[115.141349, 37.592625], [115.169869, 37.598825], [115.129055, 37.612463], [115.171404, 37.612256], [115.129055, 37.575697]];
    // console.log(this.runMarker);
    if (window.runPolyline) {
        window.map1.remove(window.runPolyline);
    }
    window.runPolyline = new AMap.Polyline({
        map: window.map1,
        path: lineArr,
        showDir: true,
        strokeColor: "#28F", //线颜色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // label:{
        //   show:true,
        //   content:'111'
        // }
        // strokeStyle: "solid"  //线样式
    });

    var passedPolyline = new AMap.Polyline({
        map: window.map1,
        strokeColor: "#AF5", //线颜色
        strokeWeight: 6, //线宽
        //  label:{
        //   show:true,
        //   content:'111'
        // }
    });

    //   this.runMarker.on('moving', function (e) {
    //     polyline.setPath(e.passedPath);
    //     window.map1.setCenter(e.target.getPosition(),true)
    //   }.bind(this));
    window.runMarker.on("click", (e) => {
        console.log(e, "click--------");
        // startAnimation(lineArr)
        window.runMarker.moveAlong(lineArr, {
            // 每一段的时长
            duration: 50, //可根据实际采集时间间隔设置
            // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
            autoRotation: true,
        });
    });
    //   window.map1.setFitView();
    //   this.startAnimation(lineArr)
};
const startAnimation = (a) => {
    console.log(a);
    window.runMarker.moveAlong(a, {
        // 每一段的时长
        duration: 500, //可根据实际采集时间间隔设置
        // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
        autoRotation: true,
    });
};
const locationPOI = (e) => {
    console.log(e);
    if (e.resourceType == "5013002") {
        ue4({
            Command: "XFHZ",
            val: "AddDW",
            longitude: e.longitude,
            latitude: e.latitude,
        });
    } else if (e.resourceType == "5013001") {
        ue4({
            Command: "XFHZ",
            val: "AddWZ",
            longitude: e.longitude,
            latitude: e.latitude,
        });
    }
};
const changePlan = (e) => {
    getLevelList({
        planId: e,
    }).then((res) => {
        res.data.data.forEach((item) => {
            item.responseName = dicts.response_level.find(
                (ele) => ele.code == item.responseLevel,
            ).name;
        });
        responseLevelList.value = res.data.data;
    });
};
const clearFlags = () => {
    // console.log(tetsList.value,"初始化后进入------------------")
    if (tetsList.value && tetsList.value["_rawValue"]) {
        // console.log(tetsList.value._rawValue,"初始化后进入------------------")
    }
    // tetsList.value["BNCS"].forEach(item=>{
    //   item.setMap(null)
    // })
    // tetsList.value["ZBJK"].forEach(item=>{
    //   item.setMap(null)
    // })
    // tetsList.value["FHMB"].forEach(item=>{
    //   item.setMap(null)
    // })
    // tetsList.value["YLJG"].forEach(item=>{
    //   item.setMap(null)
    // })
    // tetsList.value["FXYH"].forEach(item=>{
    //   item.setMap(null)
    // })
    // tetsList.value["TXBZ"].forEach(item=>{
    //   item.setMap(null)
    // })
};
const toStartPlan = () => {
    getplanList();
    formPrePlan.value = {};
    proxy.$loading.show();
    PrePlan.value = true;
    nextTick(() => {
        startPrePlan.value.style.zIndex = 5;
    });
};
const getplanList = () => {
    togetplanList({ businessTypeId: fireEvent.value.businessTypeId }).then(
        (res) => {
            planList.value = res.data.data;
        },
    );
};
const getFirePeople = () => {
    hxGetCurrentFireWatch().then((res) => {});
};
const getDictionary = () => {
    for (let item in dicts) {
        console.log(item);
        dictionary({ code: item }).then((res) => {
            console.log(res, "res字典项");
            dicts[item] = res.data.data;
        });
    }
    getEmergencyEvent();

    console.log(dicts);
};
const getoverviewList = () => {
    overviewList({ resourceType: 5013001 }).then((res) => {
        // g给ue传入全量数据，ue给返回数据
        touegoods.value = res.data.data.list;
        // 下拉框数据
        if (!chooseDispatch) {
            materialList.value = res.data.data.list;
        }
    });
    overviewList({ resourceType: 5013002 }).then((res) => {
        touetask.value = res.data.data.list;

        if (chooseDispatch) {
            materialList.value = res.data.data.list;
        }
    });
};
const getEmergencyEvent = () => {
    // emergencyEvent({
    //   current: 1,
    //   size: 1,
    // }).then((res) => {
    //   console.log(res,"接口返回数据")
    //   fireEvent.value = res.data.data[0];
    //   console.log(fireEvent.value.eventNo," fireEvent.value")
    // });
    fireEvent.value = propslist.itemList;
};
const doorControl = () => {
    door.value = true;
};
const closeDialogDoor = () => {
    door.value = false;
};
const closeDialogpower = () => {
    power.value = false;
};
const closePower = () => {
    power.value = true;
};
// const clickListButton1 = () => {
//   ue4({ Command: "XFHZ", val: "FANHUI" });
//   emit("clickListButton1", false);
// };
const materialList = ref([]);
const chooseTeam = () => {
    chooseDispatch.value = true;
    //   //proxy.$loading.show()
    materialList.value = touetask.value;
};
const chooseSupplies = () => {
    chooseDispatch.value = false;
    //   //proxy.$loading.hide()
    materialList.value = touegoods.value;
};
const chooseTeamtask = () => {
    chooseDispatchtask.value = true;
};
const toaddTask = (e) => {
    console.log("/////////////////////////", addTask.value);

    console.log(e);
    sendrankFrom.value = {};
    sendgoodFrom.value = {};
    if (e.resourceType != undefined) {
        showTopButton.value = false;
        addTask.value = true;
        proxy.$loading.show();
        if (e.resourceType == "5013001") {
            // 物资
            supplyOption().then((res) => {
                console.log(res, "物资");
                depotOptions.value = res.data.data;
                chooseDispatchtask.value = false;
                sendgoodFrom.value.depotId = e.id.toString();
                goodDisabled.value = true;
                listOfDepot({ supplyDepotId: e.id }).then((res) => {
                    goodsData.value = res.data.data;
                });
            });
        } else if (e.resourceType == "5013002") {
            // 队伍
            TaskOption().then((res) => {
                ranksOptions.value = res.data.data;
                rankDisabled.value = true;
                chooseDispatchtask.value = true;
                sendrankFrom.value.contingentId = e.id.toString();
            });
        }
    } else {
        TaskOption().then((res) => {
            ranksOptions.value = res.data.data;
        });
        supplyOption().then((res) => {
            depotOptions.value = res.data.data;
        });
        showTopButton.value = true;
        chooseDispatchtask.value = true;
        goodDisabled.value = false;
        rankDisabled.value = false;
        addTask.value = true;
        proxy.$loading.show();
        nextTick(() => {
            console.log(addtaskDialogRef.value.style);
            addtaskDialogRef.value.style.zIndex = 5;
        });
    }
};
const chooseSuppliestask = () => {
    chooseDispatchtask.value = false;
};
const totextMessage = () => {
    textMessage.value = true;
};
const closeDialogtextMessage = () => {
    textMessage.value = false;
};
const closeDialog = () => {
    console.log("点击按钮");
    command.value = false;
    // proxy.$loading.hide()
    emit("clickListButton1", false, tetsList);
};
const closeDialog1 = () => {
    console.log("点击按钮");
    videoDialogShow.value = false;
    proxy.$loading.hide();
    // emit("clickListButton1", false, tetsList);
};
const reportcloseDialog = () => {
    // console.log("点击按钮")
    reporttype.value = false;
    proxy.$loading.hide();
    // emit("clickListButton1", false,tetsList);
};

const closeDialogRecord = () => {
    proxy.$loading.hide();
    record.value = false;
};
const closeDialogImpact = () => {
    proxy.$loading.hide();
    impact.value = false;
};
const closeDialogtask = () => {
    proxy.$loading.hide();
    addTask1.value = false;
};
const impactFactor = () => {
    console.log(impact, "impact进入");
    formImpact.value = {
        temperature: "",
        humidity: "",
        windScale: "",
        windSpeed: "",
        slope: "",
        windDirection: "",
        time: "",
        longitude: propslist.itemList.longitude,
        latitude: propslist.itemList.latitude,
        analysisType: 1,
        analysisModel: 2,
    };
    Reflect.set(formImpact.value, "temperature", nowWea.value.temperature);
    Reflect.set(formImpact.value, "humidity", nowWea.value.humidity);
    Reflect.set(formImpact.value, "windScale", nowWea.value.windScale);
    if (nowWea.value && nowWea.value.windDirection) {
        let xDir = nowWea.value.windDirection + "风";
        console.log(dicts.fire_wind.find((i) => i.name == xDir));
        if (dicts.fire_wind.find((i) => i.name == xDir) != undefined) {
            let dir = dicts.fire_wind.find((i) => i.name == xDir).code;
            console.log(dir);
            Reflect.set(formImpact.value, "windDirection", dir);
        }
    }
    proxy.$loading.show();
    impact.value = true;
};
const reportdloag = () => {
    reportDetails({ id: propslist.itemList.id }).then((res) => {
        console.log(res, "事故报告", dicts.event_level);
        dicts.event_level.forEach((items) => {
            console.log(items, "每一项");
            if (res.data.data.eventLevel == items.code) {
                res.data.data.eventLevel = items.name;
                console.log(
                    res.data.data.eventLevel,
                    "res.data.data.eventLevel",
                );
            }
        });
        reporList.value = res.data.data;
        proxy.$loading.show();
        reporttype.value = true;
    });
};
const dictGetDict = () => {
    getDict({
        type: "KS",
    }).then((res) => {
        KS.value = res.data.data;
    });
    getDict({
        type: "KW",
    }).then((res) => {
        KW.value = res.data.data;
    });
};
//初始点位
const initialPoint = ref({
    longitude: 115.128757,
    latitude: 37.611534,
});
//经度-横向一格-42米-0.00039
const longAdd = ref(0.0004);
//维度-竖向一米-42米-0.00037
const latAdd = ref(0.00037);
const submitImpact = async (formEl) => {
    let x = parseInt(
        (propslist.itemList.longitude - initialPoint.value.longitude) /
            longAdd.value,
    );
    let y = parseInt(
        (initialPoint.value.latitude - propslist.itemList.latitude) /
            latAdd.value,
    );
    Reflect.set(formImpact.value, "x", x);
    Reflect.set(formImpact.value, "y", y);
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            analysisdecision({
                ...formImpact.value,
            }).then((res) => {
                proxy.$loading.hide();
                impact.value = false;
                command.value = false;
                emit("clickListButton1", false, tetsList);
                // C传给ue
                // let toue = {
                //   ...res.data,
                //   kd: formImpact.value.kd,
                //   longitude: fireEvent.value.longitude,
                //   latitude: fireEvent.value.latitude,
                // };
                // ue4({ Command: "XFHZ", val: "AddYXYZ", data: toue });
                if (formImpact.value.analysisType == 1) {
                    //   clickFire(res.data.data)
                    let saveData = [];
                    if (
                        res.data.success &&
                        res.data.data.spreadModel &&
                        res.data.data.spreadModel.length
                    ) {
                        saveData = res.data.data.spreadModel.map((x) => {
                            return {
                                lnglat: x.split(","),
                            };
                        });
                        console.log(saveData);
                        clickMass(saveData);
                    }
                } else if (formImpact.value.analysisType == 2) {
                    clickExplosion(res.data.data);
                } else {
                    //   clickGas(res.data.data)
                    let saveData = [];
                    if (
                        res.data.success &&
                        res.data.data.spreadModel &&
                        res.data.data.spreadModel.length
                    ) {
                        saveData = res.data.data.spreadModel.map((x) => {
                            return {
                                lnglat: x.split(","),
                            };
                        });
                        console.log(saveData);
                        clickMass(saveData);
                    }
                }
            });
        } else {
            console.log("error submit!", fields);
        }
    });
};
const nowWea = ref(null);
//实时气象
const nowWeather = () => {
    var weather = new AMap.Weather();
    console.log("天气", weather);
    //查询实时天气信息, 查询的城市到行政级别的城市，如朝阳区、杭州市
    weather.getLive("宁晋县", (err, data) => {
        console.log("测试天气", err, data);
        if (!err) {
            console.log(data);
            nowWea.value = data;
            let middle = data.windPower.match(/\d+/g).toString();
            Reflect.set(nowWea.value, "windScale", middle);
            console.log(nowWea.value.windDirection + "风");
            let xDir = nowWea.value.windDirection + "风";
        }
    });
};
const clickMass = (valData) => {
    console.log(valData);
    // valData.forEach(el=>{
    //   let lng = Number(el.lnglat[0])
    //   let lat = Number(el.lnglat[1])
    //   let path = [
    //     [lng+0.0002,lat+0.000185],
    //     [lng+0.0002,lat-0.000185],
    //     [lng-0.0002,lat-0.000185],
    //     [lng-0.0002,lat+0.000185],
    //   ]
    //   var dealPolygonArr = []
    //   var polygon = new AMap.Polygon({
    //     path: path,
    //     fillColor: '#fa2948',
    //     fillOpacity: 0.3,
    //     strokeColor: '#fa2948',
    //     strokeOpacity: 0,
    //     zIndex: -99,
    //   })
    //   // var label = this.showText(polygon, el.name);
    //   dealPolygonArr.push(polygon)
    //   this.polygonArr = dealPolygonArr
    //   window.map1.add(this.polygonArr);
    // })
    var style = [
        {
            url: "https://webapi.amap.com/images/mass/mass0.png",
            anchor: new AMap.Pixel(6, 6),
            size: new AMap.Size(11, 11),
            zIndex: 3,
        },
        {
            url: "https://webapi.amap.com/images/mass/mass1.png",
            anchor: new AMap.Pixel(4, 4),
            size: new AMap.Size(7, 7),
            zIndex: 2,
        },
        {
            url: "https://webapi.amap.com/images/mass/mass2.png",
            anchor: new AMap.Pixel(3, 3),
            size: new AMap.Size(5, 5),
            zIndex: 1,
        },
    ];
    let massAllPoints = new AMap.MassMarks(valData, {
        opacity: 0.8,
        zIndex: 111,
        cursor: "pointer",
        style: style,
    });
    console.log(massAllPoints);
    window.massAllPoints = massAllPoints;
    massAllPoints.setMap(window.map1);
    //   window.map1.add(massAllPoints);
    window.map1.setFitView();
};
const clickGas = (val) => {
    // var center = new AMap.LngLat(this.long, this.lat);
    var center = new AMap.LngLat(
        propslist.itemList.longitude,
        propslist.itemList.latitude,
    );
    console.log(center, "圆点");
    console.log(val.diffusionR, "半径");
    var radius = val.diffusionR;
    var circlefour = new AMap.Circle({
        center: center, //圆心
        radius: radius, //半径
        borderWeight: 1,
        strokeColor: "#b8741b",
        strokeOpacity: 1,
        strokeWeight: 6,
        fillOpacity: 0.4,
        strokeStyle: "solid",
        strokeDasharray: [10, 10],
        fillColor: "#b8741b",
        zIndex: 50,
    });
    let bounds = circlefour.getBounds();
    let point_lon = bounds.northEast.lng,
        point_lat = (bounds.northEast.lat + bounds.southWest.lat) / 2;
    let path = [circlefour.getCenter(), new AMap.LngLat(point_lon, point_lat)];
    var polyline = new AMap.Polyline({
        path: path,
        strokeWeight: 1.2,
        strokeColor: "#FFE000",
        strokeStyle: "dashed",
        strokeDasharray: [5, 5],
    });
    let text_lon = (circlefour.getCenter().lng + point_lon) / 2;
    let text_lat = point_lat;
    var text = new AMap.Text({
        text: radius + "m",
        anchor: "bottom-center", // 设置文本标记锚点
        style: {
            "background-color": "transparent",
            border: "none",
            "font-size": "12px",
            color: "#ffffff",
        },
        position: [text_lon, text_lat],
    });
    // window.map1.add([circle, polyline, text]);
    let overlays = [circlefour, polyline, text];
    window.overlays = overlays;
    window.map1.add(overlays);
    emit("removeList", overlays);
    window.map1.setFitView();
};
const clickExplosion = (val) => {
    console.log("baozha", val);
    // const center = new AMap.LngLat(this.long, this.lat);
    const center = new AMap.LngLat(
        propslist.itemList.longitude,
        propslist.itemList.latitude,
    );
    const blowData = [
        {
            radius: val.deathR,
            title: "",
        },
        {
            radius: val.seriousWoundR,
            title: "重伤",
        },
        {
            radius: val.slightWoundR,
            title: "轻伤",
        },
        {
            radius: val.propertyLossR,
            title: "财损",
        },
    ];
    blowData.sort((a, b) => {
        return a.radius - b.radius;
    });
    const color = ["#d38b1d", "#e2ac5f", "#e2cfa1", "#e9efcf"];
    window.circleone = [];
    blowData.forEach((item, index) => {
        var circleone = new AMap.Circle({
            center: center, //圆心
            radius: item.radius, //半径
            borderWeight: 1,
            strokeColor: "#b8741b",
            strokeOpacity: 1,
            strokeWeight: 1,
            fillOpacity: 0.4,
            strokeStyle: "solid",
            strokeDasharray: [10, 10],
            fillColor: color[index],
            zIndex: 50 - index,
        });
        console.log(circleone, "圆形对象");
        let bounds = circleone.getBounds();
        console.log(bounds, "找数据");
        let point_lon = bounds.northEast.lng;
        let point_lat = (bounds.northEast.lat + bounds.southWest.lat) / 2;
        let path = [
            circleone.getCenter(),
            new AMap.LngLat(point_lon, point_lat),
        ];
        let text_lon = (circleone.getCenter().lng + point_lon) / 2;
        let text_lat = point_lat;
        circleone.on("mouseover", (e) => {
            // var infoWindow = new AMap.InfoWindow({
            window.infoWindow = new AMap.InfoWindow({
                position: [text_lon, text_lat],
                offset: new AMap.Pixel(0, -40),
                content: `${item.title}半径R${index}=${item.radius}m`,
            });
            // infoWindow.open(window.map1);
            window.infoWindow.open(window.map1);
        });
        // window.map1.add(circleone);

        window.circleone.push(circleone);
        window.map1.add(circleone);
        // circleone.setMap(window.map1)
        emit("removeList", circleone);
    });
    window.map1.setFitView();
};
const clickFire = (val) => {
    const speed = val.spreadV;
    console.log(speed, "speed");
    // const sin = Math.sin(this.form.kd * Math.PI / 180)
    // const cos = Math.cos(this.form.kd * Math.PI / 180)
    const sin = Math.sin((formImpact.value.kd * Math.PI) / 180);
    const cos = Math.cos((formImpact.value.kd * Math.PI) / 180);
    console.log(sin, "sin", cos, "cos");
    const perimeter = 2 * Math.PI * 6371000;
    console.log(perimeter, "perimeter");
    // const perimeter_latitude = perimeter * Math.cos(Math.PI * this.lat / 180)
    const perimeter_latitude =
        perimeter * Math.cos((Math.PI * propslist.itemList.latitude) / 180);
    console.log(perimeter_latitude, "perimeter_latitude");
    const longitude_per_mi = 360 / perimeter_latitude;
    const latitude_per_mi = 360 / perimeter;
    console.log(
        longitude_per_mi,
        "longitude_per_mi",
        latitude_per_mi,
        "latitude_per_mi",
    );
    const blowData = [
        {
            radius: (speed * 5) / 2,
            center: (speed * 5) / 2,
            title: "5",
        },
        {
            radius: speed * 5,
            center: speed * 10,
            title: "15",
        },
        {
            radius: speed * 10,
            center: speed * 25,
            title: "35",
        },
    ];
    const subsidiaryData = [
        {
            radius: (speed * 15) / 2,
        },
        {
            radius: (speed * 35) / 2,
        },
    ];
    const color = ["#ba761e", "#f59a23", "#f9cb8f"];
    console.log(blowData, "blowData", subsidiaryData, "subsidiaryData");
    window.circletwo = [];
    blowData.forEach((item, index) => {
        console.log(
            propslist.itemList.longitude + item.center * cos * longitude_per_mi,
            propslist.itemList.latitude + item.center * sin * latitude_per_mi,
            "第一个中心点",
        );
        console.log(
            (propslist.itemList.longitude * 1 + item.center * 1) *
                cos *
                longitude_per_mi,
            propslist.itemList.latitude + item.center * sin * latitude_per_mi,
            "第一个中心点--1",
        );
        var circletwo = new AMap.Circle({
            //center: new AMap.LngLat(this.long + item.center * cos * longitude_per_mi, this.lat + item.center * sin * latitude_per_mi), //圆心
            center: new AMap.LngLat(
                propslist.itemList.longitude * 1 +
                    item.center * cos * longitude_per_mi,
                propslist.itemList.latitude * 1 +
                    item.center * sin * latitude_per_mi,
            ), //圆心
            radius: item.radius, //半径
            borderWeight: 1,
            strokeColor: "#b8741b",
            strokeOpacity: 1,
            strokeWeight: 1,
            fillOpacity: 0.4,
            strokeStyle: "solid",
            strokeDasharray: [10, 10],
            fillColor: color[index],
            zIndex: 50 - index,
        });
        let bounds = circletwo.getBounds();
        let point_lon = bounds.northEast.lng,
            point_lat = (bounds.northEast.lat + bounds.southWest.lat) / 2;
        let path = [
            circletwo.getCenter(),
            new AMap.LngLat(point_lon, point_lat),
        ];
        let text_lon = (circletwo.getCenter().lng + point_lon) / 2;
        let text_lat = point_lat;
        circletwo.on("mouseover", (e) => {
            // var infoWindow = new AMap.InfoWindow({
            window.infoWindowone = new AMap.InfoWindow({
                position: [text_lon, text_lat],
                offset: new AMap.Pixel(0, -40),
                content: `${item.title}分钟半径R${index}=${item.radius}m`,
            });
            // infoWindow.open(window.map1);
            window.infoWindowone.open(window.map1);
        });
        // window.map1.add(circletwo);
        window.circletwo.push(circletwo);
        window.map1.add(circletwo);
    });
    window.circletwoone = [];
    subsidiaryData.forEach((item, index) => {
        console.log(
            propslist.itemList.longitude + item.radius * cos * longitude_per_mi,
            propslist.itemList.latitude + item.radius * sin * latitude_per_mi,
            "第二个中心点",
        );
        console.log(
            (propslist.itemList.longitude * 1 + item.radius * 1) *
                cos *
                longitude_per_mi,
            propslist.itemList.latitude + item.radius * sin * latitude_per_mi,
            "第二个中心点--1",
        );
        var circletwo = new AMap.Circle({
            //center: new AMap.LngLat(this.long + item.radius * cos * longitude_per_mi, propslist.itemList.latitude + item.radius * sin * latitude_per_mi), //圆心
            center: new AMap.LngLat(
                propslist.itemList.longitude * 1 +
                    item.radius * cos * longitude_per_mi,
                propslist.itemList.latitude * 1 +
                    item.radius * sin * latitude_per_mi,
            ), //圆心
            radius: item.radius, //半径
            borderWeight: 1,
            strokeColor: "#b8741b",
            strokeOpacity: 1,
            strokeWeight: 1,
            fillOpacity: 0,
            strokeStyle: "dashed",
            strokeDasharray: [10, 10],
            fillColor: "transfer",
            zIndex: 0,
        });
        // window.map1.add(circle);
        window.map1.add(circletwo);
        // testmap.value = circletwo
        window.circletwoone.push(circletwo);
    });
    emit("removeList", circletwo);
    window.map1.setFitView();
};
//队伍点位
const ranksPoint = ref([]);
//队伍调度路线
const ranksRouteForm = ref({ type: 1 });
const goodsPoint = ref([]);
//队伍调度路线
const goodsRouteForm = ref({ type: 1 });
const submitaddtask = async (formEl) => {
    console.log("[[[[[[[[[[[[[[[]]]]]]]]]]]]]]]", fireEvent.value);
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            if (chooseDispatchtask.value == true) {
                if (ranksRadio.value == "1") {
                    const now = new Date();

                    const year = now.getFullYear();
                    const month = ("0" + (now.getMonth() + 1)).slice(-2);
                    const day = ("0" + now.getDate()).slice(-2);
                    const hours = ("0" + now.getHours()).slice(-2);
                    const minutes = ("0" + now.getMinutes()).slice(-2);
                    const seconds = ("0" + now.getSeconds()).slice(-2);

                    const formattedTime =
                        year +
                        "-" +
                        month +
                        "-" +
                        day +
                        " " +
                        hours +
                        ":" +
                        minutes +
                        ":" +
                        seconds;
                    console.log(formattedTime);

                    Reflect.set(
                        sendrankFrom.value,
                        "finishTime",
                        formattedTime,
                    );
                }

                saveTask({
                    businessTypeId: propslist.itemList.businessTypeId,
                    contingentVo: sendrankFrom.value,
                    taskType: 5012401,
                }).then((res) => {
                    proxy.$loading.hide();
                    addTask.value = false;
                    if (dispatch.value == true) {
                        gettemporaryList();
                    } else {
                        if (ranksRadio.value == "1") {
                            if (
                                propslist.itemList != undefined &&
                                propslist.itemList != null
                            ) {
                                Reflect.set(
                                    ranksRouteForm.value,
                                    "destination",
                                    +propslist.itemList.longitude +
                                        "," +
                                        propslist.itemList.latitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "longitude",
                                    propslist.itemList.longitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "latitude",
                                    propslist.itemList.latitude,
                                );
                            } else {
                                Reflect.set(
                                    ranksRouteForm.value,
                                    "destination",
                                    +fireEvent.value.longitude +
                                        "," +
                                        fireEvent.value.latitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "longitude",
                                    fireEvent.value.longitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "latitude",
                                    fireEvent.value.latitude,
                                );
                            }

                            getPath({ ...ranksRouteForm.value }).then((res) => {
                                if (
                                    res.data.data.steps &&
                                    res.data.data.steps.length > 0
                                ) {
                                    console.log(res.data.data.steps);
                                    console.log("///////////**************");
                                    let middlePath = [];
                                    middlePath.push(ranksPoint.value);

                                    res.data.data.steps.forEach((item) => {
                                        console.log(item.polyline.split(";"));
                                        let resA = [];
                                        resA = item.polyline.split(";");
                                        for (let i = 0; i < resA.length; i++) {
                                            resA[i] = resA[i].split(",");
                                            middlePath.push([
                                                Number(resA[i][0]),
                                                Number(resA[i][1]),
                                            ]);
                                            // middlePath.push([Number(resA[i][0])+'', Number(resA[i][1])+''])
                                        }
                                        console.log(resA, "resA");
                                        // middlePath.push(resA)
                                    });
                                    console.log(accidentPoint.value.longitude);
                                    middlePath.push([
                                        Number(accidentPoint.value.longitude),
                                        Number(accidentPoint.value.latitude),
                                    ]);
                                    // middlePath.push([Number(accidentPoint.value.longitude)+'', Number(accidentPoint.value.latitude)+''])

                                    console.log(middlePath, "middlePath");
                                    runpolyline(
                                        middlePath,
                                        ranksPoint.value,
                                        res.data.data.duration,
                                    );
                                }
                            });
                        }
                    }
                });
            } else {
                if (goodsRadio.value == "1") {
                    const now = new Date();

                    const year = now.getFullYear();
                    const month = ("0" + (now.getMonth() + 1)).slice(-2);
                    const day = ("0" + now.getDate()).slice(-2);
                    const hours = ("0" + now.getHours()).slice(-2);
                    const minutes = ("0" + now.getMinutes()).slice(-2);
                    const seconds = ("0" + now.getSeconds()).slice(-2);

                    const formattedTime =
                        year +
                        "-" +
                        month +
                        "-" +
                        day +
                        " " +
                        hours +
                        ":" +
                        minutes +
                        ":" +
                        seconds;
                    console.log(formattedTime);

                    Reflect.set(
                        sendgoodFrom.value,
                        "finishTime",
                        formattedTime,
                    );
                }
                console.log(goodsData.value);
                sendgoodFrom.value.materialVos = [];
                goodsData.value.forEach((item) => {
                    if (item) {
                        if (
                            item.dispatchQuantity != undefined &&
                            item.dispatchQuantity != ""
                        ) {
                            sendgoodFrom.value.materialVos.push({
                                materialId: item.id,
                                dispatchQuantity: item.dispatchQuantity,
                            });
                        }
                    }
                });
                saveTask({
                    businessTypeId: propslist.itemList.businessTypeId,
                    depotVo: sendgoodFrom.value,
                    taskType: 5012402,
                }).then((res) => {
                    proxy.$loading.hide();
                    addTask.value = false;
                    if (dispatch.value == true) {
                        gettemporaryList();
                    } else {
                        if (goodsRadio.value == "1") {
                            if (
                                propslist.itemList != undefined &&
                                propslist.itemList != null
                            ) {
                                Reflect.set(
                                    goodsRouteForm.value,
                                    "destination",
                                    +propslist.itemList.longitude +
                                        "," +
                                        propslist.itemList.latitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "longitude",
                                    propslist.itemList.longitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "latitude",
                                    propslist.itemList.latitude,
                                );
                            } else {
                                Reflect.set(
                                    goodsRouteForm.value,
                                    "destination",
                                    +fireEvent.value.longitude +
                                        "," +
                                        fireEvent.value.latitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "longitude",
                                    fireEvent.value.longitude,
                                );
                                Reflect.set(
                                    accidentPoint.value,
                                    "latitude",
                                    fireEvent.value.latitude,
                                );
                            }

                            getPath({ ...goodsRouteForm.value }).then((res) => {
                                if (
                                    res.data.data.steps &&
                                    res.data.data.steps.length > 0
                                ) {
                                    console.log(res.data.data.steps);
                                    console.log("///////////**************");
                                    let middlePath = [];
                                    middlePath.push(goodsPoint.value);

                                    res.data.data.steps.forEach((item) => {
                                        console.log(item.polyline.split(";"));
                                        let resA = [];
                                        resA = item.polyline.split(";");
                                        for (let i = 0; i < resA.length; i++) {
                                            resA[i] = resA[i].split(",");
                                            middlePath.push([
                                                Number(resA[i][0]),
                                                Number(resA[i][1]),
                                            ]);
                                            // middlePath.push([Number(resA[i][0])+'', Number(resA[i][1])+''])
                                        }
                                        console.log(resA, "resA");
                                        // middlePath.push(resA)
                                    });
                                    console.log(accidentPoint.value.longitude);
                                    middlePath.push([
                                        Number(accidentPoint.value.longitude),
                                        Number(accidentPoint.value.latitude),
                                    ]);
                                    // middlePath.push([Number(accidentPoint.value.longitude)+'', Number(accidentPoint.value.latitude)+''])

                                    console.log(middlePath, "middlePath");
                                    runpolyline(
                                        middlePath,
                                        goodsPoint.value,
                                        res.data.data.duration,
                                    );
                                }
                            });
                        }
                    }
                });
            }
            console.log("submit!");
        } else {
            console.log("error submit!", fields);
        }
    });
};
const submitaddtask1 = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            sendrankFromPai.value.contingentStatus = "5011902";

            updateTaskStatus(sendrankFromPai.value).then((res) => {
                proxy.$loading.hide()
                addTask1.value = false;
                toShowProcess(choosePlan.value);
            });

            console.log("submit!");
        } else {
            console.log("error submit!", fields);
        }
    });
};
const submitaddtask2 = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log(1111);
            saveResponse({
                businessTypeId: fireEvent.value.businessTypeId,
                responseLevelId: formPrePlan.value.responseLevelId,
            }).then((res) => {
                proxy.$loading.hide();
                PrePlan.value = false;
                getshowPlan();
            });

            console.log("submit!");
        } else {
            console.log("error submit!", fields);
        }
    });
};
const toRecord = () => {
    recordDetile({
        businessTypeId: fireEvent.value.businessTypeId,
    }).then((res) => {
        proxy.$loading.show();
        record.value = true;
        recordList.value = res.data.data;
    });
};
const toRoom = () => {
    location.href = "https://" + window.location.hostname + ":13004";
};
const todispatch = () => {
    dispatchMidList.value = [];
    dispatchMidRightList.value = [];
    getshowPlan();
    gettemporaryList();
    dispatch.value = true;
    //   proxy.$loading.show()
    chooseplanResponse.value = true;
};
proxy.$bus.off("clickPlan");
proxy.$bus.on("clickPlan", (val) => {
    console.log("clickPlan-----------------------", val);
    fireEvent.value = val;
    itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);
    todispatch();
    planResponse();
});
proxy.$bus.off("rankPOI");
proxy.$bus.on("rankPOI", (val) => {
    console.log("rankPOI-----------------------", val);
    Reflect.set(
        ranksRouteForm.value,
        "origin",
        +val.longitude + "," + val.latitude,
    );
    ranksPoint.value = [Number(val.longitude), Number(val.latitude)];
});
proxy.$bus.off("goodsPOI");
proxy.$bus.on("goodsPOI", (val) => {
    console.log("goodsPOI-----------------------", val);
    Reflect.set(
        goodsRouteForm.value,
        "origin",
        +val.longitude + "," + val.latitude,
    );
    goodsPoint.value = [Number(val.longitude), Number(val.latitude)];
});
proxy.$bus.off("clickResources");
proxy.$bus.on("clickResources", (val) => {
    console.log("clickResources-----------------------", val);
    fireEvent.value = val;
    itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);
    todispatch();
    temporaryAssignments();
});
proxy.$bus.off("clickToRecord");
proxy.$bus.on("clickToRecord", (val) => {
    console.log("clickToRecord-----------------------", val);
    fireEvent.value = val;
    itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);

    toRecord();
});
proxy.$bus.off("clickReportdloag");
proxy.$bus.on("clickReportdloag", (val) => {
    console.log("clickReportdloag-----------------------", val);
    fireEvent.value = val;
    itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);

    reportdloag();
});
proxy.$bus.off("clickImpactFactor");
proxy.$bus.on("clickImpactFactor", (val) => {
    console.log("clickImpactFactor-----------------------", val);
    console.log(window.circletwo);
    if (
        window.circletwo != [] &&
        window.circletwo != undefined &&
        window.circletwo != null
    ) {
        window.circletwo.forEach((r) => {
            window.map1.remove(r);
        });
        window.circletwo = [];
    }
    if (
        window.circletwoone != [] &&
        window.circletwoone != undefined &&
        window.circletwoone != null
    ) {
        window.circletwoone.forEach((r) => {
            window.map1.remove(r);
        });
        window.circletwoone = [];
    }
    if (
        window.circleone != [] &&
        window.circleone != undefined &&
        window.circleone != null
    ) {
        window.circleone.forEach((r) => {
            window.map1.remove(r);
        });
        window.circleone = [];
    }
    if (window.overlays) {
        window.map1.remove(window.overlays);
    }
    if (window.massAllPoints) {
        window.map1.remove(window.massAllPoints);
        window.massAllPoints.setMap(null);
        window.massAllPoints = null;
    }
    fireEvent.value = val;
    itemisshow.value = isWithin10Minutes(fireEvent.value.createTime);

    impactFactor();
});
proxy.$bus.off("setDistance");
proxy.$bus.on("setDistance", (val) => {
    console.log("clickImpactFactor-----------------------", val);
    toUeInit();
    radius.value = val;
    checkList.value = [];
    changeRadius(val);
});
proxy.$bus.on("clickChangeCheck", (val) => {
    console.log("clickChangeCheck-----------------------", val);
    checkList.value = val;
    toUeInit();
    changeRadius(radius.value);
    // changeCheck(val)
});
proxy.$bus.off("clickToaddTask");
proxy.$bus.on("clickToaddTask", (val) => {
    console.log("clickToaddTask-----------------------", val);
    toaddTask(val);
});
// proxy.$bus.off("attach");
// proxy.$bus.on('attach', (val) => {
//     console.log('attach-----------------------',val);
//     fireEvent.value = val
//     itemisshow.value = isWithin10Minutes(fireEvent.value.createTime)

//     // impactFactor()
// })

onUnmounted(() => {
    proxy.$bus.off("clickToRecord");
    proxy.$bus.off("fireEvent");
});
const getshowPlan = () => {
    showPlan({
        businessTypeId: fireEvent.value.businessTypeId,
    }).then((res) => {
        dispatchCardList.value = res.data.data;
    });
};
const gettemporaryList = () => {
    temporaryList({
        businessTypeId: fireEvent.value.businessTypeId,
    }).then((res) => {
        res.data.data.forEach((item) => {
            console.log(
                JSON.stringify(item.depotVo),
                JSON.stringify(item.contingentVo),
            );
            if (item.hasOwnProperty("depotVo")) {
                console.log(111);
                item = Object.assign(item, item.depotVo);
                console.log(item);
            } else if (item.hasOwnProperty("contingentVo")) {
                console.log(222);
                item = Object.assign(item, item.contingentVo);
                console.log(item);
            }
        });
        console.log(res.data);
        temporaryData.value = res.data.data;
    });
};
const planResponse = () => {
    proxy.$loading.show();
    chooseplanResponse.value = true;
};
const temporaryAssignments = () => {
    proxy.$loading.show();
    chooseplanResponse.value = false;
};
const closedispatchDialog = () => {
    proxy.$loading.hide();
    dispatch.value = false;
};
const toShowProcess = (e) => {
    choosePlan.value = e;
    showProcess({
        responseLevelId: e.responseLevelId,
        businessTypeId: e.businessTypeId,
    }).then((res) => {
        dispatchMidList.value = res.data.data;
    });
    showProcessRight({
        responseLevelId: e.responseLevelId,
        businessTypeId: e.businessTypeId,
    }).then((res) => {
        dispatchMidRightList.value = res.data.data;
    });
};
const processUpdate = (id, status) => {
    // status5011802:开始，5011803：结束
    updatePlanStatus({
        id: id,
        processStatus: status,
    }).then((res) => {
        toShowProcess(choosePlan.value);
    });
};
const useTask = (e) => {
    sendrankFromPai.value = {};
    console.log(e);
    TaskOption().then((res) => {
        ranksOptions.value = res.data.data;
            proxy.$loading.show();

        rankDisabled.value = false;
        chooseDispatchtask.value = true;

        sendrankFromPai.value.id = e.id;
        if(e.contingentId){
        sendrankFromPai.value.contingentId = e.contingentId.toString();

        }
    });
    addTask1.value = true;
    nextTick(() => {
        console.log(TaskandGoods.value);
        TaskandGoods.value.style.zIndex = 2;
    });
};
const finishTask = (e) => {
    updateTaskStatus({
        contingentId: e.contingentId,
        id: e.id,
        contingentStatus: "5011903",
    }).then((res) => {
        toShowProcess(choosePlan.value);
    });
};
const changeJob = (a, b) => {
    updatetemporaryJob({
        taskStatus: b,
        id: a.id,
    }).then((res) => {
        gettemporaryList();
    });
};
const depotChange = (e) => {
    listOfDepot({ supplyDepotId: e }).then((res) => {
        goodsData.value = res.data.data;
    });
};
const toendRescue = () => {
    console.log("===========");
    saveRecordEnd({
        businessTypeId: fireEvent.value.businessTypeId,
    })
        .then((res) => {
            showTip.value = false;

            dispatch.value = false;
            proxy.$loading.hide();
        })
        .catch((ree) => {
            showTip.value = true;
        });
};
</script>
<!-- <style lang="less" scoped>
  .title_image{
    width: 12px;
    height: 12px;
    margin: auto 4px auto 16px;
  }
:root {
  --picker_body_self: rgba(51, 218, 255, 0.15);
  --picker_text: #fff;
  --el-bg-color-loading: rgba(51, 218, 255, 0.15);
  --button_text_color_primary:#409eff;
}
.select-popper {
  // z-index: 1000002 !important;
  // top: auto !important;
  // left: auto !important;
  background: rgba(51, 218, 255, 0.15) !important;
  border: 1px solid #33daff !important;
  color: #fff;
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    color: #fff !important;
    background: var(--picker_body_self) !important;
  }
  .el-select-dropdown__item {
    color: #fff !important;
    background: var(--picker_body_self) !important;
  }
  .el-tree {
    background: var(--picker_body_self) !important;
  }
  .el-tree-node__expand-icon {
    // color: #fff !important;
  }
  .el-tree-node__content:hover {
    background: var(--picker_body_self) !important;
  }
  .el-time-spinner__item {
    color: var(--picker_text);
  }

}
 .datepopper1 {
    background: rgba(51, 218, 255, 0.15) !important;
    border: 1px solid #33daff !important;
    .el-picker-panel {
      color: #fff !important;
      background: var(--picker_body_self) !important;
      .el-picker-panel__footer {
        background: var(--picker_body_self) !important;

      }
    }

    .el-date-picker__header-label {
      color: var(--picker_text);
    }
    button {
      color: var(--button_text_color_primary) !important;
    }
    .el-date-table th {
      color: var(--picker_text);
    }
    .el-input__wrapper {
      background-color: var(--picker_body_self) !important;
    }
   .el-input__inner{
    color:var(--picker_text) ;
   }
   .el-time-spinner__item{
    color:var(--picker_text) ;

   }
   .el-select .el-input .el-select__caret{
    color:var(--picker_text) ;

   }
  }
</style> -->
<style scoped>
.eventList {
    position: absolute;
    top: 144px;
    left: 1016px;
    width: 143px;
    height: 28px;
    background: url("@/assets/images/card/index2_10.svg") no-repeat;
    background-size: cover;
    padding-left: 54px;
    color: #ebebeb;
    font-size: 14px;
    font-weight: 400;
    line-height: 27px;
    box-sizing: border-box;
    /* 157.143% */
}

.card1 {
    display: flex;
    width: 448px;
    height: 896px;
    padding-bottom: 0px;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

/* .card_title1 {
    width: 448px;
    height: 40px;
    flex-shrink: 0;
    background: url("@/assets/images/card/card_title_bg.svg") no-repeat;
    background-size: cover;
    padding-left: 35px;
    box-sizing: border-box;
} */

.page_1 {
    position: absolute;
    top: 144px;
    left: 24px;
    display: flex;
}

.page_left_1 {
    display: flex;
    width: 480px;
    height: 912px;
    padding: 0px 16px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 24px;
    flex-shrink: 0;
}

.content1 {
    padding: 20px 12px;
    width: 432px;
    height: 840px;
    box-sizing: border-box;
}

.page_left_2 {
    display: flex;
    width: 480px;
    height: 912px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 24px;
    flex-shrink: 0;
}

.title_font {
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 20px;

    font-weight: 500;
    line-height: 35px;
    /* 110% */
}

.content1_title {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-wrap: nowrap;
    width: 408px;
    height: 36px;
    /*background: url("../../../assets/images/card/index2_15.png") no-repeat;*/
    background-size: cover;
    padding-left: 30px;
    box-sizing: border-box;
    color: #47ebeb;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 175% */
    margin-bottom: 16px;
}

.line_box {
    width: 2px;
    height: 8px;
    background: #47ebeb;
    margin-right: 4px;
}

.conten1_text {
    margin-bottom: 24px;
}

.titleButton {
    display: flex;
    height: 24px;
    padding: 2px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 2px;
    border: 1px solid #ff3737;
    background: rgba(255, 55, 55, 0.5);
    width: 68px;
    margin-left: auto;
    margin-top: 4px;
    font-size: 12px;
}

.hang1 {
    display: flex;
    color: #fff;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px; /* 200% */
    margin-bottom: 8px;
}

.hang1_title {
    width: 70px;
    height: 28px;
    text-align: right;
}

.hang2_title {
    width: 98px;
    height: 28px;
    text-align: right;
}

.hang1_content {
    margin-left: 16px;
}

.phoneImg {
    width: 14.605px;
    height: 14.656px;
    margin-top: 6px;
    margin-left: 19px;
}

.conten2_text {
}

.button_hang {
    display: flex;
    margin-bottom: 24px;
    gap: 42px;
}

.button_operate {
    width: 100px;
    height: 40px;
    border-radius: 6px;
    border: 1px solid #0ff;
    background: linear-gradient(
        98deg,
        rgba(51, 153, 255, 0.5) 4.18%,
        rgba(71, 235, 235, 0.5) 96.2%
    );
    color: #fff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 114.286% */
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
}

.command {
    background: url("../../../assets/images/card/fire_2_1.png") no-repeat;
    background-size: cover;
    width: 891px;
    height: 808px;
    position: absolute;
    top: 140px;
    /*left: 2496px;*/
    left: 600px;
    box-sizing: border-box;
    z-index: 1003;
}

.command_1 {
    background: url("../../../assets/images/card/fire_2_1.png") no-repeat;
    background-size: cover;
    width: 891px;
    height: 808px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    z-index: 1005;
}

.command_title {
    margin-left: 17px;
    margin-top: 12px;
    /*color: #fff;*/
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    /*font-family: Noto Sans SC;*/
    /*font-size: 24px;*/
    font-style: normal;
    /*font-weight: 500;*/
    width: 100%;
    display: flex;
    /*line-height: 32px; !* 133.333% *!*/
    margin-bottom: 30px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 18px;
    /*font-style: normal;*/
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}

.closeImg {
    width: 14px;
    height: 14px;
    margin-right: 40px;
    /*margin-top: 23px;*/
    margin-top: 10px;
    margin-left: auto;
}

.command_content {
    height: 695px;
    margin-left: 24px;
    width: 845px;
    color: #fff;
    text-align: right;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px; /* 200% */
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 5px;
    box-sizing: border-box;
}

.command_hang {
    display: flex;
    width: 814px;
    margin-bottom: 16px;
}

.command_hang1 {
    width: 50%;
    display: flex;
}

.command_hang1_title {
    width: 134px;
    text-align: right;
    color: #47ebeb;
    text-align: right;
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
}

.command_hang1_contet {
    margin-left: 16px;
}

::v-deep .el-slider__runway {
    height: 10px;
    border-radius: 6px;
    background: linear-gradient(
        270deg,
        rgba(202, 245, 255, 0.6) 0%,
        #62e3ff 100%
    );
}

::v-deep .el-slider__bar {
    height: 10px;
}

.sliderClass {
    margin-left: 20px;
}

.sliderClasstextMessage {
    width: 360px;
    margin-bottom: 8px;
}

.sliderText {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.checkbox {
    text-align: left;
    width: 438px;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    color: white;
}

::v-deep .el-checkbox__label {
    color: white;
    width: 80px;
}

::v-deep .el-checkbox__inner {
    background-color: transparent;
    border-color: #0c858c;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #0c858c;
    border-color: #0c858c;
}

::v-deep .el-checkbox {
    margin-bottom: 16px;
}

.top_button_addtask {
    margin-left: 28px;
    width: 140px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #40b5ff;
    background: rgba(64, 181, 255, 0.3);
    padding: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
}

.top_button {
    /* margin-left: auto; */
    width: 156px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #40b5ff;
    background: rgba(64, 181, 255, 0.3);
    padding: 4px;
    display: flex;
    box-sizing: border-box;
}

.top_button_1 {
    width: 72px;
    height: 28px;
    text-align: center;
    color: #fff;
    text-align: center;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 27px; /* 157.143% */
    /* padding: 2px 12px; */
    box-sizing: border-box;
}

.top_button_2 {
    width: 72px;
    height: 28px;
    text-align: center;
    color: #fff;
    text-align: center;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 27px; /* 157.143% */
    border-radius: 4px;
    /* padding: 2px 12px; */
    background: linear-gradient(270deg, #0094ff 0%, #00d1ff 100%);
    box-sizing: border-box;
}

.materialList {
    margin-top: 30px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
}

.materialHang {
    margin-bottom: 16px;
    display: flex;
    text-align: left;
    /* margin-left: 16px; */
    padding-left: 16px;
    width: 659px;
    height: 42px;
    border-radius: 4px;
    line-height: 34px; /* 122.222% */
    /*background: linear-gradient(*/
    /*  90deg,*/
    /*  rgba(10, 209, 236, 0.3) 1.15%,*/
    /*  rgba(10, 209, 236, 0.4) 53.15%,*/
    /*  rgba(10, 209, 236, 0) 100%*/
    /*);*/

    background: linear-gradient(
        90deg,
        rgba(71, 235, 235, 0.3) 1.15%,
        rgba(71, 235, 235, 0.15) 53.15%,
        rgba(71, 235, 235, 0) 100%
    );
}

.materialButton {
    margin-left: auto;
    display: flex;
    gap: 24px;
    color: #47d3ff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}

.material_text {
    line-height: 34px; /* 157.143% */
}

.material_img {
    width: 22px;
    height: 22px;
    margin-top: 5px;
}

.materialButtonBottom {
    display: flex;
    gap: 16px;
}

.materialButtonLittle {
    width: 120px;
    height: 44px;
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
    text-align: center;
    line-height: 40px;
    color: #fff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
}

.impactDialog {
    /*background: url("../../../assets/images/card/fire_5.png");*/
    background: url("../../../assets/images/card/fire_2_1.png");
    background-size: cover;
    width: 845px;
    height: 410px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    z-index: 1005;
}
.mitigateDialog {
    /*background: url("../../../assets/images/card/fire_5.png");*/
    background: url("../../../assets/images/card/fire_2_1.png");
    background-size: cover;
    width: 845px;
    height: 260px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    z-index: 1005;
}

.mitigate_content {
    margin: auto;
}
.impact_content {
    margin-left: 24px;
}

.impact_content .el-input {
    width: 205px;
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.impact_content .el-select {
    width: 205px;
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.impact_content ::v-deep .el-input__wrapper {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

::v-deep .el-form-item__label {
    color: #fff;
    width: 142px;
    text-align: right;
}

.impact_content ::v-deep .el-form-item {
    margin-bottom: 36px;
}

::v-deep .el-input__inner {
    color: #fff !important;
}

.impactButton {
    display: flex;
    gap: 40px;
    width: 100%;
    justify-content: center;
}

.impactbutton_little {
    width: 120px;
    height: 44px;
    color: #fff;
    text-align: center;
    /* font-family: PingFang SC; */
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 44px; /* 157.143% */
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
}

.impactbutton_little_1 {
    width: 120px;
    height: 44px;
    color: #30abe8;
    text-align: center;
    /* font-family: PingFang SC; */
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 44px; /* 157.143% */
    /*border-radius: 4px;*/
    /*background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);*/
    border-radius: 4px;
    border: 1px solid #30abe8;
}

.hide {
    background-color: #000;
    filter: alpha(opacity=60);
    opacity: 0.6;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 3;
    height: 100vh;
    width: 100vw;
}

.addtaskDialog {
    background: url("../../../assets/images/card/fire_2_1.png");
    background-size: cover;
    width: 957px;
    height: 425px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1005 !important;
}

.addtaskDialog1 {
    background: url("../../../assets/images/card/fire_2_1.png");
    background-size: cover;
    width: 957px;
    height: 827px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1005 !important;
}

.sendrankFrom {
    margin-top: 32px;
}

.sendrankFrom .el-input {
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.sendrankFrom .el-select {
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.sendrankFrom ::v-deep .el-input__wrapper {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

.sendrankFrom ::v-deep .el-textarea__inner {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

.sendrankFrom .el-textarea {
    border: 1px solid #47ebeb;
    border-radius: 2px;
    width: 560px;
}

.sendrankFrom ::v-deep .el-date-editor {
    border: 1px solid #47ebeb;
    border-radius: 2px;
}

.sendrankFrom ::v-deep .el-form-item__label {
    width: 260px;
}

.el-popper {
    z-index: 1000001 !important;
}

::v-deep .el-radio__input.is-checked .el-radio__inner {
    background: #47ebeb !important;
    border-color: #47ebeb !important;
}

::v-deep .el-radio__label {
    color: rgba(255, 255, 255, 0.65) !important;
}

/deep/ .el-radio__input.is-checked + .el-radio__label {
    color: #47ebeb !important;
}

.goodFromTitle {
    width: 138px;
    text-align: right;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px;
    margin-bottom: 12px;
}

table {
    width: 909px;
    margin-bottom: 30px;
    /* height: 232px; */
}

tr:nth-child(1) {
    height: 55px;
    color: #f5fdff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400 !important;
    line-height: 22px; /* 137.5% */
    letter-spacing: 0.96px;
    background: linear-gradient(
        90deg,
        rgba(22, 115, 139, 0) 3.2%,
        #16738b 50.16%,
        rgba(22, 115, 139, 0) 100.16%
    );

    /* background: linear-gradient(270deg, #16738B 3.16%, rgba(22, 115, 139, 0.00) 100.2%); */
}

th {
    font-weight: 400 !important;
}

td {
    text-align: center;
    color: #9ddcea;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    letter-spacing: 0.84px;
    height: 38px;
    border-bottom: 1px solid rgba(14, 161, 176, 0.35);
}

.tdFontDiv {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 146px;
}

.tdFontDiv1 {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 227px;
}

.recordDialog {
    background: url("../../../assets/images/card/fire_2_2.png");
    background-size: cover;
    width: 339px;
    height: 795px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    z-index: 1005;
}

.record_content {
    position: relative;
    margin-top: 24px;
    margin-left: 23px;
    height: 690px;
    overflow-y: auto;
    overflow-x: hidden;
}

.recordList {
    margin-bottom: 24px;
}

.round {
    width: 16px;
    height: 16px;
    border-radius: 999px;
    border: 1px solid #47ebeb;
    background: rgba(71, 235, 235, 0.5);
    position: absolute;
    top: 0;
    text-align: center;
    line-height: 16px;
}

.recordTitle {
    color: rgba(255, 255, 255, 0.9);
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px; /* 137.5% */
    padding-left: 20px;
    margin-bottom: 6px;
    margin-left: 3px;
}

.subTitle {
    padding-left: 16px;
    padding-bottom: 16px;
    border-left: 1px solid #47ebeb;
    margin-left: 9px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.doorDialog {
    background: url("../../../assets/images/card/fire_9.png");
    background-size: cover;
    width: 690px;
    height: 432px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    z-index: 1003;
}

.doorContent {
    margin-top: 37px;
    margin-left: 21px;
    display: flex;
    width: 100%;
}

.doorContent .el-input {
    height: 36px;
    width: 280px;
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.doorContent ::v-deep .el-input__wrapper {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

.doorContent_left {
    width: 280px;
}

.el-tree {
    background: transparent;
    color: rgba(255, 255, 255, 0.98);
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

::v-deep .el-tree-node__content:hover {
    background: transparent;
}

::v-deep .el-tree-node__content {
    background: transparent;
    display: flex;
    width: 247px;
    height: 28px;
    padding: 5px 4px 5px 80px;
    align-items: center;
    gap: 4px;
}

::v-deep .el-tree-node:focus .el-tree-node__content {
    background: transparent;
}

.treeHeight {
    margin-top: 10px;
    height: 252px;
    overflow-y: auto;
    overflow-x: hidden;
}

.doorContent_right {
    margin-left: 38px;
}

.statusTitle {
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px; /* 200% */
}

.status {
    width: 56px;
    height: 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    background: #ff3737;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 23px; /* 133.333% */
    margin-top: 4px;
    margin-left: 12px;
}

.openChoose {
    margin-top: 20px;
}

.videodoor {
    width: 277.368px;
    height: 155px;
}

.doorButton {
    display: flex;
    gap: 34px;
    width: 277.368px;
    justify-content: center;
}

.doorButtonLittle {
    width: 104px;
    height: 36px;
    color: #fff;
    text-align: center;
    /* font-family: PingFang SC; */
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 36px; /* 157.143% */
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
}

.textMessageDialog {
    background: url("../../../assets/images/card/fire_10.png");
    background-size: cover;
    width: 690px;
    height: 482px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 185.714% */
    padding-left: 30px;
    z-index: 1003;
}

.textMessageDialog .el-select {
    border-radius: 2px;
    width: 368px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.textMessageDialog ::v-deep .el-input__wrapper {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

.textMessageDialog ::v-deep .el-textarea__inner {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

.textMessageDialog .el-textarea {
    border: 1px solid #47ebeb;
    border-radius: 2px;
    width: 626px;
}

.textTitle {
    margin-bottom: 16px;
}

.textBottom {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.bottomLeft {
    width: 306px;
    height: 38px;
    border-radius: 4px;
    border: 1px solid #47ebeb;
    background: #0a6c6c;
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums tabular-nums;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px; /* 157.143% */
    display: flex;
    justify-content: center;
    align-items: center;
}

.submitText {
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
    display: flex;
    height: 36px;
    width: 76px;
    justify-content: center;
    align-items: center;
    margin-right: 30px;
}

.dispatchDialog {
    background: url("../../../assets/images/card/fire_2_3.png");
    background-size: cover;
    width: 966px;
    height: 701px;
    position: absolute;
    top: 50%;
    left: 53%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 185.714% */
    z-index: 1003;
    /* padding-left: 30px; */
}

.dispatchContent {
    padding: 0 60px 0 23px;
}

.dispatchButton {
    width: 120px;
    height: 44px;
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 7px;
}

.chooseplanResponseContent {
    display: flex;
}

.dispatchCardList {
    width: 202px;
    padding-right: 19px;
    height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 14px;
}

.dispatchCard {
    width: 183px;
    height: 92px;
    border-radius: 4px;
    /*border: 1px solid #47ebeb;*/
    /*background: #0a6c6c;*/
    padding: 8px;
    box-sizing: border-box;
    margin-bottom: 10px;

    border: 1px solid #47ebeb;
    background: rgba(48, 171, 232, 0.15);
}

.diapatchTitle {
    font-size: 14px;
    margin-bottom: 7px;
    width: 156px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.responseLevel {
    width: 64px;
    height: 18px;
    border-radius: 2px;
    border: 1px solid #fb3535;
    background: rgba(251, 53, 53, 0.33);
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-bottom: 7px; */
}

.dispatchTime {
    font-size: 12px;
}

.dispatchtip {
    color: #f58342;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 183.333% */
}

.dispatchrecord_content {
    position: relative;
}

.dispatchbottom {
    width: 202px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    top: 628px;
    left: 10px;
}

.dispatchMid {
    width: 456px;
    height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 16px 0 7px;
    box-sizing: border-box;
}

.dispatchMidTitle {
    display: flex;
    width: 100%;
}

.dispatchsubTitle {
    padding-left: 16px;
    padding-bottom: 16px;
    border-left: 1px solid #47ebeb;
    margin-left: 9px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.keynote {
    color: rgba(255, 0, 0, 0.9);

    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
}

.dispatchName {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    margin-left: 22px;
    width: 220px;
}

.dispatchtag1 {
    width: 52px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    border: 1px solid #35fbb4;
    background: rgba(53, 251, 180, 0.33);
}

.dispatchtag2 {
    width: 52px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    border: 1px solid #35b4fb;
    background: rgba(53, 180, 251, 0.33);
}

.dispatchNamelittle {
    margin-right: 16px;
}

.dispatchTeamName {
    width: 220px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dispatchStart {
    color: #a3dfff;
    margin-left: auto;
    margin-right: 32px;
}

.dispatchEnd {
    color: #a3dfff;
    margin-left: auto;
    margin-right: 32px;
}

.dispatchNote {
    width: 211px;
    min-height: 20px;
    white-space: wrap;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-all;
    margin-left: 10px;
}

.dispatchTeam {
    display: flex;
    margin-left: 10px;
}

.dispatchLeft {
    margin-left: 9px;
    height: 500px;
    width: 219px;
    overflow-y: auto;
    overflow-x: hidden;
}

.dispatchrecord_content_left {
    position: relative;
}

.dispatchNamelittle_left {
    margin-left: 8px;
}

::-webkit-scrollbar {
    width: 5px;
    float: right;
}

::-webkit-scrollbar-track {
    border-radius: 4px;
    background: #004f4f;
    width: 5px;
}

::-webkit-scrollbar-thumb {
    background: #fff;
    border-radius: 8px;
    width: 5px;
}

.dispatchContentChoose {
    position: absolute;
    left: 730px;
    top: 68px;
}

.dispatch_left {
    margin-left: 15px;
}

.dispatchButton2 {
    width: 88px;
    height: 32px;
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
    display: flex;
    justify-content: center;
    align-items: center;
}

.tdFontDiv1 {
    display: flex;
    width: 146px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #8fb5ff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}

.dispatchtableClass {
    height: 500px;
    margin-top: 18px;
    overflow-y: auto;
    overflow-x: hidden;
    width: 910px;
}

.goodFromtableClass {
    height: 287px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 20px;
}

::v-deep .el-textarea__inner {
    color: #fff;
}

.page_left_2 ::v-deep .el-input__wrapper {
    background-color: transparent;
    box-shadow: 0 0 0 0;
    border: 1px solid #47ebeb;
}

.videoClass {
    width: 431px;
    height: 204px;
    margin-top: 10px;
}

::v-deep .el-empty__description p {
    color: #fff;
}

::v-deep .el-range-input {
    color: #fff;
}

::v-deep .el-range-separator {
    color: #fff;
}

::v-deep .el-range-separator {
    color: #fff;
}

.pictureClass {
    width: 432px;
    height: 232px;
}

.imgStyle {
    width: 1px;
    height: 570px;
}
</style>
<style>
.amap-info-sharp {
    display: none !important;
}
.amap-info-content {
    background-color: transparent !important;
    box-shadow: none !important;
}
.amap-info-outer {
    background-color: transparent !important;
    box-shadow: none !important;
}
</style>
