<template>
    <div>
        <div class="environmental-management-left">
            <!-- <wrap-slot
                title1="环境监测点总览"
                title2="环境空气质量"
                title3="雨水环境质量"
            >
                <template #content1>
                </template>
                <template #content2>
                </template>
                <template #content3>
                </template>
            </wrap-slot> -->
            <left-top @alarm="alarm"></left-top>
            <left-center></left-center>
            <left-bottom></left-bottom>


        </div>
        <equipment-alarm-dialog
            v-if="equipmentAlarmDialogShow"
            :equipmentAlarmType="equipmentAlarmType"
            @closeDialog="closeDialog"
        ></equipment-alarm-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import WrapSlot from "../../commenNew/WrapSlot.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import LeftTop from "./subgroup/LeftTop.vue";        
import LeftCenter from "./subgroup/LeftCenter.vue";
import equipmentAlarmDialog from "./subgroup/equipmentAlarmDialog.vue";
const equipmentAlarmDialogShow = ref(false);
const equipmentAlarmType = ref(null);

const alarm = (val) => {
    console.log(val);
    equipmentAlarmType.value = val;
    equipmentAlarmDialogShow.value = true;
};
const closeDialog = () => {
    equipmentAlarmDialogShow.value = false;
};
</script>


<style lang="less" scoped>
.environmental-management-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
