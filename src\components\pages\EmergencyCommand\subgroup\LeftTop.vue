<template>
    <Card title="事件概览">
        <template v-slot>
            <div class="energy-consumption-monitoring">
                <!--    <div class="top">-->
                <!--      <div class="inner">-->
                <!--        <div class="title">用电量（千瓦时）</div>-->
                <!--        <div class="num">{{ data.electricity_total }}</div>-->
                <!--      </div>-->
                <!--      <div class="inner">-->
                <!--        <div class="title">用水量（吨）</div>-->
                <!--        <div class="num">{{ data.water_total }}</div>-->
                <!--      </div>-->
                <!--    </div>-->
                <!--    <div class="bottom">-->
                <!--      <div class="inner">-->
                <!--        <div class="title">天然气（千立方米）</div>-->
                <!--        <div class="num">{{ data.gas_total }}</div>-->
                <!--      </div>-->
                <!--      <div class="inner">-->
                <!--        <div class="title">蒸汽量（吨）</div>-->
                <!--        <div class="num">{{ data.steam_total }}</div>-->
                <!--      </div>-->
                <!--    </div>-->
                <div class="top_box">
                    <div>
                        <img
                            class="images_box"
                            src="../../../../assets/images/command/leftTop_1.svg"
                        />
                        <div class="top_box_text">事故灾害</div>
                        <div class="top_box_number">{{ data.disaster_count }}</div>
                    </div>
                    <div class="top_box_one">
                        <img
                            class="images_box"
                            src="../../../../assets/images/command/leftTop_2.svg"
                        />
                        <div class="top_box_text">社会安全</div>
                        <div class="top_box_number">{{ data.secure_count }}</div>
                    </div>
                </div>
                <div class="buttom_box">
                    <div>
                        <img
                            class="images_box"
                            src="../../../../assets/images/command/leftTop_3.svg"
                        />
                        <div class="top_box_text">公共安全</div>
                        <div class="top_box_number">{{ data.public_count }}</div>
                    </div>
                    <div>
                        <img
                            class="images_box"
                            src="../../../../assets/images/command/leftTop_4.svg"
                        />
                        <div class="top_box_text">自然灾害</div>
                        <div class="top_box_number">{{ data.nature_count }}</div>
                    </div>
                    <div>
                        <img
                            class="images_box"
                            src="../../../../assets/images/command/leftTop_5.svg"
                        />
                        <div class="top_box_text">其他突发</div>
                        <div class="top_box_number">{{ data.other_count }}</div>
                    </div>
                </div>
            </div>
    </template>
</Card>
</template>
<script setup>
import Card from "@/components/commenNew/Card.vue";

import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { energy_total } from "../../../../assets/js/api/smartEnergy";
import { geteventStatistics } from "../../../../assets/js/api/api";

const data = ref({
    disaster_count: 0,
    secure_count: 0,
    public_count: 0,
    nature_count: 0,
    other_count: 0,
    gas_total: 0,
    steam_total: 0,
    water_total: 0,
});
onMounted(() => {
    energy_total({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value = res.data.data[0];
        }
    });
    geteventStatistics({ id: 1 }).then((res) => {
        console.log(res, "事件统计");
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value.disaster_count = res.data.data[0].event_count;
        }
    });
    geteventStatistics({ id: 2 }).then((res) => {
        console.log(res, "事件统计");
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value.secure_count = res.data.data[0].event_count;
        }
    });
    geteventStatistics({ id: 3 }).then((res) => {
        console.log(res, "事件统计");
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value.public_count = res.data.data[0].event_count;
        }
    });
    geteventStatistics({ id: 4 }).then((res) => {
        console.log(res, "事件统计");
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value.nature_count = res.data.data[0].event_count;
        }
    });
    geteventStatistics({ id: 5 }).then((res) => {
        console.log(res, "事件统计");
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value.other_count = res.data.data[0].event_count;
        }
    });
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.energy-consumption-monitoring {
    width: 416px;
    height: 224px;
    padding: 16px;
    .top_box {
        display: flex;
        justify-content: center;
    }
    .top_box_one {
        margin-left: 48px;
    }
    .buttom_box {
        display: flex;
        justify-content: space-around;
        margin-top: 6px;
    }
    .images_box {
        width: 72px;
        height: 56px;
        flex-shrink: 0;
    }
    .top_box_text {
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    .top_box_number {
        color: #47ebeb;
        font-family: Noto Sans SC;
        text-align: center;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px; /* 133.333% */
    }
    .top {
        width: 400px;
        height: 120px;
        display: flex;
        gap: 16px;
    }
    .bottom {
        width: 400px;
        height: 120px;
        display: flex;
        gap: 16px;
        margin-top: 16px;
    }
    .inner {
        width: 192px;
        height: 120px;
        background: url("../../../../assets/images/card/overview.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        .title {
            // width: 136px;
            // height: 20px;
            margin-top: 10px;
            align-items: center;
            gap: 10px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 150% */
            text-align: center;
        }
        .num {
            margin-top: 8px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
        }
    }
}
</style>
