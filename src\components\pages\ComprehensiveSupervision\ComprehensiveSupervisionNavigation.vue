<template>
    <div>
        <div class="input-wrapper">
            <div>
                <el-select
                    v-model="enterpriseName"
                    class="m-2"
                    placeholder="请选择"
                    clearable
                    @change="changeEnterpriseName"
                    filterable
                >
                    <el-option
                        v-for="item in enterpriseNameOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="right_select" v-if="typeShow">
                <el-select
                    v-model="selectName"
                    class="m-2"
                    placeholder="请选择"
                    clearable
                    @change="changeselectName"
                    filterable
                >
                    <el-option
                        v-for="item in changeData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </div>
        </div>
        <div>
            <div
                class="comprehensive-supervision-navigation1"
                v-if="showFlag == 1"
            ></div>
            <div
                class="comprehensive-supervision-navigation2"
                v-if="showFlag == 2"
            >
                <div v-if="allinshow">
                    <div
                        v-if="allinshow && !allType"
                        :class="active === 11 ? 'active-btn' : 'btn'"
                        @click="setPage(11)"
                    >
                        <img
                            class="icon"
                            src="../../../assets/images/icon/video.svg"
                        />
                        <div class="text">全部</div>
                    </div>
                    <div v-else-if="allType" class="noactive">
                        <img
                            class="icon"
                            src="../../../assets/images/icon/video.svg"
                        />
                        <div class="text">全部</div>
                    </div>
                </div>
                <div
                    v-else
                    :class="active === 13 ? 'active-btn' : 'btn'"
                    @click="setPage(13)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/files.svg"
                    />
                    <div class="text">企业档案</div>
                </div>
                <div
                    v-if="typeStatus[0].count != '0'"
                    :class="active === 1 ? 'active-btn' : 'btn'"
                    @click="setPage(1)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/workshop.svg"
                    />
                    <div class="text">厂房车间</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/workshop.svg"
                    />
                    <div class="text">厂房车间</div>
                </div>

                <div
                    v-if="typeStatus[1].count != '0'"
                    :class="active === 2 ? 'active-btn' : 'btn'"
                    @click="setPage(2)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/warehouse.svg"
                    />
                    <div class="text">仓库</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/warehouse.svg"
                    />
                    <div class="text">仓库</div>
                </div>

                <div
                    v-if="typeStatus[2].count != '0'"
                    :class="active === 3 ? 'active-btn' : 'btn'"
                    @click="setPage(3)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/tank.svg"
                    />
                    <div class="text">罐区</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/tank.svg"
                    />
                    <div class="text">罐区</div>
                </div>
                <div
                    v-if="typeStatus[3].count != '0'"
                    :class="active === 9 ? 'active-btn' : 'btn'"
                    @click="setPage(9)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/storageTank.svg"
                    />
                    <div class="text">储罐</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/storageTank.svg"
                    />
                    <div class="text">储罐</div>
                </div>

                <!-- <div
            v-if="typeStatus[5].count != '0'"
            :class="active === 6 ? 'active-btn' : 'btn'"
            @click="setPage(6)"
        >
          <img
              class="icon"
              src="../../../assets/images/icon/highRiskProcesses.svg"
          />
          <div class="text">重点工艺</div>
        </div>
        <div v-else class="noactive">
          <img
              class="icon"
              src="../../../assets/images/icon/highRiskProcesses.svg"
          />
          <div class="text">重点工艺</div>
        </div>
        <div
            v-if="typeStatus[6].count != '0'"
            :class="active === 8 ? 'active-btn' : 'btn'"
            @click="setPage(8)"
        >
          <img
              class="icon"
              src="../../../assets/images/icon/hazardousChemicals.svg"
          />
          <div class="text">监管化学品</div>
        </div>
        <div v-else class="noactive">
          <img
              class="icon"
              src="../../../assets/images/icon/hazardousChemicals.svg"
          />
          <div class="text">监管化学品</div>
        </div> -->

                <div
                    :class="activeCom === 3 ? 'active-btn' : 'btn'"
                    @click="setPageCom(3)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/viewpoint-switch.svg"
                    />
                    <div class="text">视角切换</div>
                </div>
            </div>
            <div
                class="comprehensive-supervision-navigation3"
                v-if="showFlag == 3"
            >
                <div v-if="allinshow">
                    <div
                        v-if="allinshow && !allType"
                        :class="active === 11 ? 'active-btn' : 'btn'"
                        @click="setPage(11)"
                    >
                        <img
                            class="icon"
                            src="../../../assets/images/icon/video.svg"
                        />
                        <div class="text">全部</div>
                    </div>
                    <div v-else-if="allType" class="noactive">
                        <img
                            class="icon"
                            src="../../../assets/images/icon/video.svg"
                        />
                        <div class="text">全部</div>
                    </div>
                </div>
                <div
                    v-else
                    :class="active === 13 ? 'active-btn' : 'btn'"
                    @click="setPage(13)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/files.svg"
                    />
                    <div class="text">企业档案</div>
                </div>
                <div
                    v-if="typeStatus[0].count != '0'"
                    :class="active === 1 ? 'active-btn' : 'btn'"
                    @click="setPage(1)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/workshop.svg"
                    />
                    <div class="text">厂房车间</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/workshop.svg"
                    />
                    <div class="text">厂房车间</div>
                </div>

                <div
                    v-if="typeStatus[1].count != '0'"
                    :class="active === 2 ? 'active-btn' : 'btn'"
                    @click="setPage(2)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/warehouse.svg"
                    />
                    <div class="text">仓库</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/warehouse.svg"
                    />
                    <div class="text">仓库</div>
                </div>

                <div
                    v-if="typeStatus[2].count != '0'"
                    :class="active === 3 ? 'active-btn' : 'btn'"
                    @click="setPage(3)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/tank.svg"
                    />
                    <div class="text">罐区</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/tank.svg"
                    />
                    <div class="text">罐区</div>
                </div>
                <div
                    v-if="typeStatus[3].count != '0'"
                    :class="active === 9 ? 'active-btn' : 'btn'"
                    @click="setPage(9)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/storageTank.svg"
                    />
                    <div class="text">储罐</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/storageTank.svg"
                    />
                    <div class="text">储罐</div>
                </div>

                <!-- <div
          v-if="typeStatus[5].count != '0'"
          :class="active === 6 ? 'active-btn' : 'btn'"
          @click="setPage(6)"
        >
          <img
            class="icon"
            src="../../../assets/images/icon/highRiskProcesses.svg"
          />
          <div class="text">重点工艺</div>
        </div>
        <div v-else class="noactive">
          <img
            class="icon"
            src="../../../assets/images/icon/highRiskProcesses.svg"
          />
          <div class="text">重点工艺</div>
        </div>
        <div
          v-if="typeStatus[6].count != '0'"
          :class="active === 8 ? 'active-btn' : 'btn'"
          @click="setPage(8)"
        >
          <img
            class="icon"
            src="../../../assets/images/icon/hazardousChemicals.svg"
          />
          <div class="text">监管化学品</div>
        </div>
        <div v-else class="noactive">
          <img
            class="icon"
            src="../../../assets/images/icon/hazardousChemicals.svg"
          />
          <div class="text">监管化学品</div>
        </div> -->

                <!-- <div
          :class="activeCom === 1 ? 'active-btn' : 'btn'"
          @click="setPageCom(1)"
        >
          <img
            class="icon"
            src="../../../assets/images/icon/electronic-fence.svg"
          />
          <div class="text">电子围栏</div>
        </div>
        <div
          :class="activeCom === 2 ? 'active-btn' : 'btn'"
          @click="setPageCom(2)"
        >
          <img class="icon" src="../../../assets/images/icon/ranging.svg" />
          <div class="text">测距</div>
        </div> -->
            </div>
            <div class="map-btns" v-if="mapBtnShow">
                <div
                    :class="mapActive === 1 ? 'map-btn-active' : 'map-btn'"
                    @click="clickAdd"
                >
                    新建围栏
                </div>
                <div
                    :class="mapActive === 2 ? 'map-btn-active' : 'map-btn'"
                    @click="clickEdit"
                >
                    开始编辑
                </div>
                <div
                    :class="mapActive === 3 ? 'map-btn-active' : 'map-btn'"
                    @click="closeEdit"
                >
                    结束编辑
                </div>
            </div>
            <div class="visual-angle-btns" v-if="visualBtnShow">
                <div
                    :class="visualActive === 1 ? 'map-btn-active' : 'map-btn'"
                    @click="changeVisual(1)"
                >
                    水源
                </div>
                <div
                    :class="visualActive === 2 ? 'map-btn-active' : 'map-btn'"
                    @click="changeVisual(2)"
                >
                    绿地
                </div>
            </div>
        </div>
        <workshop-dialog
            :pickWorkshopkIsGrey="pickWorkshopkIsGrey"
            :pickId="pickWorkshopkId"
            part="综合监管"
            v-if="workshopDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></workshop-dialog>
        <warehouse-dialog
            :pickId="warehouseId"
            :warehouseIsGrey="warehouseIsGrey"
            part="综合监管"
            v-if="warehouseDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></warehouse-dialog>
        <tank-dialog
            :pickId="tankId"
            :tankIsGrey="tankIsGrey"
            part="综合监管"
            v-if="tankDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></tank-dialog>
        <highrisk-processes-dialog
            :pickId="highriskProcessesId"
            v-if="highriskProcessesDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></highrisk-processes-dialog>
        <toxic-gas-dialog
            v-if="ToxicGasDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></toxic-gas-dialog>
        <hazardous-chemicals-dialog
            :pickId="hazardousChemicalsId"
            v-if="hazardousChemicalsDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></hazardous-chemicals-dialog>
        <monitoring-points-dialog
            part="综合监管"
            :pickId="monitoringPointskId"
            :monitoringPointskIsGrey="monitoringPointskIsGrey"
            v-if="monitoringPointsDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></monitoring-points-dialog>
        <files-dialog
            :pickId="filesId"
            :pickFilesId="filesName"
            v-if="filesDialogShow"
            @closeDialog="closeDialog"
            @openDialog="openDialog"
        ></files-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    onBeforeMount,
    defineExpose,
    nextTick,
} from "vue";
import * as Cesium from "cesium";

import { enterprise_list } from "../../../assets/js/api/parkArchives";
import {
    electronic_select_all,
    electronic_add,
    electronic_update,
} from "../../../assets/js/api/comprehensiveSupervision";
import {
    getEquipmentInfo,
    getFlv,
    major_danger_source_info,
    major_danger_source_info_all,
    plant_workshop,
    entrepo,
    tank_farm,
    storage_tank,
    storage_tank_info,
    risk_register,
    risk_register_info,
    danger_chemical_process_list,
    danger_chemical_process_info,
    danger_chemical_list,
    getInformationStatus,
    data_plant_workshop,
    data_entrepo,
    data_tank_farm,
    data_storage_tank,
} from "../../../assets/js/api/safetySupervision";
import WorkshopDialog from "../SafetySupervision/subgroup/WorkshopDialog.vue";
import WarehouseDialog from "../SafetySupervision/subgroup/WarehouseDialog.vue";
import TankDialog from "../SafetySupervision/subgroup/TankDialog.vue";
import HighriskProcessesDialog from "../SafetySupervision/subgroup/HighriskProcessesDialog.vue";
import ToxicGasDialog from "../SafetySupervision/subgroup/ToxicGasDialog.vue";
import HazardousChemicalsDialog from "../SafetySupervision/subgroup/HazardousChemicalsDialog.vue";
import MonitoringPointsDialog from "../SafetySupervision/subgroup/MonitoringPointsDialog.vue";
import filesDialog from "../ParkArchives/subgroup/filesDialog.vue";
const { proxy } = getCurrentInstance();
const showFlag = ref(1);
const activeCom = ref(0);
const mapActive = ref(0);
//自定义样式
var startMarkerOptions = {
    icon: new AMap.Icon({
        size: new AMap.Size(19, 31), //图标大小
        imageSize: new AMap.Size(19, 31),
        image: "https://webapi.amap.com/theme/v1.3/markers/b/start.png",
    }),
};
var endMarkerOptions = {
    icon: new AMap.Icon({
        size: new AMap.Size(19, 31), //图标大小
        imageSize: new AMap.Size(19, 31),
        image: "https://webapi.amap.com/theme/v1.3/markers/b/end.png",
    }),
    offset: new AMap.Pixel(-9, -31),
};
var midMarkerOptions = {
    icon: new AMap.Icon({
        size: new AMap.Size(19, 31), //图标大小
        imageSize: new AMap.Size(19, 31),
        image: "https://webapi.amap.com/theme/v1.3/markers/b/mid.png",
    }),
    offset: new AMap.Pixel(-9, -31),
};
var lineOptions = {
    strokeStyle: "solid",
    strokeColor: "#FF33FF",
    strokeOpacity: 1,
    strokeWeight: 2,
};
var rulerOptions = {
    startMarkerOptions: startMarkerOptions,
    midMarkerOptions: midMarkerOptions,
    endMarkerOptions: endMarkerOptions,
    lineOptions: lineOptions,
};
var ruler2;
var path5 = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
// var mouseTool;
const fenceData = ref([]);
const polygonArr = ref([]);
const polyEditor = ref(null);
const selectName = ref(null);
const typeShow = ref(false);
const mapBtnShow = ref(false);
const visualBtnShow = ref(false);
const visualActive = ref(0);
var zIndex = 30;
const changeVisual = (val) => {
    if (visualActive.value == val) {
        visualActive.value = 0;
        window.viewer.entities.removeAll(); //删除所有
        if (window.mouseTool) {
            window.mouseTool.close(true); //关闭，并清除覆盖物
        }
        // ruler2.turnOff();
        window.map1.clearMap();
        mapBtnShow.value = false;
        initBoundary();
    } else {
        visualActive.value = val;
        window.viewer.entities.removeAll(); //删除所有
        if (window.mouseTool) {
            window.mouseTool.close(true); //关闭，并清除覆盖物
        }
        window.map1.clearMap();
        initBoundary();
        if (val == 2) {
            greenLandData.value.forEach((item, index) => {
                const entity = window.viewer.entities.add({
                    id: index,
                    show: true,
                    polygon: {
                        hierarchy:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item), //参数为四个角点坐标
                        zIndex: zIndex,
                        height: zIndex, //多层次
                        outline: true,
                        outlineColor:
                            Cesium.Color.fromCssColorString("#47EBEB"),
                        outlineWidth: 15.0,
                        material: Cesium.Color.fromCssColorString(
                            "rgba(71, 235, 235, 0.30)",
                        ),
                    },
                });
            });
        } else if (val == 1) {
            waterData.value.forEach((item, index) => {
                const entity = window.viewer.entities.add({
                    id: index,
                    show: true,
                    polygon: {
                        hierarchy:
                            Cesium.Cartesian3.fromDegreesArrayHeights(item), //参数为四个角点坐标
                        zIndex: zIndex,
                        height: zIndex, //多层次
                        outline: true,
                        outlineColor:
                            Cesium.Color.fromCssColorString("#47EBEB"),
                        outlineWidth: 15.0,
                        material: Cesium.Color.fromCssColorString(
                            "rgba(71, 235, 235, 0.30)",
                        ),
                    },
                });
            });
        }
    }
};
//与安全监管无关的按钮切换
const setPageCom = (type) => {
    if (activeCom.value === type) {
        activeCom.value = 0;
        visualActive.value = 0;

        window.viewer.entities.removeAll(); //删除所有
        if (window.mouseTool) {
            window.mouseTool.close(true); //关闭，并清除覆盖物
        }
        // ruler2.turnOff();
        window.map1.clearMap();
        mapBtnShow.value = false;
        initBoundary();
    } else {
        activeCom.value = type;
        window.viewer.entities.removeAll(); //删除所有
        if (window.mouseTool) {
            window.mouseTool.close(true); //关闭，并清除覆盖物
        }
        window.map1.clearMap();
        initBoundary();

        if (type === 1) {
            var dealPolygonArr = [];
            electronic_select_all({}).then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    fenceData.value = res.data.data;
                    fenceData.value.forEach((item) => {
                        let polygon = new AMap.Polygon({
                            path: item.points,
                            strokeColor: "#FF33FF",
                            strokeWeight: 6,
                            strokeOpacity: 0.2,
                            fillOpacity: 0.4,
                            fillColor: "#1791fc",
                            zIndex: 50,
                            bubble: true,
                            exData: item,
                        });
                        dealPolygonArr.push(polygon);
                    });
                    polygonArr.value = dealPolygonArr;
                    console.log(polygonArr.value);
                    window.map1.add(polygonArr.value);
                    window.map1.setFitView();
                }
            });

            polyEditor.value = new AMap.PolygonEditor(window.map1);
            mapBtnShow.value = true;
        } else if (type === 2) {
            mapBtnShow.value = false;

            //自定义样式
            //   ruler2 = new AMap.RangingTool(window.map1, rulerOptions);
            //原始样式
            //   ruler2 = new AMap.RangingTool(window.map1);
            //   ruler2.turnOn();
            //   mouseTool = new AMap.MouseTool(window.map1);
            console.log(window.mouseTool);
            window.mouseTool.rule({
                startMarkerOptions: {
                    //可缺省
                    icon: new AMap.Icon({
                        size: new AMap.Size(19, 31), //图标大小
                        imageSize: new AMap.Size(19, 31),
                        image: "https://webapi.amap.com/theme/v1.3/markers/b/start.png",
                    }),
                },
                endMarkerOptions: {
                    //可缺省
                    icon: new AMap.Icon({
                        size: new AMap.Size(19, 31), //图标大小
                        imageSize: new AMap.Size(19, 31),
                        image: "https://webapi.amap.com/theme/v1.3/markers/b/end.png",
                    }),
                    offset: new AMap.Pixel(-9, -31),
                },
                midMarkerOptions: {
                    //可缺省
                    icon: new AMap.Icon({
                        size: new AMap.Size(19, 31), //图标大小
                        imageSize: new AMap.Size(19, 31),
                        image: "https://webapi.amap.com/theme/v1.3/markers/b/mid.png",
                    }),
                    offset: new AMap.Pixel(-9, -31),
                },
                lineOptions: {
                    //可缺省
                    strokeStyle: "solid",
                    strokeColor: "#FF33FF",
                    strokeOpacity: 1,
                    strokeWeight: 2,
                },
                //同 RangingTool 的 自定义 设置，缺省为默认样式
            });
        } else if (type == 3) {
            visualBtnShow.value = true;
            //   const entity = window.viewer.entities.add({
            //     id: "polygon",
            //     name: "面",
            //     show: true,
            //     polygon: {
            //       hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights([
            //         115.545, 38.89, 30, 115.545, 38.8908, 30, 115.5447, 38.89, 30,
            //         115.545, 38.89, 30,
            //       ]), //参数为四个角点坐标
            //       material: Cesium.Color.fromCssColorString("#0000ff"),
            //     },
            //   });
            //   viewer.zoomTo(entity);
        }
    }
};
//新建电子围栏
const clickAdd = () => {
    mapActive.value = 1;
    console.log("add");

    polyEditor.value.setTarget();
    polyEditor.value.open();
    polyEditor.value.on("add", (data) => {
        console.log(data);
    });
    polyEditor.value.on("end", (data) => {
        console.log("add-end");

        console.log(data.target._opts.path);
        // polyEditor.value.close()
        console.log(data.target._opts.exData);
        if (
            data.target._opts.path != null &&
            data.target._opts.path !== undefined
        ) {
            var path = JSON.stringify(data.target._opts.path);
            electronic_add({ tempPoints: data.target._opts.path }).then(
                (res) => {},
            );
        }
    });
};
const clickPolEditor = ref(null);
var id = null;
var weilanPath = [];
//编辑电子围栏
const clickEdit = () => {
    mapActive.value = 2;
    polygonArr.value.forEach((polygon, index) => {
        // console.log(polygon);
        polygon.on("dblclick", (data) => {
            console.log(data.target._opts.exData);
            if (polyEditor.value) {
                polyEditor.value.setTarget(polygon);
                console.log(polyEditor.value);
                polyEditor.value.open();
                polyEditor.value.on("end", (data) => {
                    console.log("edit-end");
                    // console.log(data);
                    // polyEditor.value.close()
                    if (
                        data.target._opts.exData != null &&
                        data.target._opts.exData !== undefined &&
                        data.target._opts.path != null &&
                        data.target._opts.path !== undefined
                    ) {
                        // console.log(data.target._opts.exData.id);
                        // console.log(data.target._opts.path);
                        electronic_update({
                            id: data.target._opts.exData.id,
                            tempPoints: data.target._opts.path,
                        }).then((res) => {});
                    }
                });
            }
        });
    });
};
//结束编辑
const closeEdit = () => {
    mapActive.value = 3;
    console.log(polyEditor.value);
    // polyEditor.value.on('end', (data) => {
    //     console.log(data);
    //     // polyEditor.value.close()
    // })
    polyEditor.value.close();
    polyEditor.value = null;
    window.map1.clearMap();
    var dealPolygonArr = [];
    setTimeout(() => {
        initBoundary();

        electronic_select_all({}).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                fenceData.value = res.data.data;
                fenceData.value.forEach((item) => {
                    let polygon = new AMap.Polygon({
                        path: item.points,
                        strokeColor: "#FF33FF",
                        strokeWeight: 6,
                        strokeOpacity: 0.2,
                        fillOpacity: 0.4,
                        fillColor: "#1791fc",
                        zIndex: 50,
                        bubble: true,
                        exData: item,
                    });
                    dealPolygonArr.push(polygon);
                });
                polygonArr.value = dealPolygonArr;
                console.log(polygonArr.value);
                window.map1.add(polygonArr.value);

                window.map1.setFitView();
                mapActive.value = 0;
            }
        });
        polyEditor.value = new AMap.PolygonEditor(window.map1);
    }, 500);
};

const putPoints = (_datas) => {
    const entityCollection = new Cesium.EntityCollection();
    // window.viewer.entities.add(entityCollection)
    console.log(window.viewer.entities);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        const entity = new Cesium.Entity({
            name: "cameraPoint",
            // 参数顺序：经度、纬度
            position: Cesium.Cartesian3.fromDegrees(
                data.longitude,
                data.latitude,
                30,
            ), // 标签的位置
            label: {
                text: "我是一个点",
                font: "100px HelVetica",
                fillColor: Cesium.Color.RED,
            },
            billboard: {
                image: aliveImg,
                width: 49,
                height: 49,
            },
        });
        entityCollection.add(entity);
    }
    window.viewer.entities.add(entityCollection);

    console.log(window.viewer.entities);
};
onBeforeMount(() => {
    console.log(window);
});
//画园区边界
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path5,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
const typesValue = ref(null); //判断点的是哪个类型
const img = ref(null);
const pickMajorId = ref(null);
const pickWorkshopkId = ref(null);
const pickWorkshopkIsGrey = ref(false);
const warehouseId = ref(null);
const warehouseIsGrey = ref(false);

const monitoringPointskId = ref(null);
const monitoringPointskIsGrey = ref(false);

const riskPointsId = ref(null);
const riskPointsName = ref(null);
const highriskProcessesId = ref(null);
const tankId = ref(null);
const tankIsGrey = ref(false);

const hazardousChemicalsId = ref(null);
const filesId = ref(null);
const filesName = ref(null);
//厂房车间不置灰数组
const plantWorkshopNoGrey = ref([]);
//仓库不置灰数组
const entrepoNoGrey = ref([]);
//罐区不置灰数组
const tankNoGrey = ref([]);
//储罐不置灰数组
const storageTankNoGrey = ref([]);
const enterpriseName = ref("");
const enterpriseNameOptions = ref([]);
//改变企业控制置灰
const changeEnterpriseName = (e) => {
    if (e) {
        allinshow.value = true;
        if (!active.value || (active.value == 0 && !allType.value)) {
            active.value = 11;
        }
        console.log(typesValue.value, enterpriseName.value, "双击");
        if (
            enterpriseName.value &&
            typesValue.value &&
            typesValue.value != 11 &&
            typesValue.value !== 0
        ) {
            typeShow.value = true;
        }
        //获取数量-各标签是否置灰
        getInformationStatus({ enterprise_name: e }).then((res) => {
            res.data.data.forEach((items) => {
                typeStatus.value.forEach((item) => {
                    if (items.table_name === item.table_name) {
                        item.count = items.count;
                    }
                });
            });
            allType.value = typeStatus.value.every((item) => item.count == "0");
            console.log(typeStatus.value, "typeStatus.value", allType.value);
        });
    } else {
        active.value = 0;
        typeStatus.value.forEach((item) => {
            item.count = "1";
        });
        allinshow.value = false;
        allType.value = false;
        typeShow.value = false;
    }

    window.viewer.entities.removeAll(); //删除所有
    window.map1.clearMap();
    initBoundary();

    switch (active.value) {
        case 1:
            console.log(enterpriseName.value, "enterpriseName.value");
            data_plant_workshop({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                    }
                    searchPlantWorkshop("", 1);
                },
            );
            break;
        case 2:
            data_entrepo({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        entrepoNoGrey.value = re1.data.data;
                    }
                    searchEntrepo("", 2);
                },
            );
            break;
        case 3:
            data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        tankNoGrey.value = re1.data.data;
                    }
                    searchTankForm("", 3);
                },
            );
            break;
        case 6:
            searchDangerChemicalProcess("", 6);
            break;
        case 8:
            searchDangerChemicalList("", 8);
            break;
        case 9:
            data_storage_tank({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        storageTankNoGrey.value = re1.data.data;
                    }
                    searchStorageTank("", 9);
                },
            );
            break;
        case 11:
            positionData.value = [];
            areaData.value = [];
            areaDataThree.value = [];
            data_plant_workshop({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                    }
                    searchPlantWorkshop("all", 1);
                },
            );
            data_entrepo({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        entrepoNoGrey.value = re1.data.data;
                    }
                    searchEntrepo("all", 2);
                },
            );
            data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        tankNoGrey.value = re1.data.data;
                    }
                    searchTankForm("all", 3);
                },
            );
            // searchDangerChemicalList("all", 8);
            data_storage_tank({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        storageTankNoGrey.value = re1.data.data;
                    }
                    searchStorageTank("all", 9);
                },
            );
            // searchDangerChemicalProcess("all", 6);

            searchNjEquipments("all", 10);
            console.log(areaDataThree.value, "areaDataThree.value--=-=-=");
            break;
    }
};
//根据点击事件展示的搜素框
const changeselectName = (e) => {
    changeData.value = [];
    // console.log(e,"测试",typesValue.value)
    if (typesValue.value == 1) {
        searchPlantWorkshop("", 1, e);
    } else if (typesValue.value == 2) {
        searchEntrepo("", 2, e);
    } else if (typesValue.value == 3) {
        searchTankForm("", 3, e);
    } else if (typesValue.value == 9) {
        searchStorageTank("", 9, e);
    }
};
//是否展示全部
const allinshow = ref(false);
const allType = ref(false);
//判断是否置灰
const typeStatus = ref([
    {
        table_name: "plant_workshop",
        count: "1",
    },
    {
        table_name: "entrepo",
        count: "1",
    },
    {
        table_name: "tank_farm",
        count: "1",
    },
    {
        table_name: "storage_tank",
        count: "1",
    },
    {
        table_name: "major_danger_source",
        count: "1",
    },
    {
        table_name: "danger_chemical_process",
        count: "1",
    },
    {
        table_name: "danger_chemical",
        count: "1",
    },
    {
        table_name: "risk_register",
        count: "1",
    },
    {
        table_name: "camera",
        count: "1",
    },
]);
//安全监管按钮
const active = ref(0);

//改变按钮
const setPage = (type) => {
    // console.log(positionData.value, "数据",type);
    if (active.value === type) {
        active.value = 0;
        typesValue.value = 0;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        console.log(window.viewer.entities);
        typeShow.value = false;
        initBoundary();
    } else {
        active.value = type;
        typesValue.value = type;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        initBoundary();
        //打点前临时;
        selectName.value = null;
        switch (type) {
            case 1:
                data_plant_workshop({
                    enterprise_name: enterpriseName.value,
                }).then((re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                        console.log(
                            plantWorkshopNoGrey.value,
                            "plantWorkshopNoGrey.value",
                        );
                    }
                    if (enterpriseName.value) {
                        typeShow.value = true;
                    }
                    searchPlantWorkshop("", 1, "");
                });
                break;
            case 2:
                data_entrepo({ enterprise_name: enterpriseName.value }).then(
                    (re1) => {
                        if (
                            re1.data.success &&
                            re1.data.data &&
                            re1.data.data.length
                        ) {
                            entrepoNoGrey.value = re1.data.data;
                        }
                        if (enterpriseName.value) {
                            typeShow.value = true;
                        }
                        searchEntrepo("", 2, "");
                    },
                );

                break;
            case 3:
                data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                    (re1) => {
                        if (
                            re1.data.success &&
                            re1.data.data &&
                            re1.data.data.length
                        ) {
                            tankNoGrey.value = re1.data.data;
                            console.log(tankNoGrey.value, "tankNoGrey.value");
                        }
                        if (enterpriseName.value) {
                            typeShow.value = true;
                        }
                        searchTankForm("", 3, "");
                    },
                );
                break;
            case 6:
                searchDangerChemicalProcess("", 6);
                break;
            case 7:
                ToxicGasDialogShow.value = true;
                break;
            case 8:
                searchDangerChemicalList("", 8);
                break;
            case 9:
                data_storage_tank({
                    enterprise_name: enterpriseName.value,
                }).then((re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        storageTankNoGrey.value = re1.data.data;
                        console.log(
                            storageTankNoGrey.value,
                            "storageTankNoGrey.value",
                        );
                    }
                    if (enterpriseName.value) {
                        typeShow.value = true;
                    }
                    searchStorageTank("", 9, "");
                });

                break;
            case 11:
                typeShow.value = false;
                data_plant_workshop({
                    enterprise_name: enterpriseName.value,
                }).then((re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                    }
                    searchPlantWorkshop("all", 1, "");
                });
                data_entrepo({ enterprise_name: enterpriseName.value }).then(
                    (re1) => {
                        if (
                            re1.data.success &&
                            re1.data.data &&
                            re1.data.data.length
                        ) {
                            entrepoNoGrey.value = re1.data.data;
                        }
                        searchEntrepo("all", 2, "");
                    },
                );
                data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                    (re1) => {
                        if (
                            re1.data.success &&
                            re1.data.data &&
                            re1.data.data.length
                        ) {
                            tankNoGrey.value = re1.data.data;
                        }
                        searchTankForm("all", 3, "");
                    },
                );
                //   searchDangerChemicalList("all", 8);
                data_storage_tank({
                    enterprise_name: enterpriseName.value,
                }).then((re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        storageTankNoGrey.value = re1.data.data;
                    }
                    searchStorageTank("all", 9, "");
                });
                //   searchDangerChemicalProcess("all", 6);

                break;
            case 13:
                searchFiles();
                break;
        }
    }
};
//各类弹窗
const workshopDialogShow = ref(false);
const warehouseDialogShow = ref(false);
const tankDialogShow = ref(false);
const highriskProcessesDialogShow = ref(false);
const ToxicGasDialogShow = ref(false);
const hazardousChemicalsDialogShow = ref(false);
const monitoringPointsDialogShow = ref(false);
const filesDialogShow = ref(false);
//打开弹窗
const openDialog = (value) => {
    if (value == "workshop") {
        workshopDialogShow.value = true;
    } else if (value == "warehouse") {
        warehouseDialogShow.value = true;
    } else if (value == "tank") {
        tankDialogShow.value = true;
    } else if (value == "highriskProcesses") {
        highriskProcessesDialogShow.value = true;
    } else if (value == "toxicGas") {
        ToxicGasDialogShow.value = true;
    } else if (value == "hazardousChemicals") {
        hazardousChemicalsDialogShow.value = true;
    } else if (value == "monitoringPoints") {
        monitoringPointsDialogShow.value = true;
    } else if (value == "enterprise") {
        filesDialogShow.value = true;
    }
    proxy.$loading.show();
};
//关闭弹窗
const closeDialog = (value) => {
    console.log(value);

    if (value == "workshop") {
        workshopDialogShow.value = false;
    } else if (value == "warehouse") {
        warehouseDialogShow.value = false;
    } else if (value == "tank") {
        tankDialogShow.value = false;
    } else if (value == "highriskProcesses") {
        highriskProcessesDialogShow.value = false;
    } else if (value == "toxicGas") {
        ToxicGasDialogShow.value = false;
    } else if (value == "hazardousChemicals") {
        hazardousChemicalsDialogShow.value = false;
    } else if (value == "monitoringPoints") {
        monitoringPointsDialogShow.value = false;
    } else if (value == "enterprise") {
        filesDialogShow.value = false;
    }
    proxy.$loading.hide();
};
const searchEnterpriseData = () => {
    enterprise_list().then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            enterpriseNameOptions.value = res.data.data.map((x) => {
                return {
                    value: x.enterprise_name,
                    label: x.enterprise_name,
                };
            });
        }
    });
};
onMounted(() => {
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
        activeCom.value = 0;
        visualActive.value = 0;
    });

    initBoundary();
    proxy.$bus.on("clicJump1", (val) => {
        if (val.alarmObjectId != null && val.alarmObjectId != undefined) {
            console.log(val);
            if (val.tabFlag == "安全监管") {
                switch (val.tag) {
                    case "罐区":
                        console.log("罐区");
                        data_tank_farm({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                tankNoGrey.value = re1.data.data;
                            }
                            searchTankForm("", 3, val.alarmObjectId);
                        });

                        break;
                    case "储罐":
                        console.log("储罐");
                        data_storage_tank({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                storageTankNoGrey.value = re1.data.data;
                            }
                            searchStorageTank("", 9, val.alarmObjectId);
                        });

                        break;
                    case "仓库":
                        console.log("仓库");
                        data_entrepo({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                entrepoNoGrey.value = re1.data.data;
                            }
                            searchEntrepo("", 2, val.alarmObjectId);
                        });

                        break;
                    case "厂房车间":
                        console.log("厂房车间");
                        data_plant_workshop({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                plantWorkshopNoGrey.value = re1.data.data;
                            }
                            searchPlantWorkshop("", 1, val.alarmObjectId);
                        });

                        break;

                    default:
                        break;
                }
            }
        }
    });
    showFlag.value = window.toggle;
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }

    searchEnterpriseData();
});
onUnmounted(() => {
    proxy.$bus.off("change_toggle");
});

//筛选点击的下拉框里的数据,并返回一个新数组
const resetData = (list, values, uid) => {
    //list全部数据
    //values是根据哪个属性
    //uid要查询的id数据
    let selectLsit = [];
    list.forEach((item) => {
        if (item[values] == uid) {
            selectLsit.push(item);
        }
    });
    window.viewer.entities.removeAll(); //删除所有
    window.map1.clearMap();
    // console.log(window.viewer.entities);
    initBoundary();
    return selectLsit;
};
//厂房车间 type是1
const searchPlantWorkshop = (val, type, uid) => {
    //传''则查单个 置空
    if (!val) {
        console.log("ceshi cesium");
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
        changeData.value = [];
    }

    // console.log(positionData.value);
    plant_workshop({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data
            // console.log(res.data.data,"仓库")
            res.data.data.forEach((item) => {
                if (item.is_point == 1) {
                    positionData.value.push({
                        name: item.id,
                        type: type,
                        id: item.workshop_name,
                        isGrey:
                            plantWorkshopNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                    changeData.value.push({
                        id: item.id,
                        type: type,
                        name: item.workshop_name,
                        isGrey:
                            plantWorkshopNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                } else {
                    if (item.points != null) {
                        let threeMiddle = [];
                        console.log(eval(item.points));
                        eval(item.points).forEach((innerItem) => {
                            innerItem.forEach((inner, index) => {
                                if (index == 0) {
                                    threeMiddle.push(Number(inner) - 0.0062);
                                } else {
                                    threeMiddle.push(Number(inner) - 0.00085);
                                }
                            });
                            threeMiddle.push(30);
                        });
                        let obj = {};
                        // console.log()
                        Reflect.set(obj, "id", item.id);
                        Reflect.set(obj, "type", type);
                        Reflect.set(obj, "isGrey", type);
                        Reflect.set(obj, "path", threeMiddle);
                        areaDataThree.value.push(obj);
                        areaData.value.push({
                            name: item.id,
                            type: type,
                            id: item.workshop_name,
                            isGrey:
                                plantWorkshopNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            path: item.points,
                        });
                    }
                }
            });

            if (window.toggle == 2) {
                console.log(positionData.value);
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    selectLsit.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                }
                putIcons(positionNoGreyData, workshopImg);
                putIcons(positionGreyData, workshopGreyImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);
                        console.log(e.position);
                        pickWorkshopkIsGrey.value = pick.id.isGrey;
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                        openCesiumDialog(pick.id.type, pick.id._chuancan);
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
                areaDataThree.value.forEach((item) => {
                    // if (item.type == 1){
                    let polygonColor = "#737B8C";
                    if (!item.isGrey) {
                        polygonColor = "rgba(71, 235, 235, 0.30)";
                    }

                    const entity = window.viewer.entities.add({
                        // id: item.id,
                        chuancan: item.id,
                        type: item.type,
                        isGrey: item.isGrey,
                        name: item.id,
                        longitude: Number(item.path[0]),
                        latitude: Number(item.path[1]),
                        show: true,
                        polygon: {
                            hierarchy:
                                Cesium.Cartesian3.fromDegreesArrayHeights(
                                    item.path,
                                ), //参数为四个角点坐标
                            zIndex: zIndex,
                            height: zIndex, //多层次
                            outline: true,
                            outlineColor:
                                Cesium.Color.fromCssColorString("#47EBEB"),
                            outlineWidth: 15.0,
                            material:
                                Cesium.Color.fromCssColorString(polygonColor),
                        },
                    });
                    viewer.zoomTo(entity);
                    // }
                });
            } else if (window.toggle == 3) {
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.type == 1) {
                            var imgUrl1 = "/static/poi/workshop-new.svg";
                            if (item.isGrey) {
                                imgUrl1 = "/static/poi/workshop-grey.svg";
                            }
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: imgUrl1, //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                proxy.$loading.show();

                                pickWorkshopkId.value = item.name;
                                pickWorkshopkIsGrey.value = item.isGrey;
                                workshopDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                                console.log(pickWorkshopkId.value);
                            });
                            const boxStyle = `
                width: 120px;
                height: 40px;
                border: 2px solid #47EBEB;
                text-align: center;
                background-color: rgba(0, 0, 0, 0.5);
                    border-radius: 10px;
                    line-height: 40px;
                `;
                            const sizeColor = `
                width: 100%;
                                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #FFFFFF;
                    font-size: 14px;
                `;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.id}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // // 添加鼠标移入事件监听器
                            // marker.on("mouseover", (e) => {
                            //   console.log("yirushijian", e.target.getPosition());
                            //   infoWindow.open(window.map1, e.target.getPosition());
                            // });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                console.log("yichu sjiajian");
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    areaData.value.forEach((item) => {
                        console.log(item.path);

                        if (item.type == 1) {
                            let polygonColor = "#1791fc";
                            if (item.isGrey) {
                                polygonColor = "#737B8C";
                            }
                            let polygon = new AMap.Polygon({
                                path: eval(item.path),
                                strokeColor: polygonColor,
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: polygonColor,
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                console.log(e);
                                proxy.$loading.show();

                                //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                                pickWorkshopkIsGrey.value = item.isGrey;
                                pickWorkshopkId.value = item.name;
                                workshopDialogShow.value = true;
                            });
                            window.map1.add([polygon]);
                        }
                    });
                    window.map1.setFitView();
                } else {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    // positionData.value.forEach((item) => {
                    //     console.log(uid,"uid",item.name)
                    //     if (item.name == uid){
                    //       selectLsit.push(item)
                    //     }
                    //
                    // });
                    //window.viewer.entities.removeAll(); //删除所有
                    //window.map1.clearMap();
                    // console.log(window.viewer.entities);
                    //initBoundary()
                    //console.log(selectLsit,"selectLsit")
                    selectLsit.forEach((item) => {
                        if (item.type == 1) {
                            var imgUrl1 = "/static/poi/workshop-new.svg";
                            if (item.isGrey) {
                                imgUrl1 = "/static/poi/workshop-grey.svg";
                            }
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: imgUrl1, //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                pickWorkshopkId.value = item.name;
                                pickWorkshopkIsGrey.value = item.isGrey;
                                workshopDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                                console.log(pickWorkshopkId.value);
                            });
                            const boxStyle = `
                width: 120px;
                height: 40px;
                border: 2px solid #47EBEB;
                text-align: center;
                background-color: rgba(0, 0, 0, 0.5);
                    border-radius: 10px;
                    line-height: 40px;
                `;
                            const sizeColor = `
                width: 100%;
                                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #FFFFFF;
                    font-size: 14px;
                `;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.id}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // // 添加鼠标移入事件监听器
                            // marker.on("mouseover", (e) => {
                            //   console.log("yirushijian", e.target.getPosition());
                            //   infoWindow.open(window.map1, e.target.getPosition());
                            // });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                console.log("yichu sjiajian");
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    window.map1.setFitView();
                }
            }
        }
    });
};
//仓库  type是2
const searchEntrepo = (val, type, uid) => {
    // positionData.value = [];
    //   areaData.value = [];
    //   areaDataThree.value = []
    if (!val) {
        changeData.value = [];
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    console.log(positionData.value);

    var aData = [];

    entrepo({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data
            for (let i = 0; i < res.data.data.length; i++) {
                if (res.data.data[i].is_point == 1) {
                    if (
                        res.data.data[i].longitude != null &&
                        res.data.data[i].latitude != null
                    ) {
                        // console.log(res.data.data[i].id);
                        // console.log(
                        //     entrepoNoGrey.value.findIndex((v) => v.id == res.data.data[i].id)
                        // );

                        positionData.value.push({
                            name: res.data.data[i].id,
                            lablename: res.data.data[i].entrepo_name,
                            type: type,
                            id: res.data.data[i].workshop_name,
                            isGrey:
                                entrepoNoGrey.value.findIndex(
                                    (v) => v.id == res.data.data[i].id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(res.data.data[i].longitude),
                            latitude: Number(res.data.data[i].latitude),
                        });
                        changeData.value.push({
                            id: res.data.data[i].id,
                            name: res.data.data[i].entrepo_name,
                            type: type,
                            // id: res.data.data[i].workshop_name,
                            isGrey:
                                entrepoNoGrey.value.findIndex(
                                    (v) => v.id == res.data.data[i].id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(res.data.data[i].longitude),
                            latitude: Number(res.data.data[i].latitude),
                        });
                    }
                } else {
                    if (res.data.data[i].points != null) {
                        let threeMiddle = [];
                        eval(res.data.data[i].points).forEach((innerItem) => {
                            innerItem.forEach((inner, index) => {
                                if (index == 0) {
                                    threeMiddle.push(Number(inner) - 0.0062);
                                } else {
                                    threeMiddle.push(Number(inner) - 0.00085);
                                }
                            });
                            threeMiddle.push(30);
                        });
                        let obj = {};
                        Reflect.set(obj, "id", res.data.data[i].id);
                        Reflect.set(
                            obj,
                            "lablename",
                            res.data.data[i].entrepo_name,
                        );
                        Reflect.set(obj, "type", type);
                        Reflect.set(
                            obj,
                            "isGrey",
                            entrepoNoGrey.value.findIndex(
                                (v) => v.id == res.data.data[i].id,
                            ) == -1
                                ? true
                                : false,
                        );
                        Reflect.set(obj, "path", threeMiddle);
                        console.log(obj, "obj");
                        areaDataThree.value.push(obj);
                        console.log(
                            areaDataThree.value,
                            "areaDataThree.value push后",
                        );
                        areaData.value.push({
                            name: res.data.data[i].id,
                            type: type,
                            isGrey:
                                entrepoNoGrey.value.findIndex(
                                    (v) => v.id == res.data.data[i].id,
                                ) == -1
                                    ? true
                                    : false,

                            lablename: res.data.data[i].entrepo_name,
                            id: res.data.data[i].workshop_name,
                            path: res.data.data[i].points,
                        });
                        changeData.value.push({
                            id: res.data.data[i].id,
                            name: res.data.data[i].entrepo_name,
                            type: type,
                            // id: res.data.data[i].workshop_name,
                            isGrey:
                                entrepoNoGrey.value.findIndex(
                                    (v) => v.id == res.data.data[i].id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(res.data.data[i].longitude),
                            latitude: Number(res.data.data[i].latitude),
                        });
                    }
                }
            }
            // console.log(positionData.value,"仓库------positionData");
            // console.log(areaData.value,"仓库------areaData");
            //   res.data.data.forEach((item) => {
            //     console.log(item, "shuju---------------------------");
            //     if (item.is_point == 1) {
            //       if (item.longitude != null && item.latitude != null) {
            //         positionData.value.push({
            //           name: item.id,
            //           lablename: item.entrepo_name,
            //           type: type,
            //           id: item.workshop_name,
            //           isGrey: true,
            //           longitude: Number(item.longitude),
            //           latitude: Number(item.latitude),
            //         });
            //       }
            //     } else {
            //       if (item.points != null) {
            //         let threeMiddle = [];
            //         eval(item.points).forEach((innerItem) => {
            //           innerItem.forEach((inner, index) => {
            //             if (index == 0) {
            //               threeMiddle.push(Number(inner) - 0.0062);
            //             } else {
            //               threeMiddle.push(Number(inner) - 0.00085);
            //             }
            //           });
            //           threeMiddle.push(30);
            //         });
            //         let obj = {};
            //         Reflect.set(obj, "id", item.id);
            //         Reflect.set(obj, "lablename", item.entrepo_name);
            //         Reflect.set(obj, "type", type);
            //         Reflect.set(obj, "path", threeMiddle);
            //         console.log(obj, "obj");
            //         areaDataThree.value.push(obj);
            //         console.log(areaDataThree.value, "areaDataThree.value push后");
            //         areaData.value.push({
            //           name: item.id,
            //           type: type,
            //           lablename: item.entrepo_name,
            //           id: item.workshop_name,
            //           path: item.points,
            //         });
            //       }
            //     }
            //   });
            // console.log("aaaaaaaaaaaaaaaaaa");
            // console.log();
            //   nextTick(() => {
            //     data_entrepo({ enterprise_name: enterpriseName.value }).then((re1) => {
            //       if (re1.data.success && re1.data.data && re1.data.data.length) {
            //         re1.data.data.forEach((x) => {
            //           console.log("test", positionData.value);
            //           // console.log(a);
            //           //   console.log(positionData.value.findIndex(i => i.name == a.id));
            //           //   let obj = positionData.value.find(i => i.name == a.id)
            //           //   console.log(positionData.value);
            //           positionData.value.forEach((a) => {
            //             if (a.type == 2 && a.name == x.id) {
            //               console.log("*-------");
            //               console.log(a);
            //               Reflect.set(a, "isGrey", false);

            //               console.log(a);
            //             }
            //           });
            //         });
            //       }
            //     });
            //   });
            nextTick(() => {
                if (window.toggle == 2) {
                    //不置灰数组
                    let positionNoGreyData = [];
                    let positionGreyData = [];
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    } else {
                        let selectLsit = resetData(
                            positionData.value,
                            "name",
                            uid,
                        );
                        selectLsit.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    }

                    putIcons(positionNoGreyData, warehouseImg);
                    putIcons(positionGreyData, warehouseGreyImg);
                    // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                    window.viewer.screenSpaceEventHandler.setInputAction(
                        function (e) {
                            var pick = viewer.scene.pick(e.position);
                            if (pick && pick.id) {
                                console.log(pick.id);
                                warehouseIsGrey.value = pick.id.isGrey;
                                openCesiumDialog(
                                    pick.id.type,
                                    pick.id._chuancan,
                                );

                                window.viewer.camera.setView({
                                    destination: Cesium.Cartesian3.fromDegrees(
                                        pick.id._longitude,
                                        pick.id._latitude,
                                        800,
                                    ),
                                });
                            }
                        },
                        Cesium.ScreenSpaceEventType.LEFT_CLICK,
                    );
                    areaDataThree.value.forEach((item) => {
                        // if (item.type == 2){
                        let polygonColor = "#737B8C";
                        if (!item.isGrey) {
                            polygonColor = "rgba(71, 235, 235, 0.30)";
                        }
                        const entity = window.viewer.entities.add({
                            //   id: item.id,
                            chuancan: item.id,
                            type: item.type,
                            name: item.id,
                            isGrey: item.isGrey,
                            longitude: Number(item.path[0]),
                            latitude: Number(item.path[1]),
                            show: true,
                            polygon: {
                                hierarchy:
                                    Cesium.Cartesian3.fromDegreesArrayHeights(
                                        item.path,
                                    ), //参数为四个角点坐标
                                zIndex: zIndex,
                                height: zIndex, //多层次
                                outline: true,
                                outlineColor:
                                    Cesium.Color.fromCssColorString("#47EBEB"),
                                outlineWidth: 15.0,
                                material:
                                    Cesium.Color.fromCssColorString(
                                        polygonColor,
                                    ),
                            },
                        });
                        viewer.zoomTo(entity);
                        // }
                    });
                } else if (window.toggle == 3) {
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            if (item.type == 2) {
                                var imgUrl1 = "/static/poi/warehouse-new.svg";
                                if (item.isGrey) {
                                    imgUrl1 = "/static/poi/warehouse-grey.svg";
                                }
                                let marker = new AMap.Marker({
                                    // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                    icon: new AMap.Icon({
                                        size: new AMap.Size(72, 92), // 图标尺寸
                                        image: imgUrl1, //绝对路径
                                        imageSize: new AMap.Size(72, 92),
                                    }),
                                    position: [item.longitude, item.latitude],
                                    offset: new AMap.Pixel(-36, -92),
                                });
                                marker.on("click", (e) => {
                                    proxy.$loading.show();
                                    warehouseId.value = item.name;
                                    console.log(
                                        "ceshihhahahhahahahha",
                                        item.isGrey,
                                    );
                                    warehouseIsGrey.value = item.isGrey;
                                    warehouseDialogShow.value = true;
                                    //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                                });
                                const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                                const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

                                // const box = `<div style="${boxStyle}"></div>`;
                                let infoWindow = new AMap.InfoWindow({
                                    isCustom: true,
                                    // 设置信息窗口的内容
                                    content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.lablename}</div>
                       </div>`,
                                    // 设置信息窗口的偏移量
                                    offset: new AMap.Pixel(0, -85),
                                });
                                // 添加鼠标移入事件监听器
                                marker.on("mouseover", (e) => {
                                    infoWindow.open(
                                        window.map1,
                                        e.target.getPosition(),
                                    );
                                });

                                // 添加鼠标移出事件监听器
                                marker.on("mouseout", (e) => {
                                    infoWindow.close();
                                });
                                marker.setMap(window.map1);
                            }
                        });
                        areaData.value.forEach((item) => {
                            if (item.type == 2) {
                                let polygonColor = "#1791fc";
                                if (item.isGrey) {
                                    polygonColor = "#737B8C";
                                }
                                let polygon = new AMap.Polygon({
                                    path: eval(item.path),
                                    strokeColor: polygonColor,
                                    strokeWeight: 6,
                                    strokeOpacity: 0.2,
                                    fillOpacity: 0.4,
                                    fillColor: polygonColor,
                                    zIndex: 50,
                                    bubble: true,
                                });
                                polygon.on("click", (e) => {
                                    proxy.$loading.show();
                                    warehouseId.value = item.name;
                                    warehouseIsGrey.value = item.isGrey;
                                    warehouseDialogShow.value = true;
                                    //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                                });
                                window.map1.add([polygon]);
                            }
                        });
                        window.map1.setFitView();
                    } else {
                        let selectLsit = resetData(
                            positionData.value,
                            "name",
                            uid,
                        );
                        selectLsit.forEach((item) => {
                            if (item.type == 2) {
                                var imgUrl1 = "/static/poi/warehouse-new.svg";
                                if (item.isGrey) {
                                    imgUrl1 = "/static/poi/warehouse-grey.svg";
                                }
                                let marker = new AMap.Marker({
                                    // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                    icon: new AMap.Icon({
                                        size: new AMap.Size(72, 92), // 图标尺寸
                                        image: imgUrl1, //绝对路径
                                        imageSize: new AMap.Size(72, 92),
                                    }),
                                    position: [item.longitude, item.latitude],
                                    offset: new AMap.Pixel(-36, -92),
                                });
                                marker.on("click", (e) => {
                                    warehouseId.value = item.name;
                                    console.log(
                                        "ceshihhahahhahahahha",
                                        item.isGrey,
                                    );
                                    warehouseIsGrey.value = item.isGrey;
                                    warehouseDialogShow.value = true;
                                    //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                                });
                                const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                                const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

                                // const box = `<div style="${boxStyle}"></div>`;
                                let infoWindow = new AMap.InfoWindow({
                                    isCustom: true,
                                    // 设置信息窗口的内容
                                    content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.lablename}</div>
                       </div>`,
                                    // 设置信息窗口的偏移量
                                    offset: new AMap.Pixel(0, -85),
                                });
                                // 添加鼠标移入事件监听器
                                marker.on("mouseover", (e) => {
                                    infoWindow.open(
                                        window.map1,
                                        e.target.getPosition(),
                                    );
                                });

                                // 添加鼠标移出事件监听器
                                marker.on("mouseout", (e) => {
                                    infoWindow.close();
                                });
                                marker.setMap(window.map1);
                            }
                        });
                    }
                }
            });
        }
    });
};
//罐区  type是3
const searchTankForm = (val, type, uid) => {
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
        changeData.value = [];
    }
    tank_farm({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data

            res.data.data.forEach((item) => {
                if (item.is_point == 1) {
                    positionData.value.push({
                        name: item.id,
                        type: type,
                        id: item.tank_farm_name,
                        isGrey:
                            tankNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,

                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                    changeData.value.push({
                        id: item.id,
                        type: type,
                        name: item.tank_farm_name,
                        isGrey:
                            tankNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,

                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                    // console.log( positionData.value,'走的1')
                    // console.log( changeData.value,'走的1')
                } else {
                    if (item.points != null) {
                        let threeMiddle = [];
                        eval(item.points).forEach((innerItem) => {
                            innerItem.forEach((inner, index) => {
                                if (index == 0) {
                                    threeMiddle.push(Number(inner) - 0.0062);
                                } else {
                                    threeMiddle.push(Number(inner) - 0.00085);
                                }
                            });
                            threeMiddle.push(30);
                        });
                        let obj = {};
                        Reflect.set(obj, "id", item.id);
                        Reflect.set(obj, "type", type);
                        Reflect.set(
                            obj,
                            "isGrey",
                            tankNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,
                        );
                        Reflect.set(obj, "path", threeMiddle);
                        areaDataThree.value.push(obj);
                        areaData.value.push({
                            name: item.id,
                            type: type,
                            isGrey:
                                tankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            id: item.tank_farm_name,
                            path: item.points,
                        });
                        changeData.value.push({
                            id: item.id,
                            type: type,
                            name: item.tank_farm_name,
                            isGrey:
                                tankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,

                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                        // console.log( positionData.value,'走的1')
                        // console.log( changeData.value,'走的222')
                        // console.log( areaData.value,'走的222')
                    }
                }
            });
            if (window.toggle == 2) {
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                console.log(positionData.value, "positionData.value");
                let selectLsit = [];
                let selectLsitOne = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    selectLsit = resetData(positionData.value, "id", uid);
                    if (selectLsit.length) {
                        selectLsit.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    } else {
                        positionGreyData = [];
                        positionNoGreyData = [];
                    }
                }
                putIcons(positionNoGreyData, tankNoGreyImg);
                putIcons(positionGreyData, tankGreyImg);

                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);
                        tankIsGrey.value = pick.id.isGrey;

                        openCesiumDialog(pick.id.type, pick.id._chuancan);
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
                if (!uid) {
                    areaDataThree.value.forEach((item) => {
                        // if (item.type ==3){
                        let polygonColor = "#737B8C";
                        if (!item.isGrey) {
                            polygonColor = "rgba(71, 235, 235, 0.30)";
                        }
                        const entity = window.viewer.entities.add({
                            // id: item.id,
                            chuancan: item.id,
                            type: item.type,
                            isGrey: item.isGrey,
                            name: item.id,
                            longitude: Number(item.path[0]),
                            latitude: Number(item.path[1]),
                            show: true,
                            polygon: {
                                hierarchy:
                                    Cesium.Cartesian3.fromDegreesArrayHeights(
                                        item.path,
                                    ), //参数为四个角点坐标
                                zIndex: zIndex,
                                height: zIndex, //多层次
                                outline: true,
                                outlineColor:
                                    Cesium.Color.fromCssColorString("#47EBEB"),
                                outlineWidth: 15.0,
                                material:
                                    Cesium.Color.fromCssColorString(
                                        polygonColor,
                                    ),
                            },
                        });
                        //    viewer.zoomTo(entity);
                        // }
                    });
                } else {
                    selectLsitOne = resetData(areaDataThree.value, "id", uid);
                    selectLsitOne.forEach((item) => {
                        // if (item.type ==3){
                        let polygonColor = "#737B8C";
                        if (!item.isGrey) {
                            polygonColor = "rgba(71, 235, 235, 0.30)";
                        }
                        const entity = window.viewer.entities.add({
                            // id: item.id,
                            chuancan: item.id,
                            type: item.type,
                            isGrey: item.isGrey,
                            name: item.id,
                            longitude: Number(item.path[0]),
                            latitude: Number(item.path[1]),
                            show: true,
                            polygon: {
                                hierarchy:
                                    Cesium.Cartesian3.fromDegreesArrayHeights(
                                        item.path,
                                    ), //参数为四个角点坐标
                                zIndex: zIndex,
                                height: zIndex, //多层次
                                outline: true,
                                outlineColor:
                                    Cesium.Color.fromCssColorString("#47EBEB"),
                                outlineWidth: 15.0,
                                material:
                                    Cesium.Color.fromCssColorString(
                                        polygonColor,
                                    ),
                            },
                        });
                        //    viewer.zoomTo(entity);
                        // }
                    });
                }
            } else if (window.toggle == 3) {
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.type == 3) {
                            var imgUrl1 = "/static/poi/tank-new.svg";
                            if (item.isGrey) {
                                imgUrl1 = "/static/poi/tank-grey.svg";
                            }
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: imgUrl1, //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                proxy.$loading.show();
                                tankId.value = item.name;
                                tankIsGrey.value = item.isGrey;
                                tankDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                            });
                            const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                            const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 16px;
`;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.id}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // 添加鼠标移入事件监听器
                            marker.on("mouseover", (e) => {
                                infoWindow.open(
                                    window.map1,
                                    e.target.getPosition(),
                                );
                            });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    areaData.value.forEach((item) => {
                        if (item.type == 3) {
                            let polygonColor = "#1791fc";
                            if (item.isGrey) {
                                polygonColor = "#737B8C";
                            }
                            let polygon = new AMap.Polygon({
                                path: eval(item.path),
                                strokeColor: polygonColor,
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: polygonColor,
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                proxy.$loading.show();
                                tankId.value = item.name;
                                tankIsGrey.value = item.isGrey;
                                tankDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                            });
                            window.map1.add([polygon]);
                        }
                    });
                    window.map1.setFitView();
                } else {
                    // console.log(uid,"选中id")
                    let selectLsit = resetData(areaData.value, "name", uid);
                    let selectLsitOne = resetData(
                        positionData.value,
                        "name",
                        uid,
                    );
                    // console.log(selectLsit,"selectLsit333",changeData.value)
                    selectLsitOne.forEach((item) => {
                        if (item.type == 3) {
                            var imgUrl1 = "/static/poi/tank-new.svg";
                            if (item.isGrey) {
                                imgUrl1 = "/static/poi/tank-grey.svg";
                            }
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: imgUrl1, //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                tankId.value = item.name;
                                tankIsGrey.value = item.isGrey;
                                tankDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                            });
                            const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                            const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 16px;
`;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.id}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // 添加鼠标移入事件监听器
                            marker.on("mouseover", (e) => {
                                infoWindow.open(
                                    window.map1,
                                    e.target.getPosition(),
                                );
                            });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    selectLsit.forEach((item) => {
                        if (item.type == 3) {
                            let polygonColor = "#1791fc";
                            if (item.isGrey) {
                                polygonColor = "#737B8C";
                            }
                            let polygon = new AMap.Polygon({
                                path: eval(item.path),
                                strokeColor: polygonColor,
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: polygonColor,
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                tankId.value = item.name;
                                tankIsGrey.value = item.isGrey;
                                tankDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                            });
                            window.map1.add([polygon]);
                        }
                    });
                    window.map1.setFitView();
                }
            }
        }
    });
};

const searchDangerChemicalProcess = (val, type) => {
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    // positionData.value = [];
    //   areaData.value = [];
    //   areaDataThree.value = []
    danger_chemical_process_list({
        enterprise_name: enterpriseName.value,
    }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data
            res.data.data.forEach((item) => {
                if (item.is_point == 1) {
                    if (item.longitude != null && item.latitude != null) {
                        positionData.value.push({
                            name: item.id,
                            craftName: item.craft_name,
                            type: type,
                            id: item.id,
                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                    }
                } else {
                    if (item.points != null) {
                        areaData.value.push({
                            name: item.id,
                            type: type,
                            craftName: item.craft_name,
                            id: item.id,
                            path: item.points,
                        });
                    }
                }
            });
            if (window.toggle == 2) {
                putIcons(positionData.value, highRiskProcessesImg);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);

                        openCesiumDialog(pick.id.type, pick.id._chuancan);

                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
                areaDataThree.value.forEach((item) => {
                    const entity = window.viewer.entities.add({
                        //   id: item.id,
                        chuancan: item.id,
                        type: item.type,
                        isGrey: item.isGrey,
                        name: item.id,
                        longitude: Number(item.path[0]),
                        latitude: Number(item.path[1]),
                        show: true,
                        polygon: {
                            hierarchy:
                                Cesium.Cartesian3.fromDegreesArrayHeights(
                                    item.path,
                                ), //参数为四个角点坐标
                            zIndex: zIndex,
                            height: zIndex, //多层次
                            outline: true,
                            outlineColor:
                                Cesium.Color.fromCssColorString("#47EBEB"),
                            outlineWidth: 15.0,
                            material: Cesium.Color.fromCssColorString(
                                "rgba(71, 235, 235, 0.30)",
                            ),
                        },
                    });
                    viewer.zoomTo(entity);
                });
            } else if (window.toggle == 3) {
                positionData.value.forEach((item) => {
                    if (item.type == 6) {
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: "/static/poi/highRiskProcesses-new.svg", //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            highriskProcessesId.value = item.name;
                            highriskProcessesDialogShow.value = true;
                            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                        });
                        const boxStyle = `
                width: 120px;
                height: 40px;
                border: 2px solid #47EBEB;
                text-align: center;
                background-color: rgba(0, 0, 0, 0.5);
                border-radius: 10px;
                line-height: 40px;
                `;
                        const sizeColor = `
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #FFFFFF;
                font-size: 14px;
                `;

                        // const box = `<div style="${boxStyle}"></div>`;
                        let infoWindow = new AMap.InfoWindow({
                            isCustom: true,
                            // 设置信息窗口的内容
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.craftName}</div>
                       </div>`,
                            // 设置信息窗口的偏移量
                            offset: new AMap.Pixel(0, -85),
                        });
                        // 添加鼠标移入事件监听器
                        marker.on("mouseover", (e) => {
                            infoWindow.open(
                                window.map1,
                                e.target.getPosition(),
                            );
                        });

                        // 添加鼠标移出事件监听器
                        marker.on("mouseout", (e) => {
                            infoWindow.close();
                        });
                        marker.setMap(window.map1);
                    }
                });
                areaData.value.forEach((item) => {
                    if (item.type == 6) {
                        let polygon = new AMap.Polygon({
                            path: eval(item.path),
                            strokeColor: "#FF33FF",
                            strokeWeight: 6,
                            strokeOpacity: 0.2,
                            fillOpacity: 0.4,
                            fillColor: "#1791fc",
                            zIndex: 50,
                            bubble: true,
                        });
                        polygon.on("click", (e) => {
                            highriskProcessesId.value = item.name;
                            highriskProcessesDialogShow.value = true;
                            //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                        });
                        window.map1.add([polygon]);
                    }
                });
                window.map1.setFitView();
            }
        }
    });
};
const searchDangerChemicalList = (val, type) => {
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    // positionData.value = [];
    //     areaData.value = [];
    //     areaDataThree.value = []
    danger_chemical_list({ enterprise_name: enterpriseName.value }).then(
        (res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                // positionData.value = res.data.data
                res.data.data.forEach((item) => {
                    if (item.judge == "1") {
                        if (
                            item.position != undefined &&
                            item.position != null
                        ) {
                            positionData.value.push({
                                name: item.id,
                                chemicalName: item.chemical_name,
                                id: item.id,
                                type: type,
                                longitude: Number(eval(item.position)[0].lng),
                                latitude: Number(eval(item.position)[0].lat),
                            });
                        }
                    } else {
                        if (item.position != null) {
                            let points = [];
                            let threeMiddle = [];
                            eval(item.position).forEach((x) => {
                                let middlePoints = [];
                                middlePoints.push(Number(x.lng) - 0.0062);
                                middlePoints.push(Number(x.lat) - 0.00085);
                                threeMiddle.push(Number(x.lng) - 0.0062);
                                threeMiddle.push(Number(x.lat) - 0.00085);
                                threeMiddle.push(30);
                                points.push(middlePoints);
                                //
                            });
                            let obj = {};
                            Reflect.set(obj, "id", item.id);
                            Reflect.set(obj, "type", type);
                            Reflect.set(
                                obj,
                                "chemicalName",
                                item.chemical_name,
                            );
                            Reflect.set(obj, "path", threeMiddle);
                            areaDataThree.value.push(obj);
                            areaData.value.push({
                                name: item.id,
                                id: item.source_name,
                                chemicalName: item.chemical_name,
                                type: type,
                                path: points,
                            });
                        }
                    }
                });

                if (window.toggle == 2) {
                    putIcons(positionData.value, hazardousChemicalsImg);
                    // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                    window.viewer.screenSpaceEventHandler.setInputAction(
                        function (e) {
                            var pick = viewer.scene.pick(e.position);
                            if (pick && pick.id) {
                                console.log(pick.id);

                                openCesiumDialog(
                                    pick.id.type,
                                    pick.id._chuancan,
                                );

                                window.viewer.camera.setView({
                                    destination: Cesium.Cartesian3.fromDegrees(
                                        pick.id._longitude,
                                        pick.id._latitude,
                                        800,
                                    ),
                                });
                            }
                        },
                        Cesium.ScreenSpaceEventType.LEFT_CLICK,
                    );
                    areaDataThree.value.forEach((item) => {
                        // if (item.type ==8){

                        const entity = window.viewer.entities.add({
                            //   id: item.id,
                            chuancan: item.id,
                            type: item.type,
                            isGrey: item.isGrey,
                            name: item.id,
                            longitude: Number(item.path[0]),
                            latitude: Number(item.path[1]),
                            show: true,
                            polygon: {
                                hierarchy:
                                    Cesium.Cartesian3.fromDegreesArrayHeights(
                                        item.path,
                                    ), //参数为四个角点坐标
                                zIndex: zIndex,
                                height: zIndex, //多层次
                                outline: true,
                                outlineColor:
                                    Cesium.Color.fromCssColorString("#47EBEB"),
                                outlineWidth: 15.0,
                                material: Cesium.Color.fromCssColorString(
                                    "rgba(71, 235, 235, 0.30)",
                                ),
                            },
                        });
                        viewer.zoomTo(entity);
                        // }
                    });
                } else if (window.toggle == 3) {
                    positionData.value.forEach((item) => {
                        if (item.type == 8) {
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: "/static/poi/hazardousChemicals-new.svg", //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                hazardousChemicalsId.value = item.name;
                                hazardousChemicalsDialogShow.value = true;
                                proxy.$loading.show();
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                            });
                            const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                            const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.chemicalName}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // 添加鼠标移入事件监听器
                            marker.on("mouseover", (e) => {
                                infoWindow.open(
                                    window.map1,
                                    e.target.getPosition(),
                                );
                            });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    areaData.value.forEach((item) => {
                        if (item.type == 8) {
                            let polygon = new AMap.Polygon({
                                path: item.path,
                                strokeColor: "#FF33FF",
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: "#1791fc",
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                hazardousChemicalsId.value = item.name;
                                hazardousChemicalsDialogShow.value = true;
                                proxy.$loading.show();
                                //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                            });
                            window.map1.add([polygon]);
                        }
                    });
                    window.map1.setFitView();
                }
            }
        },
    );
};
//储罐 type是9
const searchStorageTank = (val, type, uid) => {
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
        changeData.value = [];
    }
    // positionData.value = [];
    //   areaData.value = [];
    //   areaDataThree.value = []
    storage_tank({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data

            res.data.data.forEach((item) => {
                if (item.is_point == 1) {
                    if (item.longitude != null && item.latitude != null) {
                        positionData.value.push({
                            name: item.id,
                            storageName: item.storage_name,
                            id: item.id,
                            type: type,
                            isGrey:
                                storageTankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                        changeData.value.push({
                            // name: item.id,
                            name: item.storage_name,
                            id: item.id,
                            type: type,
                            isGrey:
                                storageTankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                    }
                } else {
                    if (item.points != null) {
                        let threeMiddle = [];
                        eval(item.points).forEach((innerItem) => {
                            innerItem.forEach((inner, index) => {
                                if (index == 0) {
                                    threeMiddle.push(Number(inner) - 0.0062);
                                } else {
                                    threeMiddle.push(Number(inner) - 0.00085);
                                }
                            });
                            threeMiddle.push(30);
                        });
                        let obj = {};
                        Reflect.set(obj, "id", item.id);
                        Reflect.set(obj, "type", type);
                        Reflect.set(
                            obj,
                            "isGrey",
                            storageTankNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,
                        );
                        Reflect.set(obj, "storageName", item.storage_name);
                        Reflect.set(obj, "path", threeMiddle);
                        areaDataThree.value.push(obj);
                        areaData.value.push({
                            name: item.id,
                            id: item.id,
                            storageName: item.storage_name,
                            type: type,
                            isGrey:
                                storageTankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            path: item.points,
                        });
                        changeData.value.push({
                            // name: item.id,
                            name: item.storage_name,
                            id: item.id,
                            type: type,
                            isGrey:
                                storageTankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                    }
                }
            });
            if (window.toggle == 2) {
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                }

                putIcons(positionNoGreyData, storageNoGreyTankImg);
                putIcons(positionGreyData, storageGreyTankImg);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);

                        openCesiumDialog(pick.id.type, pick.id._chuancan);
                        monitoringPointskIsGrey.value = pick.id.isGrey;
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
                areaDataThree.value.forEach((item) => {
                    // if (item.type == 9){
                    let polygonColor = "#737B8C";
                    if (!item.isGrey) {
                        polygonColor = "rgba(71, 235, 235, 0.30)";
                    }
                    const entity = window.viewer.entities.add({
                        // id: item.id,
                        chuancan: item.id,
                        type: item.type,
                        isGrey: item.isGrey,
                        name: item.id,
                        longitude: Number(item.path[0]),
                        latitude: Number(item.path[1]),
                        show: true,
                        polygon: {
                            hierarchy:
                                Cesium.Cartesian3.fromDegreesArrayHeights(
                                    item.path,
                                ), //参数为四个角点坐标
                            zIndex: zIndex,
                            height: zIndex, //多层次
                            outline: true,
                            outlineColor:
                                Cesium.Color.fromCssColorString("#47EBEB"),
                            outlineWidth: 15.0,
                            material:
                                Cesium.Color.fromCssColorString(polygonColor),
                        },
                    });
                    viewer.zoomTo(entity);
                    // }
                });
            } else if (window.toggle == 3) {
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.type == 9) {
                            var imgUrl1 = "/static/poi/storageTank-new.svg";
                            if (item.isGrey) {
                                imgUrl1 = "/static/poi/storageTank-grey.svg";
                            }
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: imgUrl1, //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                proxy.$loading.show();
                                monitoringPointskId.value = item.name;
                                monitoringPointskIsGrey.value = item.isGrey;

                                monitoringPointsDialogShow.value = true;

                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                            });
                            const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                            const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.storageName}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // 添加鼠标移入事件监听器
                            marker.on("mouseover", (e) => {
                                infoWindow.open(
                                    window.map1,
                                    e.target.getPosition(),
                                );
                            });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    areaData.value.forEach((item) => {
                        if (item.type == 9) {
                            let polygonColor = "#1791fc";
                            if (item.isGrey) {
                                polygonColor = "#737B8C";
                            }
                            let polygon = new AMap.Polygon({
                                path: eval(item.path),
                                strokeColor: polygonColor,
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: polygonColor,
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                proxy.$loading.show();
                                monitoringPointskId.value = item.name;
                                monitoringPointskIsGrey.value = item.isGrey;
                                monitoringPointsDialogShow.value = true;
                                //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                            });
                            window.map1.add([polygon]);
                        }
                    });
                    window.map1.setFitView();
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        if (item.type == 9) {
                            var imgUrl1 = "/static/poi/storageTank-new.svg";
                            if (item.isGrey) {
                                imgUrl1 = "/static/poi/storageTank-grey.svg";
                            }
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: imgUrl1, //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                monitoringPointskId.value = item.name;
                                monitoringPointskIsGrey.value = item.isGrey;

                                monitoringPointsDialogShow.value = true;
                                proxy.$loading.show();
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                            });
                            const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                            const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.storageName}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // 添加鼠标移入事件监听器
                            marker.on("mouseover", (e) => {
                                infoWindow.open(
                                    window.map1,
                                    e.target.getPosition(),
                                );
                            });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    window.map1.setFitView();
                }
            }
        }
    });
};
//企业档案
const searchFiles = () => {
    positionData.value = [];
    areaData.value = [];
    areaDataThree.value = [];
    enterprise_list({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            // positionData.value = res.data.data
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        isGrey: false,
                        id: item.enterprise_name,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            console.log(positionData.value);
            console.log(areaData.value);
            if (window.toggle == 2) {
                console.log(positionData.value);
                putIcons(positionData.value, ParkArchivesImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    console.log(e.position);
                    if (pick && pick.id) {
                        console.log(pick);
                        filesId.value = pick.id.name;
                        filesDialogShow.value = true;
                        //   filesName.value = item.name
                        // monitoringPointskId.value = item.name;
                        //   monitoringPointsDialogShow.value = true;
                        // enterpriseInfoCount(pick.id.name);
                        // pickId.value = pick.id.name;
                        // surroundingId.value = pick.id.id;
                        // enterpriseDialog.value = true;
                        // console.log(pick.id._longitude);
                        proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                console.log(window.map1, positionData.value);
                positionData.value.forEach((item) => {
                    let marker = new AMap.Marker({
                        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                        icon: new AMap.Icon({
                            size: new AMap.Size(72, 92), // 图标尺寸
                            image: "/static/poi/ParkArchives-poi-new.svg", //绝对路径
                            imageSize: new AMap.Size(72, 92),
                        }),
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-36, -92),
                    });
                    marker.on("click", (e) => {
                        // surroundingId.value = item.name;
                        console.log(e.target._opts.position);
                        console.log(item);
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                        // enterpriseInfoCount(item.id);
                        // pickId.value = item.id;
                        // enterpriseDialog.value = true;
                        filesId.value = item.id;
                        filesDialogShow.value = true;
                        proxy.$loading.show();
                        filesName.value = item.name;
                        console.log(filesDialogShow.value);
                    });
                    const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                    const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 16px;
`;

                    // const box = `<div style="${boxStyle}"></div>`;
                    let infoWindow = new AMap.InfoWindow({
                        isCustom: true,
                        // 设置信息窗口的内容
                        content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.id}</div>
                       </div>`,
                        // 设置信息窗口的偏移量
                        offset: new AMap.Pixel(0, -85),
                    });
                    // 添加鼠标移入事件监听器
                    marker.on("mouseover", (e) => {
                        console.log(item, "eeeee");

                        infoWindow.open(window.map1, e.target.getPosition());
                    });

                    // 添加鼠标移出事件监听器
                    marker.on("mouseout", (e) => {
                        infoWindow.close();
                    });
                    marker.setMap(window.map1);
                    marker.setMap(window.map1);
                });
                window.map1.setFitView();
            }
        }
    });
};

let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let chartData = ref([]);
const openCesiumDialog = (val, id) => {
    console.log("jfksdjfhjsdfjdgfjhf", val);
    switch (val) {
        case 1:
            pickWorkshopkId.value = id;
            workshopDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 2:
            warehouseId.value = id;
            warehouseDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 3:
            tankId.value = id;
            tankDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 9:
            monitoringPointskId.value = id;
            monitoringPointsDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 6:
            highriskProcessesId.value = id;
            highriskProcessesDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 8:
            hazardousChemicalsId.value = id;
            hazardousChemicalsDialogShow.value = true;
            proxy.$loading.show();
            break;
        default:
            break;
    }
};
import ParkArchivesImg from "/static/poi/ParkArchives-poi-new.svg";
import workshopImg from "/static/poi/workshop-new.svg";
import workshopGreyImg from "/static/poi/workshop-grey.svg";
import warehouseImg from "/static/poi/warehouse-new.svg";
import warehouseGreyImg from "/static/poi/warehouse-grey.svg";
import tankNoGreyImg from "/static/poi/tank-new.svg";
import tankGreyImg from "/static/poi/tank-grey.svg";
import storageGreyTankImg from "/static/poi/storageTank-grey.svg";
import storageNoGreyTankImg from "/static/poi/storageTank-new.svg";
import majorHazardImg1 from "/static/poi/majorHazard1.svg";
import majorHazardImg2 from "/static/poi/majorHazard2.svg";
import majorHazardImg3 from "/static/poi/majorHazard3.svg";
import majorHazardImg4 from "/static/poi/majorHazard4.svg";
import majorHazardImgGrey from "/static/poi/majorHazard-grey.svg";
import riskPointsImg1 from "/static/poi/riskPoints1.svg";
import riskPointsImg2 from "/static/poi/riskPoints2.svg";
import riskPointsImg3 from "/static/poi/riskPoints3.svg";
import riskPointsImg4 from "/static/poi/riskPoints4.svg";
import highRiskProcessesImg from "/static/poi/highRiskProcesses-new.svg";
import hazardousChemicalsImg from "/static/poi/hazardousChemicals-new.svg";
import videoImg from "/static/poi/video-new.svg";

// const aliveImg = ref('../../../assets/images/card/interval.svg')
const totalData = ref([]); //风险点查id对应的name
const positionData = ref([]);
const areaData = ref([]);
const areaDataThree = ref([]);
const changeData = ref([]);
const adjustField = () => {
    const arrs = []; //经纬度数组
    const lonArr = []; //经纬度数组
    const latArr = []; //经纬度数组

    positionData.value.forEach((item) => {
        //循环数据 push经纬度
        arrs.push(item.longitude);
        lonArr.push(item.longitude);
        arrs.push(item.latitude);
        latArr.push(item.latitude);
    });
    if (areaDataThree.value != []) {
        areaDataThree.value.forEach((item) => {
            for (let i = 0; i < item.path.length; i++) {
                // 根据索引值判断要添加到哪个目标数组
                if (i % 3 === 0) {
                    arrs.push(item.path[i]);
                    lonArr.push(item.path[i]);
                } else if (i % 3 === 1) {
                    arrs.push(item.path[i]);
                    latArr.push(item.path[i]);
                }
            }
        });
    }
    let adr = Cesium.Cartesian3.fromDegreesArray(arrs);
    let polys = Cesium.BoundingSphere.fromPoints(adr).center;
    polys = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polys);
    let ellipso = window.viewer.scene.globe.ellipsoid;
    let cartesian = new Cesium.Cartesian3(polys.x, polys.y, polys.z);
    let cartographic2 = ellipso.cartesianToCartographic(cartesian);

    let obj2 = {};
    obj2.lat = Cesium.Math.toDegrees(cartographic2.latitude);
    obj2.lng = Cesium.Math.toDegrees(cartographic2.longitude);
    obj2.alt = cartographic2.height;
    const maxLon = Math.max.apply(Math, lonArr);
    const minLon = Math.min.apply(Math, lonArr);
    const maxLat = Math.max.apply(Math, latArr);
    const minLat = Math.min.apply(Math, latArr);
    const lat = maxLat - minLat;
    const lon = maxLon - minLon;
    let radius = lat > lon ? lat : lon;
    //   console.log(radius);
    let h = radius * 550000;
    if (radius == 0) {
        radius = 0.002;
    }
    if (radius > 1) {
        // console.log(11111111);
        h = radius * 200000; //高度 自行调整  250000
    }
    if (radius < 0.004) {
        h = radius * 1150000;
    }
    if (radius < 0.002) {
        h = radius * 9550000;
    }

    nextTick(() => {
        window.viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(obj2.lng, obj2.lat, h),
        });
    });
};
/**
 * 视图定位方法，定位到范围
 * 范围数组（最西、最南、最东、最北）
 */
let locationRectEntity = null;
const viewerFlyToRange = (rect) => {
    let maxlat = 0; //维度最大
    let minlat = 90;
    let maxlag = 0;
    let minlag = 120; //经度最小
    positionData.value.forEach((item) => {
        //循环数据 push经纬度
        maxlag = item.longitude > maxlag ? item.longitude : maxlag;
        minlag = item.longitude < minlag ? item.longitude : minlag;
        maxlat = item.latitude > maxlat ? item.latitude : maxlat;

        minlat = item.latitude < minlat ? item.latitude : minlat;
    });
    if (locationRectEntity) window.viewer.entities.remove(locationRectEntity);
    locationRectEntity = window.viewer.entities.add({
        // name: 'locationRectangle',
        // id: 'locationRectangle',
        rectangle: {
            coordinates: Cesium.Rectangle.fromDegrees(
                minlag,
                minlat,
                maxlag,
                maxlat,
            ),
            material: Cesium.Color.GREEN.withAlpha(0),
            height: 10.0,
            outline: false,
        },
    });
    // let flyPromise = window.viewer.flyTo(locationRectEntity, {
    //     duration: 5,
    //     offset: new Cesium.HeadingPitchRange(0.0, Cesium.Math.toRadians(-20.0))
    // });

    //倾斜后记录相机到矩形中心点的距离，然后再根据倾斜角度，计算新的距离，
    // 再viewer.flyto的完成后，再通过中心点离相机的距离，使用zoomTo来拉近一下距离
    flyPromise.then(function () {
        let center = Cesium.Rectangle.center(
            Cesium.Rectangle.fromDegrees(minlag, minlat, maxlag, maxlat),
        );
        let car = Cesium.Cartesian3.fromRadians(
            center.longitude,
            center.latitude,
        );
        let range =
            Cesium.Cartesian3.distance(car, window.viewer.camera.position) *
            Math.cos(20);
        window.viewer.zoomTo(
            locationRectEntity,
            new Cesium.HeadingPitchRange(
                0.0,
                Cesium.Math.toRadians(-20.0),
                range,
            ),
        );
    });
};
const putIcons = (_datas, img, _parent) => {
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        console.log(data.isGrey);
        const entity = window.viewer.entities.add({
            name: data.id,
            // 参数顺序：经度、纬度
            isGrey: data.isGrey, //是否置灰
            chuancan: data.name,
            type: data.type,
            longitude: Number(data.longitude) - 0.0062,
            latitude: Number(data.latitude) - 0.00085,
            position: Cesium.Cartesian3.fromDegrees(
                Number(data.longitude) - 0.0062,
                Number(data.latitude) - 0.00085,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 72,
                height: 92,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                pixelOffset: new Cesium.Cartesian2(0, -46),
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            //   type: "text", // 自定义属性
        });
        // window.viewer.zoomTo(entity);
        // console.log(entity);
        // console.log(999999999999999999999999999999999);
        adjustField();
    }
};
//经度小数点第三位减6，纬度小数点第四位减7
const lvdi = ref([
    [115.125599, 37.578102], //1
    [115.125513, 37.572903], //2
    [115.135652, 37.572903], //3
    [115.134629, 37.581748], //4
    [115.133329, 37.581748], //5
    [115.133329, 37.582221], //6
    [115.130859, 37.582018], //7
    [115.130859, 37.578305], //8

    [115.131966, 37.61058], //9
    [115.141167, 37.610685], //10
    [115.146075, 37.610348], //11
    [115.146075, 37.609336], //12
    [115.141167, 37.609606], //13
    [115.131966, 37.609636], //14

    [115.146553, 37.610078], //15
    [115.165467, 37.608121], //16
    [115.165467, 37.607311], //17
    [115.146553, 37.609133], //18

    [115.160274, 37.593259], //19
    [115.163252, 37.593259], //20
    [115.163842, 37.591706], //21
    [115.160274, 37.591706], //22
]);
const greenLandData = ref([
    [
        115.125599, 37.578102, 30, 115.125513, 37.572903, 30, 115.135652,
        37.572903, 30, 115.134629, 37.581748, 30, 115.133329, 37.581748, 30,
        115.133329, 37.582221, 30, 115.130859, 37.582018, 30, 115.130859,
        37.578305, 30, 115.125599, 37.578102, 30,
    ],
    [
        115.131966, 37.61058, 30, 115.141167, 37.610685, 30, 115.146075,
        37.610348, 30, 115.146075, 37.609336, 30, 115.141167, 37.609606, 30,
        115.131966, 37.609636, 30, 115.131966, 37.61028, 30,
    ],
    [
        115.146553, 37.610078, 30, 115.165467, 37.608121, 30, 115.165467,
        37.607311, 30, 115.146553, 37.609133, 30, 115.146553, 37.610078, 30,
    ],
    [
        115.160274, 37.593259, 30, 115.163252, 37.593259, 30, 115.163252,
        37.591706, 30, 115.160274, 37.591706, 30, 115.160274, 37.593259, 30,
    ],
]);

const shuiyuan = ref([
    [115.132254, 37.611152, 30, 115.134733, 37.581151, 30],
    [115.134733, 37.581151, 30, 115.135765, 37.574157, 30], //竖向左
    [
        115.135058, 37.57239, 30, 115.132503, 37.57254, 30, 115.1308, 37.57269,
        30, 115.12938, 37.57254, 30, 115.129002, 37.573365, 30,
    ], //线条外侧
]);
//内侧和外侧比内侧第四位比外侧少3
const waterData = ref([
    [
        115.132254, 37.611152, 30, 115.134733, 37.581151, 30, 115.134733,
        37.581151, 30, 115.135765, 37.574157, 30, 115.135465, 37.574157, 30,
        115.134433, 37.581151, 30, 115.134433, 37.581151, 30, 115.131954,
        37.611152, 30,
    ], //右侧
    [
        115.135365,
        37.574457,
        30, //右下
        115.129654,
        37.574557,
        30, //左下
        115.129654,
        37.574857,
        30, //左上
        115.135365,
        37.574857,
        30, //右上
    ], //中间
    [
        115.129554, 37.577657, 30, 115.129554, 37.574857, 30, 115.127054,
        37.574857, 30, 115.127054, 37.577657, 30,
    ],
]);
</script>

<style lang="less" scoped>
.input-wrapper {
    display: flex;
    width: 660px;
    height: 40px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 496px;
    .right_select {
        margin-left: 10px;
    }
    :deep(.el-input__wrapper) {
        background-color: transparent;
        box-shadow: 0 0 0 0;
        border-radius: 4px;
        border: 1px solid #30abe8;
        height: 22px;
        padding: 7px 12px;
    }

    :deep(.el-input__inner) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-input__inner::placeholder) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}

.comprehensive-supervision-navigation1 {
    width: 158px;
    height: 672px;
    z-index: 1000;
    position: absolute;
    top: 458px;
    left: 496px;

    > div:not(:first-child) {
        margin-top: 24px;
    }
}

.comprehensive-supervision-navigation2 {
    width: 158px;
    height: 672px;
    z-index: 1000;
    position: absolute;
    top: 458px;
    left: 496px;

    > div:not(:first-child) {
        margin-top: 24px;
    }
}

.comprehensive-supervision-navigation3 {
    width: 158px;
    height: 672px;
    z-index: 1000;
    position: absolute;
    top: 574px;
    left: 496px;

    > div:not(:first-child) {
        margin-top: 24px;
    }
}

.btn {
    width: 156px;
    height: 34px;
    background: url("../../../assets/images/jump-btn/nav-big.svg") no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
}

.active-btn {
    width: 156px;
    height: 34px;
    background: url("../../../assets/images/jump-btn/nav-active-big.svg")
        no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
    // gap: 24px;
}

.noactive {
    width: 156px;
    height: 34px;
    background: url("../../../assets/images/jump-btn/nav-noactive-big.svg")
        no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
}

.icon {
    width: 24px;
    height: 24px;
    // margin: auto 0;
    margin-top: 4px;
    margin-left: 12px;
}

.text {
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin: auto 0;
    margin-left: 24px;
}

.visual-angle-btns {
    z-index: 1000;
    position: absolute;
    top: 846px;
    left: 1348px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 157.143% */
    text-align: center;

    .map-btn {
        width: 130px;
        height: 32px;
        margin-top: 24px;
        background: url("../../../assets/images/jump-btn/self-nav.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }

    .map-btn-active {
        width: 130px;
        height: 32px;
        margin-top: 24px;
        background: url("../../../assets/images/jump-btn/self-nav-active.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }
}

.map-btns {
    z-index: 1000;
    position: absolute;
    top: 790px;
    left: 1348px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 157.143% */
    text-align: center;

    .map-btn {
        width: 130px;
        height: 32px;
        margin-top: 24px;
        background: url("../../../assets/images/jump-btn/self-nav.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }

    .map-btn-active {
        width: 130px;
        height: 32px;
        margin-top: 24px;
        background: url("../../../assets/images/jump-btn/self-nav-active.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }
}
</style>
