<template>
    <div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">车辆详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                  <div class="container">
                    <div v-if="dialogData.type == 1" class="error_log_1">
                        <div class="log_content">{{ "违停" }}</div>
                    </div>
                    <div v-if="dialogData.type == 2" class="error_log">
                        <div class="log_content">{{ "未按车道行驶" }}</div>
                    </div>
                    <div
                        v-if="dialogData.type != 1 && dialogData.type != 2"
                        class="success_log"
                    >
                        <div class="log_content">{{ "暂无违规" }}</div>
                    </div>
                    <div class="container_top">
                        <div class="contain_title" :title="limitData.goodsTypeName">
                            <div class="title_top">
                                {{ limitData.goodsTypeName_1 || "" }}
                            </div>
                        </div>
                        <div class="contain_content">
                            <div class="contain_name">
                                {{ dialogData.goodsName || "" }}
                            </div>
                            <el-row>
                                <el-col :span="12" class="innner special">
                                    <div class="headline">电子运单：</div>
                                    <div
                                        class="content"
                                        :title="dialogData.orderNumber"
                                    >
                                        {{ dialogData.orderNumber || "" }}
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12" class="innner special">
                                    <div class="headline">驾驶员姓名：</div>
                                    <div class="content" :title="dialogData.driverName">
                                        {{ dialogData.driverName || "" }}
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12" class="innner special">
                                    <div class="headline">电话：</div>
                                    <div
                                        class="content"
                                        :title="dialogData.driverPhone"
                                    >
                                        {{ dialogData.driverPhone || "" }}
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <div class="details">
                        <el-row :gutter="10">
                            <el-col :span="12" class="innner special">
                                <div class="headline">车牌号：</div>
                                <div class="content" :title="dialogData.carPlate">
                                    {{ dialogData.carPlate || "" }}
                                </div>
                            </el-col>
                            <el-col :span="12" class="innner special">
                                <div class="headline">货物信息：</div>
                                <div class="content" :title="dialogData.goodsName">
                                    {{ dialogData.goodsName || "" }}
                                </div>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="12" class="innner">
                                <div class="headline">货物装载量：</div>
                                <div class="content">
                                    {{ dialogData.goodsWeight || "" }}
                                </div>
                            </el-col>
                            <el-col :span="12" class="innner">
                                <div class="headline">危险品分类：</div>
                                <div class="content">
                                    {{ limitData.goodsTypeName || "" }}
                                </div>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="12" class="innner">
                                <div class="headline">押运员姓名：</div>
                                <div class="content">
                                    {{ dialogData.escortName || "" }}
                                </div>
                            </el-col>
                            <el-col :span="12" class="innner">
                                <div class="headline">押运员电话：</div>
                                <div class="content">
                                    {{ dialogData.escortPhone || "" }}
                                </div>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="12" class="innner">
                                <div class="headline">访问企业：</div>
                                <div class="content">
                                    {{ dialogData.bevisitCompany || "" }}
                                </div>
                            </el-col>
                            <el-col :span="12" class="innner">
                                <div class="headline">企业联系人姓名：</div>
                                <div class="content">
                                    {{ dialogData.bevisitPerson || "" }}
                                </div>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="12" class="innner">
                                <div class="headline">企业联系人电话：</div>
                                <div class="content">
                                    {{ dialogData.bevisitNumber || "" }}
                                </div>
                            </el-col>
                            <el-col :span="12" class="innner">
                                <div class="headline">预计开始时间：</div>
                                <div class="content">
                                    {{ dialogData.planTime || "" }}
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    <div style="margin-top: 20px" class="bottom-title">
                        <div class="icon"></div>
                        <div class="text">违规照片</div>
                    </div>
                    <img :src="dialogData.alarmPic" class="video" id="carLine" />
                </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
   
</template>

<script setup>
import { ref, watch, defineEmits, toRefs } from "vue";
import {
    getEquipmentInfo,
    getFlv,
    tank_farm_info,
} from "../../../../assets/js/api/safetySupervision";
import { nj_equipment_info } from "../../../../assets/js/api/parkArchives";
import {
    carGpsLine,
    getDictByType,
} from "../../../../assets/js/api/comprehensiveSupervision";
import AMapLoader from "@amap/amap-jsapi-loader";

const checked1 = ref(false);
const checked2 = ref(false);
const boundary = ref(false);
const speed = ref(false);
let map = null;
var jessibuca = null;
const img = ref(null);
const clickId = ref();
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const dialogData = ref({
    carPlate: "",
    goodsName: "",
    goodsWeight: "",
    goodsType: "",
    driverName: "",
    driverPhone: "",
    bevisitCompany: "",
    bevisitPerson: "",
    bevisitNumber: "",
    planTime: "",
    goodsTypeName: "",
});
const actual = ref([]);
const project = ref([]);
const pointList = ref([]);
const goodsType = ref([]);
const limitData = ref({
    speedLimit: 999,
});
const trueData = ref(null);
import url from "../../../../assets/images/closed/car_success.svg";
const isTrue = () => {
    if (
        trueData.value.heavyTruckEdit.speed >
        trueData.value.drivePlan.speedLimit
    ) {
        return false;
    } else return true;
};
const dealWith = (e) => {
    return Number(e.toString().substring(0, 5));
};
const initPathSimplifier = (path1) => {
    // let path = [[116.478935, 39.997761], [116.478939, 39.997825], [116.478912, 39.998549], [116.478912, 39.998549], [116.478998, 39.998555], [116.478998, 39.998555], [116.479282, 39.99856], [116.479658, 39.998528], [116.480151, 39.998453], [116.480784, 39.998302], [116.480784, 39.998302], [116.481149, 39.998184], [116.481573, 39.997997], [116.481863, 39.997846], [116.482072, 39.997718], [116.482362, 39.997718], [116.483633, 39.998935], [116.48367, 39.998968], [116.484648, 39.999861]];
    console.log(actual);
    let path = actual.value[0];
    map = new AMap.Map("carLine", {
        //设置地图容器id
        viewMode: "3D", //是否为3D地图模式
        zoom: 12, //初始化地图级别
        mapStyle: "amap://styles/db6d462a4571314863ef03382b234c33",
        //   layers: [new AMap.TileLayer.Satellite()],
        center: [path[0][0], path[0][1]], //初始化地图中心点位置
    });
    let marker = new AMap.Marker({
        icon: url,
        position: [path[0][0], path[0][1]],
        offset: new AMap.Pixel(-23, -60),
        exData: {
            data: dialogData.value,
        },
    });
    marker.setMap(map);
    // marker.on("click", () => {
    console.log(path, map);
    // marker.moveAlong(path, {
    //   duration: 500, //可根据实际采集时间间隔设置
    //   autoRotation: false,
    // })
    // marker.setMap(map)
    // 绘制轨迹
    // var polyline = new AMap.Polyline({
    //   map: map,
    //   path: path,
    //   showDir: true,
    //   strokeColor: "#28F", //线颜色
    //   // strokeOpacity: 1,     //线透明度
    //   strokeWeight: 6, //线宽
    //   // strokeStyle: "solid"  //线样式
    // })
    let lineArr = []; // 存储轨迹的一维数组
    let mapList = new Map();
    let arr = [];
    let arr1 = [];
    let arr2 = [];
    pointList.value.forEach((pos, index) => {
        let marker1 = new AMap.CircleMarker({
            center: [pos.lng, pos.lat], //圆心
            radius: 10.1, //半径
            strokeColor: "white", //轮廓线颜色
            strokeWeight: 0.1, //轮廓线宽度
            strokeOpacity: 0, //轮廓线透明度
            fillColor: "rgba(255,255,255,0)", //多边形填充颜色
            fillOpacity: 0, //多边形填充透明度
            zIndex: 99, //多边形覆盖物的叠加顺序
            cursor: "pointer", //鼠标悬停时的鼠标样式
            exData: {
                data: trueData.value,
            },
        });
        marker1.on("mouseover", (e) => {
            let item = e.target._opts.exData.data;
            if (pos.speed > item.drivePlan.speedLimit) {
                window.infoWindowone = new AMap.InfoWindow({
                    position: [pos.lng, pos.lat],
                    offset: new AMap.Pixel(0, 0),
                    content: `超速：${pos.speed}km/h,参考值${item.drivePlan.speedLimit}km/h`,
                });
                window.infoWindowone.open(map);
            }
        });
        map.add(marker1);
        if (pointList.value.length == 1) {
            if (
                !project.value
                    .map((e) => dealWith(e[0]))
                    .includes(dealWith(pos.lng)) ||
                !project.value
                    .map((e) => dealWith(e[1]))
                    .includes(dealWith(pos.lat))
            )
                boundary.value = true;
            if (pos.speed > limitData.value.speedLimit) speed.value = true;
        }
        if (
            project.value.length != 0 &&
            (!project.value
                .map((e) => dealWith(e[0]))
                .includes(dealWith(pos.lng)) ||
                !project.value
                    .map((e) => dealWith(e[1]))
                    .includes(dealWith(pos.lat)))
        ) {
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
            if (arr.length != 0) {
                speed.value = true;
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            arr1.push([pos.lng, pos.lat]);
        } else if (pos.speed > limitData.value.speedLimit) {
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
            if (arr1.length != 0) {
                boundary.value = true;
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            arr.push([pos.lng, pos.lat]);
        } else {
            if (arr.length != 0) {
                speed.value = true;
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            if (arr1.length != 0) {
                boundary.value = true;
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            arr2.push([pos.lng, pos.lat]);
        }
        if (index == pointList.value.length - 1) {
            if (arr.length != 0) {
                speed.value = true;
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            if (arr1.length != 0) {
                boundary.value = true;
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
        }
    });
    console.log(mapList);
    let objKeyArr = Array.from(mapList.keys());
    for (let i = 0; i < objKeyArr.length; i++) {
        if (i < objKeyArr.length - 1) {
            mapList.get(objKeyArr[i]).push(mapList.get(objKeyArr[i + 1])[0]);
        }
    }
    mapList.forEach((key, i) => {
        let color = ""; // 定义轨迹的颜色变量
        let type = i.split("_"); // 获取轨迹的类型
        if (type[1] == "speed") {
            // 根据轨迹的类型进行颜色的赋值
            color = "#ff8119";
        } else if (type[1] == "boundary") {
            color = "#ff0000";
        } else if (type[1] == "success") {
            color = "#AF5";
        }
        // 配置轨迹
        name = new AMap.Polyline({
            map: map,
            path: key, // 轨迹的坐标数组
            showDir: true,
            strokeColor: color,
            strokeWeight: 6, //线宽
            lineJoin: "round",
        });
        map.add([name]);
        lineArr = lineArr.concat(key);
    });
    // })
    var passedPolyline = new AMap.Polyline({
        map: map,
        strokeColor: "#AF5", //线颜色
        strokeWeight: 6, //线宽
    });
    if (lineArr.length != 0)
        marker.moveAlong(lineArr, {
            duration: 500, //可根据实际采集时间间隔设置
            autoRotation: false,
        });
    // marker.on("moving", function (e) {
    //   passedPolyline.setPath(e.passedPath)
    //   map.setCenter(e.target.getPosition())
    // })
    // })
    map.setFitView();
    console.log(map);
};
const initPathSimplifier1 = (path1) => {
    // let path = [[116.478935, 39.997761], [116.478939, 39.997825], [116.478912, 39.998549], [116.478912, 39.998549], [116.478998, 39.998555], [116.478998, 39.998555], [116.479282, 39.99856], [116.479658, 39.998528], [116.480151, 39.998453], [116.480784, 39.998302], [116.480784, 39.998302], [116.481149, 39.998184], [116.481573, 39.997997], [116.481863, 39.997846], [116.482072, 39.997718], [116.482362, 39.997718], [116.483633, 39.998935], [116.48367, 39.998968], [116.484648, 39.999861]];
    console.log(actual);
    let path = project.value;
    map = new AMap.Map("carLine", {
        //设置地图容器id
        viewMode: "3D", //是否为3D地图模式
        zoom: 12, //初始化地图级别
        mapStyle: "amap://styles/db6d462a4571314863ef03382b234c33",
        //   layers: [new AMap.TileLayer.Satellite()],
        center: [path[0][0], path[0][1]], //初始化地图中心点位置
    });
    let marker = new AMap.Marker({
        icon: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
        position: [path[0][0], path[0][1]],
        offset: new AMap.Pixel(-13, -30),
    });
    marker.setMap(map);
    marker.on("click", () => {
        console.log(path, map);
        marker.moveAlong(path, {
            // 每一段的时长
            duration: 500, //可根据实际采集时间间隔设置
            // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
            autoRotation: true,
        });
        marker.setMap(map);
        // 绘制轨迹
        var polyline = new AMap.Polyline({
            map: map,
            path: path,
            showDir: true,
            strokeColor: "#ff2222", //线颜色
            // strokeOpacity: 1,     //线透明度
            strokeWeight: 6, //线宽
            // strokeStyle: "solid"  //线样式
        });

        var passedPolyline = new AMap.Polyline({
            map: map,
            strokeColor: "#ff5555", //线颜色
            strokeWeight: 6, //线宽
        });

        marker.on("moving", function (e) {
            passedPolyline.setPath(e.passedPath);
            map.setCenter(e.target.getPosition());
        });
    });
    // map.setView()
    console.log(map);
};
watch(
    pickId,
    (a, b) => {
        console.log(a, b);
        dialogData.value = a;
        clickId.value = a;
        if (a != undefined) {
            getDictByType({
                type: "goodsType",
            }).then((goods) => {
                goodsType.value = goods.data.data;
                Reflect.set(
                    a.drivePlan,
                    "goodsTypeName",
                    goods.data.data.find((u) => u.code == a.goodsType) ==
                        undefined
                        ? ""
                        : goods.data.data.find((u) => u.code == a.goodsType)
                              .name,
                );
                Reflect.set(
                    a.drivePlan,
                    "goodsTypeName_1",
                    goods.data.data.find((u) => u.code == a.goodsType) ==
                        undefined
                        ? ""
                        : goods.data.data
                              .find((u) => u.code == a.goodsType)
                              .name.substring(0, 3),
                );
            });
        }
    },
    {
        immediate: true,
    },
);
watch(
    checked1,
    (a, b) => {
        if (a) {
            initPathSimplifier1();
        } else {
        }
    },
    {
        immediate: true,
    },
);
watch(
    checked2,
    (a, b) => {
        if (a) {
            initPathSimplifier();
        } else {
        }
    },
    {
        immediate: true,
    },
);
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
const emit = defineEmits(["closeDialog"]);
const typeActive = ref(1);
const closeDialog = () => {
    destroy();
    console.log("video");
    emit("closeDialog", "carData");
};
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 732px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 732px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 636px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.title_top {
    color: #fff;
    text-align: center;
    font-family: "Noto Sans SC";
    font-size: 28px;
    font-style: normal;
    font-weight: 700;
    line-height: 166px;
    width: 135px;
    overflow: hidden; //超出的文本隐藏
    //text-overflow:ellipsis; //溢出用省略号显示
    white-space: nowrap;
    margin: auto;
}

.contain_title {
    width: 166px;
    height: 166px;
    background: url("../../../../assets/images/closed/level_background.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
}

.infrastructure-dialog {
    width: 840px;
    height: 732px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}

.header {
    width: 840px;
    height: 52px;
    flex-shrink: 0;
    background: url("../../../../assets/images/dialog/header52.svg") no-repeat
        center center;
    background-size: cover;
    background-position: center;
    display: flex;

    .title {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 52px; /* 150% */
        margin-left: 32px;
    }

    .close {
        width: 24px;
        height: 24px;
        background: url("../../../../assets/images/dialog/close.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: auto 0;
        margin-left: 696px;
    }
}

.container {
    width: 792px;
    height: 680px;
    padding: 16px 24px;
    // background-color: aquamarine;
    .bottom-title {
        width: 70px;
        height: 24px;
        display: flex;
        gap: 4px;
        // margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }

        .text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }

    .details {
        margin-top: 10px;
        width: 792px;
        height: 169px;
        // background-color: #47ebeb;
        .innner {
            display: flex;
            padding: 0 !important;

            .headline {
                width: 198px;
                height: 36px;
                color: #47ebeb;
                text-align: left;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 36px; /* 150% */
                border: 1px solid #30abe8;
                background: rgba(48, 171, 232, 0.1);
                padding: 0px 12px;
            }

            .content {
                width: 198px;
                height: 36px;
                border: 1px solid #30abe8;
                //width: 251px;
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 36px; /* 150% */
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 0px 12px;
            }
        }

        .special {
            margin-top: 0px;
        }
    }

    .video {
        width: 792px;
        height: 244px;
        flex-shrink: 0;
        background: rgba(48, 171, 232, 0.15);
        // margin-top: 17px;
    }
}

.select {
    z-index: 999;
    position: absolute;
}

.container_top {
    display: flex;
}

.contain_content {
    .contain_name {
        color: #fff;
        text-align: left;
        font-family: "Noto Sans SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: 26px;
        width: 118px;
        padding: 0px 12px;
    }

    .innner {
        display: flex;
        padding: 0 !important;

        .headline {
            width: 218px;
            height: 36px;
            color: #47ebeb;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 150% */
            padding: 0px 12px;
        }

        .content {
            width: 218px;
            height: 36px;
            //width: 251px;
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 150% */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 0px 12px;
        }
    }
}
.success_log {
    width: 124px;
    height: 96px;
    background: url("../../../../assets/images/closed/title_success.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    right: 100px;
    top: 52px;
}
.error_log {
    width: 124px;
    height: 96px;
    background: url("../../../../assets/images/closed/title_error.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    right: 100px;
    top: 52px;
}
.error_log_1 {
    width: 104px;
    height: 96px;
    background: url("../../../../assets/images/closed/title_error.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    right: 220px;
    top: 52px;
}
.log_content {
    color: #fff;
    text-align: center;
    font-family: "Noto Sans SC";
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-top: 25px;
}
</style>
