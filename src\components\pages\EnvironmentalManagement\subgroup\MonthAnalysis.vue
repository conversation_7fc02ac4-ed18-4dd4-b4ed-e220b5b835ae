<template>
    <div class="month-analysis">
        <div class="inner">
            <div class="week">
                <div class="img">
                    <div class="num">{{ week }}</div>
                </div>
                <div class="title">本周环比</div>
            </div>
            <div class="month">
                <div class="img">
                    <div class="num">{{ month }}</div>
                </div>
                <div class="title">本月环比</div>
            </div>
            <div class="year">
                <div class="img">
                    <div class="num">{{ year }}</div>
                </div>
                <div class="title">本年环比</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import { chain_ratio } from "../../../../assets/js/api/environmentalManagement";
const week = ref(0);
const month = ref(0);
const year = ref(0);
onMounted(() => {
    chain_ratio({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            let result = res.data.data;
            let curYearSum = Number(
                result.find((i) => i.time == "cur_year").sum,
            );
            let lastYearSum = Number(
                result.find((i) => i.time == "last_year").sum,
            );
            if (lastYearSum == 0) {
                year.value = 0;
            } else {
                year.value = (
                    ((curYearSum - lastYearSum) / lastYearSum) *
                    100
                ).toFixed(1);
            }
            let curMonthSum = Number(
                result.find((i) => i.time == "cur_month").sum,
            );
            let lastMonthSum = Number(
                result.find((i) => i.time == "last_month").sum,
            );
            if (lastMonthSum == 0) {
                month.value = 0;
            } else {
                month.value = (
                    ((curMonthSum - lastMonthSum) / lastMonthSum) *
                    100
                ).toFixed(1);
            }
            let curWeekSum = Number(
                result.find((i) => i.time == "cur_week").sum,
            );
            let lastWeekSum = Number(
                result.find((i) => i.time == "last_week").sum,
            );
            if (lastWeekSum == 0) {
                week.value = 0;
            } else {
                week.value = (
                    ((curWeekSum - lastWeekSum) / lastWeekSum) *
                    100
                ).toFixed(1);
            }
        }
    });
});
</script>

<style lang="less" scoped>
.month-analysis {
    width: 416px;
    height: 196px;
    .inner {
        width: 398px;
        height: 196px;
        margin: 40px 9px;
        display: flex;
        // gap: 32px;
        justify-content: space-between;
        .week {
            .img {
                width: 106px;
                height: 106px;      
                background: url("../../../../assets/images/card/week.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
        .month {
            .img {
                width: 106px;
                height: 106px;
                background: url("../../../../assets/images/card/month.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
        .year {
            .img {
                width: 106px;
                height: 106px;
                background: url("../../../../assets/images/card/year.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
        .num {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 106px; /* 140% */
        }
        .title {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            margin-top: 8px;
        }
    }
}
</style>
