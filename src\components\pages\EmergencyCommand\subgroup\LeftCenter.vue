<template>
    <Card title="事件报告">
        <template v-slot>
    <div class="energy-consumption-trends">
        <div class="top_box">
            <div>
                <div class="top_box_number">{{ infoList.death }}</div>
                <div>
                    <img
                        class="image_box"
                        src="../../../../assets/images/command/left_center_1.svg"
                    />
                </div>
                <div class="top_box_text">累计死亡人数(人)</div>
            </div>
        </div>
        <div class="buttom_box">
            <div>
                <div class="top_box_number">{{ infoList.injured }}</div>
                <div>
                    <img
                        class="image_box"
                        src="../../../../assets/images/command/left_center_1.svg"
                    />
                </div>
                <div class="top_box_text">累计受伤人数(人)</div>
            </div>
            <div>
                <div class="top_box_number">{{ infoList.economic }}</div>
                <div>
                    <img
                        class="image_box"
                        src="../../../../assets/images/command/left_center_1.svg"
                    />
                </div>
                <div class="top_box_text">累计经济损失(万元)</div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import { geteventReport } from "../../../../assets/js/api/api";

const infoList = ref({
    death: 0,
    economic: 0,
    injured: 0,
});

onMounted(() => {
    geteventReport({}).then((res) => {
        infoList.value.death = res.data.data[0].death_number;
        infoList.value.economic = Number(
            res.data.data[0].economic_loss,
        ).toFixed(2);
        infoList.value.injured = res.data.data[0].injured_number;
        console.log(infoList.value, "事件报告");
    });
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.energy-consumption-trends {
    width: 416px;
    height: 224px;
    padding: 16px;
    .image_box {
        width: 144px;
        height: 68px;
        flex-shrink: 0;
    }
    .top_box_number {
        color: #47ebeb;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 28px;
        font-style: normal;
        font-weight: 700;
        line-height: 30px; /* 128.571% */
    }
    .top_box_text {
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        margin-top: -12px;
    }
    .top_box {
        display: flex;
        justify-content: center;
    }
    .buttom_box {
        display: flex;
        justify-content: space-around;
    }
    .select-btns {
        width: 248px;
        height: 24px;
        display: flex;
        gap: 8px;
        margin-left: 152px;
        .active-btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.5px solid #ffc61a;
            background: rgba(255, 198, 26, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
        .btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.622px solid #1ab2ff;
            background: rgba(26, 178, 255, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
    }
    .linewrap {
        width: 400px;
        height: 232px;
        // margin-top: 16px;
        // background-color: aqua;
    }
}
</style>
