<template>
    <div class="bottom-all-wrapper">
        <div class="top-content">
                        <div class="select-option">
                            <div class="option-item">
                                <div class="title">企业名称</div>
                                <el-input
                                    v-model="queryParams.enterpriseName"
                                    placeholder="请输入"
                                    clearable
                                />
                            </div>
                        </div>
                        <div class="select-btns">
                            <div class="search-btn" @click="getData">
                                搜索
                            </div>
                            <div class="reset-btn" @click="resetSearch">
                                重置
                            </div>
                        </div>
                    </div>
        <div class="bottom">
            <el-table class="tablebox" :data="tableData" style="width: 100%">
                <el-table-column
                    prop="enterpriseName"
                    label="企业名称"
                    width="220px"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="solidWaste"
                    label="固废贮存场"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="solidWasteUnit"
                    label="固废处置单元"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="solidWasteSource"
                    label="固废源"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="trash"
                    label="废物名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="wasteCode"
                    label="危废代码"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="wasteType"
                    label="危废类别"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                v-model:currentPage="pageNum"
                :page-size="pageSize"
                @current-change="handleCurrentChange"
                layout="->,total, prev, pager, next"
                :total="total"
            />
        </div>
    </div>
</template>

<script setup>
import {
    solid_waste_page,
} from "@/assets/js/api/dialog/secureBase";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    getCurrentInstance,
    toRefs
} from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
    sonId: {
        type: [String, Number],
    },
});
const { sonId } = toRefs(props);
let queryParams = ref({});
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);

const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData();
};
const resetSearch = () =>{
    queryParams.value = {};
    pageNum.value = 1;
    getData();
}
const getData = () => {
    solid_waste_page({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        enterpriseId:sonId.value,
        ...queryParams.value,
    }).then((res) => {
        console.log(res);
        if (res.data && res.data.data) {
            tableData.value=res.data.data.list;
            total.value = res.data.data.total;
            // tableData.value = res.data.data.list.map((i) => {
            //     return {
            //         ...i,
            //         awardFileObj: i.awardFile ? JSON.parse(i.awardFile) : "",
            //     };
            // });
        }
    });
};

onMounted(() => {
    console.log(sonId.value,'llsssssaaaaaaaaa');
    
    getData();
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.bottom-all-wrapper{
    width: 1120px;
    height: 600px;
display: flex;
        flex-direction: column;
        gap:20px;

}
.top-content {
            display: flex;
            justify-content: space-between;

            .select-option {
                width: 1120px;
                height: 40px;
                display: flex;
                gap: 20px;
                align-items: center;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                letter-spacing: 0%;
                text-align: right;
                color: rgba(128, 234, 255, 1);

                .option-item {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                }
                .title {
                    // padding-right: 12px;
                    line-height: 40px; /* 157.143% */
                }
                .el-input {
                    width: 210px;
                    height: 40px;
                }
                .el-select {
                    width: 210px;
                }
                .el-date-editor {
                    width: 210px;
                }
                :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
               
            }
            .select-btns {
                width: 192px;
                height: 40px;
                display: flex;
                gap: 16px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 40px; /* 142.857% */
                .search-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #30abe8;
                    background: rgba(48, 171, 232, 0.3);
                }
                .reset-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #ffc61a;
                    background: rgba(255, 198, 26, 0.3);
                }
            }
            .select2 {
                margin-top: 16px;
            }
        }
.bottom {
    width: 1120px;
    height: 504px;
    .tablebox {
        //表格四个边框的颜色
        // border: 1px solid #30abe8 !important;
        border: none !important;
        border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

        th.el-table__cell {
            border: none !important;
        }
    }

    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        // border-bottom: 1px solid #30abe8 !important;
        height: 40px;
        background-color: rgba(25, 159, 255, 0.1) !important;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        text-align: center;
        color: rgba(128, 234, 255, 1);
    }

    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: none !important;
        }
    }

    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
    }

    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    // 表头样式（去掉底部边框）
    :deep(.el-table__header) {
        .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
        }
    }

    // 表格内容样式（保留底部边框）
    :deep(.el-table__body) {
        .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(255, 255, 255, 1);
        }
    }

    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }

    .pagination {
        margin-top: 16px;
    }

    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .btn-next) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .btn-prev) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination__total) {
        background-color: transparent;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: right;
        vertical-align: middle;
        color: rgba(26, 159, 255, 1);
    }
}
</style>
