<template>
    <div class="smart-energy-right">
        <!-- <wrap-slot title1="今日值班值守" title2="事件上报" title3="应急演练">
            <template #content1>
            </template>
            <template #content2>
            </template>
            <template #content3>
            </template>
        </wrap-slot> -->
        <right-top></right-top>
        <right-center></right-center>
        <right-bottom-second></right-bottom-second>

    </div>
</template>

<script setup>
import WrapSlot from "../../commenNew/WrapSlot.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightCenter from "./subgroup/RightCenter.vue";
import RightBottom from "./subgroup/RightBottom.vue";
import RightBottomNew from "./subgroup/RightBottomNew.vue";
import RightBottomSecond from "./subgroup/RightBottomSecond.vue";
</script>

<style lang="less" scoped>
.smart-energy-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
