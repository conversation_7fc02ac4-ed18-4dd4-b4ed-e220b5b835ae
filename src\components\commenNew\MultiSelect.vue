<template>
    <div class="select_show" ref="selectBox">
      <div class="select_show_choose">
        {{ selectedLabels }}
      </div>
      <div class="select_content" v-if="showSelect" ref="selectContent">
        <div class="select_content_filter">
          <div
            v-for="item in sonList"
            :key="item.value"
            class="select_content_little"
            @click="toggleChoose(item)"
            :title="item.label"
            :class="{ selected: isSelected(item) }"
          >
            <div class="select_content_little_label">
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
      <img
        src="@/assets/images/card/H008_1.svg"
        alt=""
        class="select_img"
        @click="isShowImg"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, watch, defineProps, defineEmits, computed, onMounted, onBeforeUnmount } from "vue";
  
  const props = defineProps({
    sonList: {
      type: Array,
      default: () => [],
    },
    chooseValue: {
      type: Array,
      default: () => [],
    },
  });
  
  const emit = defineEmits(["choose"]);
  
  const chooseValue = ref([...props.chooseValue]); // 存储选中的值（value 数组）
  const sonList = ref([...props.sonList]); // 选项列表
  const showSelect = ref(false); // 控制下拉框显示
  const selectBox = ref(null); // 选择框的 DOM 元素
  const selectContent = ref(null); // 下拉框的 DOM 元素
  
  // 监听 props.chooseValue 变化
  watch(
    () => props.chooseValue,
    (newValue) => {
      chooseValue.value = [...newValue]; // 同步父组件传递的选中值
    },
    { deep: true, immediate: true }
  );
  
  // 监听 props.sonList 变化
  watch(
    () => props.sonList,
    (newValue) => {
      sonList.value = [...newValue]; // 同步父组件传递的选项列表
    },
    { deep: true, immediate: true }
  );
  
  // 显示/隐藏下拉框
  const isShowImg = () => {
    showSelect.value = !showSelect.value;
  };
  
  // 判断当前项是否已选中
  const isSelected = (item) => {
    return chooseValue.value.includes(item.value); // 通过 value 判断
  };
  
  // 切换选中状态
  const toggleChoose = (item) => {
    if (isSelected(item)) {
      // 移除选中
      chooseValue.value = chooseValue.value.filter((value) => value !== item.value);
    } else {
      // 添加选中
      chooseValue.value.push(item.value);
    }
    // 传递给父组件的是 value 数组
    emit("choose", [...chooseValue.value]); // 确保传递的是新数组
  };
  
  // 计算选中项的展示文本
  const selectedLabels = computed(() => {
    if (chooseValue.value.length === 0) return "请选择";
  
    const selectedItems = sonList.value.filter((item) =>
      chooseValue.value.includes(item.value)
    );
  
    if (selectedItems.length === 1) {
      return selectedItems[0].label;
    } else {
      return `${selectedItems[0].label} +${selectedItems.length - 1}`;
    }
  });
  
  // 点击外部区域关闭下拉框
  const handleClickOutside = (event) => {
    if (
      selectBox.value &&
      !selectBox.value.contains(event.target) &&
      selectContent.value &&
      !selectContent.value.contains(event.target)
    ) {
      showSelect.value = false;
    }
  };
  
  onMounted(() => {
    document.addEventListener("click", handleClickOutside);
  });
  
  onBeforeUnmount(() => {
    document.removeEventListener("click", handleClickOutside);
  });
  </script>
  
  <style lang="less" scoped>
  /* 样式保持不变 */
  .select_show {
    width: 150px;
    height: 24px;
    display: flex;
    padding: 2px 4px 2px 16px;
    border-radius: 4px;
    border: 2px solid rgba(43, 217, 255, 1);
    background: rgba(13, 72, 85, 0.5) right center;
    background-size: 20px 20px;
    box-shadow: 0 0 4px 1px rgba(43, 217, 255, 1) inset;
    box-sizing: border-box;
    position: relative;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }
  
  .select_show_choose {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  
  .select_content_little_label {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  
  .select_img {
    width: 20px;
    height: 20px;
    margin-left: auto;
    cursor: pointer;
  }
  
  .select_content {
    width: 150px;
    max-height: 150px;
    overflow-y: auto;
    border: 2px solid rgba(43, 217, 255, 1);
    position: absolute;
    top: 32px;
    left: 0px;
    padding: 5px 0;
    z-index: 999;
    background: rgba(13, 72, 85, 0.9);
  }
  
  .select_content_filter {
    position: relative;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
  }
  
  .select_content_little {
    padding-left: 12px;
    box-sizing: border-box;
    position: relative;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    height: 24px;
    width: 100%;
    cursor: pointer;
  
    &:hover {
      background-color: rgba(43, 217, 255, 0.3);
    }
  
    &.selected {
      background-color: rgba(43, 217, 255, 0.5);
    }
  }
  </style>