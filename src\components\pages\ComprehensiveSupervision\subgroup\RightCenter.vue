<template>
    <Card title="实时监控">
        <template v-slot>
    <div class="monitor">
        <div class="left">
            <div class="video">
                <div ref="img" id="imgCCC" class="img"></div>
            </div>
            <div class="door-list">
                <div
                    :class="[
                        list.length > 2
                            ? 'normal-container_warp'
                            : 'normal-container',
                    ]"
                >
                    <div
                        :class="
                            active === index ? 'container' : 'container-two'
                        "
                        v-for="(item, index) in list"
                        :key="index"
                        @click="setButton(item, index)"
                    >
                        {{ item.areaName }}
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <div
                :class="alarmList.length > 3 ? 'inner first-marquee' : 'inner'"
            >
                <div
                    class="item"
                    v-for="(item, index) in alarmList"
                    :key="index"
                >
                    <div
                        :class="
                            item.pass_type == '出'
                                ? 'item-left-out'
                                : 'item-left-in'
                        "
                    ></div>
                    <div class="item-right">
                        <div class="right-top">
                            <div>{{ item.carPlate }}</div>
                            <div>{{ selectName }}</div>
                        </div>
                        <div class="right-bottom">
                            <div>{{ item.snapTime }}</div>
                            <!-- <div>{{ item.time }}</div> -->
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="alarmList.length > 3">
                <div
                    :class="
                        alarmList.length > 3 ? 'inner second-marquee' : 'inner'
                    "
                >
                    <div
                        class="item"
                        v-for="(item, index) in alarmList"
                        :key="index"
                    >
                        <div
                            :class="
                                item.pass_type == '出'
                                    ? 'item-left-out'
                                    : 'item-left-in'
                            "
                        ></div>
                        <div class="item-right">
                            <div class="right-top">
                                <div>{{ item.carPlate }}</div>
                                <div>{{ selectName }}</div>
                            </div>
                            <div class="right-bottom">
                                <div>{{ item.snapTime }}</div>
                                <!-- <div>{{ item.time }}</div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    onUnmounted,
} from "vue";
import {
    security_car_io,
    security_car_info,
} from "../../../../assets/js/api/closedManagement";
import { getFlv } from "../../../../assets/js/api/safetySupervision";
import {
    gate_video,
    nj_iocGateC,
    listAreas,
    getPassRecord,
} from "../../../../assets/js/api/comprehensiveSupervision";
import { cars_today_by_deviceId } from "../../../../assets/js/api/closedManagement";
const { proxy } = getCurrentInstance();

var jessibuca = null;
const img = ref(null);
const list = ref([]);
const active = ref(0);
const alarmList = ref([]);

const setButton = (val, type) => {
    active.value = type;
    selectId.value = val.equipment_id;
    selectArea.value = val.areaNumber;
    selectName.value = val.areaName;
    obtainCarTodayData();
    destroy();
    console.log(222, val.cameraId);
    if (val.cameraId != null) {
        getFlvPlay([val.cameraId]);
    }
};
//获取视频流地址并播放
const getFlvPlay = (id) => {
    getFlv({
        equipmentIdList: id,
    }).then((res3) => {
        if (
            res3.data[0].flvAddress &&
            res3.data[0].flvAddress.indexOf(".live.flv") != -1
        ) {
            jessibuca = new JessibucaPro({
                container: img.value,
                videoBuffer: 0.2, // 缓存时长
                isResize: false,
                decoder: "/Jessibuca/decoder-pro.js",
                text: "",
                loadingText: "加载中",
                showBandwidth: true, // 显示网速
                operateBtns: {
                    fullscreen: false,
                    screenshot: false,
                    play: false,
                    audio: false,
                    performance: false,
                },
                forceNoOffscreen: true,
                isNotMute: false,
                heartTimeout: 10,
                ptzClickType: "mouseDownAndUp",
            });
            if (res3.data[0].flvAddress)
                jessibuca.play(proxy.$changeUrl(res3.data[0].flvAddress));
        }
    });
};
const destroy = () => {
    if (jessibuca) {
        jessibuca.destroy();
        jessibuca.value = null;
    }
};
proxy.$bus.on("cars_today_by_deviceId", () => {
    obtainCarTodayData();
});
const obtainCarTodayData = () => {
    alarmList.value = [];
    if (selectArea.value != null && selectArea.value != undefined) {
        console.log(1111);
        getPassRecord({
            device_id: selectArea.value,
            pageNum: 1,
            pageSize: 3,
        }).then((res) => {
            console.log(res.data.data);

            if (res.data.data && res.data.data.records) {
                console.log(res.data.data);
                alarmList.value = res.data.data.records;
                if (alarmList.value.length > 4) {
                    alarmList.value = alarmList.value.slice(0, 4);
                }
            }
        });
    }
};
const areaList = ref([]);
const selectId = ref();
const selectArea = ref();
const selectName = ref();

onMounted(() => {
    console.log(proxy.$changeUrl);
    nj_iocGateC({ "": "" }).then((re) => {
        if (re.data.success && re.data.data && re.data.data.length) {
            if (re.data.data.length > 4) {
                list.value = re.data.data.slice(0, 4);
            } else {
                list.value = re.data.data;
            }
            selectId.value = list.value[0].equipment_id;
            selectArea.value = list.value[0].areaNumber;
            selectName.value = list.value[0].areaName;
            obtainCarTodayData();
            if (list.value[0].cameraId != null) {
                getFlvPlay([list.value[0].cameraId]);
            }
        }
    });
});
onBeforeUnmount(() => {
    destroy();
});
onUnmounted(() => {
    proxy.$bus.off("cars_today_by_deviceId");
});
</script>

<style lang="less" scoped>
.monitor {
    width: 416px;
    height: 224px;
    padding: 16px;
    display: flex;
    gap: 16px;
    .left {
        width: 217px;
        height: 224px;
        .video {
            width: 217px;
            height: 156px;
            background-color: rgba(48, 171, 232, 0.15);

            .img {
                width: 217px;
                height: 156px;
            }
        }
        .door-list {
            width: 180px;
            height: 64px;
            margin: 0 auto;
            margin-top: 4px;
            text-align: center;
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px; /* 150% */
            .normal-container {
                flex-direction: row;
                align-items: center;
                display: flex;
                gap: 14px;
            }
            .normal-container_warp {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                gap: 14px;
            }
            .container {
                width: 78px;
                height: 28px;
                background: url("../../../../assets/images/card/door-active.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .container-two {
                width: 78px;
                height: 28px;
                background: url("../../../../assets/images/card/door.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
    }
    .right {
        width: 151px;
        height: 224px;
        margin-left: 16px;
        overflow: auto;
        .item {
            display: flex;
            gap: 8px;
            margin-top: 5.5px;
            .item-left-in {
                width: 24px;
                height: 24px;
                background-color: aqua;
                background: url("../../../../assets/images/card/in.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .item-left-out {
                width: 24px;
                height: 24px;
                background: url("../../../../assets/images/card/out.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .right-top {
                // display: flex;
                gap: 8px;
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }
            .right-bottom {
                display: flex;
                gap: 8px;
                color: #47ebeb;
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 18px; /* 150% */
            }
        }
    }
    .inner {
        width: 400px;
        height: 256px;
        overflow: hidden;

        // animation: scroll 5s linear infinite;
    }
    .first-marquee {
        animation: 12s first-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes first-marquee {
        0% {
            transform: translate3d(0, 0, 0);
        }
        /* 向上移动 */
        100% {
            transform: translate3d(0, -100%, 0);
        }
    }
    .second-marquee {
        /* 因为要在第一个span播完之前就得出现第二个span，所以就延迟12s才播放 */
        animation: 12s second-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes second-marquee {
        0% {
            transform: translateY(0);
        }
        /* 向上移动 */
        100% {
            transform: translateY(-100%);
        }
    }
    .right::-webkit-scrollbar {
        // width: 3px;
        // height: 10px;
        // background-color: transparent;
        display: none;
    }
}
</style>
