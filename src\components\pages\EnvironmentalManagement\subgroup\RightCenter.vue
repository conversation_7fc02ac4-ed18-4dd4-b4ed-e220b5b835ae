<template>
    <Card title="污水排放分析">
        <template v-slot>
            <div class="wastewater-discharge">
                <div class="select-btns">
                    <div class="left-select">
                <div
                    :class="leftActive === 1 ? 'active-left-btn' : 'left-btn'"
                    @click="changeShow(1)"
                >
                    超标统计
                </div>
                <div
                    :class="leftActive === 2 ? 'active-left-btn' : 'left-btn'"
                    @click="changeShow(2)"
                >
                    超标排行
                </div>
            </div>
            <div class="right-select">
                <div
                    :class="rightActive === 1 ? 'active-btn' : 'btn'"
                    @click="changeDate(1)"
                >
                    本周
                </div>
                <div
                    :class="rightActive === 2 ? 'active-btn' : 'btn'"
                    @click="changeDate(2)"
                >
                    本月
                </div>
                <div
                    :class="rightActive === 3 ? 'active-btn' : 'btn'"
                    @click="changeDate(3)"
                >
                    本年
                </div>
            </div>
        </div>
        <div class="select">
            <el-select
                v-model="selectValueId"
                class="m-2"
                placeholder="请选择"
                @change="changeFactor"
                filterable
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </div>
        <div v-show="!nonFlag">
            <div class="totalwrap" ref="totalwrap" v-if="leftActive == 1"></div>
            <div class="progress" v-else>
                <div
                    class="progress-box"
                    v-for="(item, index) in peopleData"
                    :key="index"
                >
                    <div class="title-box">
                        <div class="title-box-left">
                            <div class="ranking">NO.{{ index + 1 }}</div>
                            <div class="name">&nbsp;{{ item.name }}</div>
                        </div>
                        <div class="title-box-right">
                            <div class="num">{{ item.value }}&nbsp;</div>
                            <div class="unit">次</div>
                        </div>
                    </div>
                    <el-progress
                        class="progress-height"
                        :stroke-width="16"
                        :percentage="item.percentage"
                    ></el-progress>
                </div>
            </div>
        </div>
        <div class="all-wrapper" v-show="nonFlag">
                    <div class="img-no">暂无数据</div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    factor_type,
    factor_emission_year,
    factor_emission_month,
    factor_emission_week,
    exceedance_statistics_year,
    exceedance_statistics_month,
    exceedance_statistics_week,
    exceedance_areastatistics_year,
    exceedance_areastatistics_month,
    exceedance_areastatistics_week,
    exceedance_enterprisestatistics_year,
    exceedance_enterprisestatistics_month,
    exceedance_enterprisestatistics_week,
} from "../../../../assets/js/api/environmentalManagement";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
const peopleData = ref([]);

const selectValue = ref(""); //所选因子name
const selectValueId = ref(""); // 所选因子id
const options = ref([]);

const leftActive = ref(1);
const rightActive = ref(1);
//暂无数据标志
const nonFlag = ref(true);
//改变图表
const changeShow = (type) => {
    leftActive.value = type;
    salvProName.value = [];
    salvProValue.value = [];
    xData = [];
    yData = [];
    if (totalChart) {
        totalChart.dispose();
    }
    console.log(rightActive.value);
    if (type == 1) {
        if (rightActive.value == 1) {
            statisticsWeek(selectValueId.value);
        } else if (rightActive.value == 2) {
            statisticsMonth(selectValueId.value);
        } else {
            statisticsYear(selectValueId.value);
        }
    } else {
        if (rightActive.value == 1) {
            ranWeek(selectValueId.value);
        } else if (rightActive.value == 2) {
            ranMonth(selectValueId.value);
        } else {
            ranYear(selectValueId.value);
        }
    }
};
//改变因子选择
const changeFactor = (val) => {
    console.log(val);
    selectValueId.value = val;
    salvProName.value = [];
    salvProValue.value = [];
    xData = [];
    yData = [];
    if (totalChart) {
        totalChart.dispose();
    }
    if (leftActive.value == 1) {
        if (rightActive.value == 1) {
            statisticsWeek(selectValueId.value);
        } else if (rightActive.value == 2) {
            statisticsMonth(selectValueId.value);
        } else {
            statisticsYear(selectValueId.value);
        }
    } else {
        if (rightActive.value == 1) {
            ranWeek(selectValueId.value);
        } else if (rightActive.value == 2) {
            ranMonth(selectValueId.value);
        } else {
            ranYear(selectValueId.value);
        }
    }
};

//改变统计维度
const changeDate = (type) => {
    rightActive.value = type;
    salvProName.value = [];
    salvProValue.value = [];
    xData = [];
    yData = [];
    console.log(leftActive.value);
    if (totalChart) {
        totalChart.dispose();
    }
    if (type == 1) {
        if (leftActive.value == 1) {
            console.log(1111111111);
            statisticsWeek(selectValueId.value);
        } else {
            ranWeek(selectValueId.value);
        }
    } else if (type == 2) {
        if (leftActive.value == 1) {
            statisticsMonth(selectValueId.value);
        } else {
            ranMonth(selectValueId.value);
        }
    } else {
        if (leftActive.value == 1) {
            statisticsYear(selectValueId.value);
        } else {
            ranYear(selectValueId.value);
        }
    }
};

//超标统计-周
const statisticsWeek = (fac) => {
    exceedance_statistics_week({ factor_id: fac }).then((res1) => {
        if (res1.data.success && res1.data.data && res1.data.data.length) {
            nonFlag.value = false;
            console.log(res1);
            res1.data.data.forEach((item) => {
                console.log(item);
                xData.push(item.datetime);
                yData.push(item.num);
            });
            totalChart = echarts.init(totalwrap.value);
            initChart();
            totalChart.setOption(option);
            window.addEventListener("resize", () => {
                totalChart.resize();
            });
        } else {
            nonFlag.value = true;
        }
    });
};
//超标统计-月
const statisticsMonth = (fac) => {
    exceedance_statistics_month({ factor_id: fac }).then((res1) => {
        if (res1.data.success && res1.data.data && res1.data.data.length) {
            nonFlag.value = false;

            console.log(res1);
            res1.data.data.forEach((item) => {
                console.log(item);
                xData.push(item.datetime);
                yData.push(item.num);
            });
            totalChart = echarts.init(totalwrap.value);
            initChart();
            totalChart.setOption(option);
            window.addEventListener("resize", () => {
                totalChart.resize();
            });
        } else {
            nonFlag.value = true;
        }
    });
};
//超标统计-年
const statisticsYear = (fac) => {
    exceedance_statistics_year({ factor_id: fac }).then((res1) => {
        if (res1.data.success && res1.data.data && res1.data.data.length) {
            nonFlag.value = false;
            console.log(res1);
            res1.data.data.forEach((item) => {
                console.log(item);
                xData.push(item.datetime);
                yData.push(item.num);
            });
            totalChart = echarts.init(totalwrap.value);
            initChart();
            totalChart.setOption(option);
            window.addEventListener("resize", () => {
                totalChart.resize();
            });
        } else {
            nonFlag.value = true;
        }
    });
};
//超标排行-周
const ranWeek = (fac) => {
    peopleData.value = [];

    exceedance_enterprisestatistics_week({ factor_id: fac }).then((res) => {
        console.log(res);
        if (res.data.success && res.data.data && res.data.data.length) {
            nonFlag.value = false;
            let max = Number(res.data.data[0].overshootCount);
            peopleData.value = res.data.data.map((x) => {
                return {
                    name: x.area_name,
                    value: x.overshootCount,
                    percentage: (Number(x.overshootCount) / max) * 100,
                };
            });
        } else {
            nonFlag.value = true;
        }
    });
};
//超标排行-月
const ranMonth = (fac) => {
    peopleData.value = [];
    exceedance_enterprisestatistics_month({ factor_id: fac }).then((res) => {
        console.log(res);
        if (res.data.success && res.data.data && res.data.data.length) {
            nonFlag.value = false;
            let max = Number(res.data.data[0].overshootCount);
            peopleData.value = res.data.data.map((x) => {
                return {
                    name: x.area_name,
                    value: x.overshootCount,
                    percentage: (Number(x.overshootCount) / max) * 100,
                };
            });
        } else {
            nonFlag.value = true;
        }
    });
};

//超标排行-年
const ranYear = (fac) => {
    peopleData.value = [];

    exceedance_enterprisestatistics_year({ factor_id: fac }).then((res) => {
        console.log(res);
        if (res.data.success && res.data.data && res.data.data.length) {
            nonFlag.value = false;

            let max = Number(res.data.data[0].overshootCount);
            peopleData.value = res.data.data.map((x) => {
                return {
                    name: x.area_name,
                    value: x.overshootCount,
                    percentage: (Number(x.overshootCount) / max) * 100,
                };
            });
        } else {
            nonFlag.value = true;
        }
    });
};

let option;
let optionRank;
let totalwrap = ref(null);
let totalChart;
let xData = ref([]);
let yData = ref([]);
const salvProName = ref([]);
const salvProValue = ref([]);
const initChart = () => {
    option = {
        grid: {
            left: 10,
            right: 30,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        tooltip: {
            trigger: "axis",
            valueFormatter: function (value) {
                return value + "次";
            },
        },
        legend: {
            show: false,
            x: "right", // 图例水平居中
            y: "top", // 图例垂直居上
            itemStyle: { opacity: 0 }, //去圆点
            textStyle: {
                color: "#fff",
            },
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    color: "#1AB2FF",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#397cbc",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "#195384",
                    },
                },
                data: xData,
            },
        ],
        yAxis: [
            {
                type: "value",
                name: "单位：次",
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#fff",
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#27b4c2",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
            },
        ],
        series: [
            {
                name: "超标统计",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: true,
                itemStyle: {
                    normal: {
                        color: "#1AB2FF",
                        lineStyle: {
                            color: "#1AB2FF",
                            width: 1,
                        },
                        areaStyle: {
                            //color: '#94C9EC'
                            color: new echarts.graphic.LinearGradient(
                                0,
                                1,
                                0,
                                0,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(26, 178, 255, 0.00)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(26, 178, 255, 0.30)",
                                    },
                                ],
                            ),
                        },
                    },
                },
                data: yData,
            },
        ],
    };
};
const initChartRank = () => {
    // var salvProName =["河北华栋化工有限责任公司","河北六合化工有限公司","河北双强合成材料有限公司","河北华荣制药有限公司","宁晋县诚源化工科技有限公司"];
    // var salvProValue =[25,18,17,13,10];
    var salvProMax = []; //背景按最大值
    for (let i = 0; i < salvProValue.value.length; i++) {
        salvProMax.push(salvProValue.value[0]);
    }
    optionRank = {
        // backgroundColor:"#003366",
        grid: {
            left: "-55%",

            right: "-50",
            bottom: "-35",
            top: "10",
            containLabel: true,
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "none",
            },
            formatter: function (params) {
                return params[0].name + " : " + params[0].value;
            },
        },
        xAxis: {
            show: false,
            type: "value",
        },
        yAxis: [
            {
                type: "category",
                show: true,
                inverse: true,
                axisLabel: {
                    interval: 0,
                    formatter: (val) => {
                        let c = document.createElement("canvas");
                        const ctx = c.getContext("2d");
                        ctx.font = "16px normal"; // 设置画布内的字体，与设置的textStyle一致
                        // ctx.fontSize = "16px"
                        ctx.height = "40px";
                        const arr = val.split("");
                        arr.map((item) => ctx.measureText(item).width).reduce(
                            (pre, next, index) => {
                                const nLen = pre + next;
                                if (nLen > 120) {
                                    arr[index - 1] += "\n";
                                    return next;
                                } else {
                                    return nLen;
                                }
                            },
                        );
                        c = null;
                        return arr.join("");
                    },
                },
                axisLabel: {
                    show: true,
                    interval: 0,
                    // shadowOffsetX: '-30px',
                    align: "left",
                    verticalAlign: "bottom",
                    lineHeight: 22,
                    fontSize: 14,
                    textStyle: {
                        color: "#fff",
                        rich: {
                            name: {
                                fontSize: 14,
                                padding: [0, 0, 8, 8],
                                fontWeight: 700,
                                color: "#ffffff",
                                lineHeight: 28,
                            },
                            title: {
                                fontSize: 14,
                                padding: [0, 0, 8, 8],
                                fontWeight: 700,
                                color: "#47EBEB",
                                lineHeight: 28,
                            },
                        },
                    },
                    formatter: function (val, index) {
                        console.log(index);
                        return (
                            "{title|NO." +
                            `${index + 1}` +
                            "}{name|" +
                            val +
                            "}"
                        );
                    },
                },
                splitLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                data: salvProName.value,
            },
            {
                type: "category",
                inverse: true,
                axisTick: "none",
                axisLine: "none",
                show: true,
                axisLabel: {
                    interval: 0,
                    // shadowOffsetX: '30px',
                    align: "right",
                    verticalAlign: "bottom",
                    // lineHeight: 28,
                    // fontSize: 14,
                    textStyle: {
                        color: "#fff",
                        rich: {
                            name: {
                                fontSize: 14,
                                padding: [0, 0, 8, 0],
                                fontWeight: 700,
                                color: "#ffffff",
                                lineHeight: 28,
                            },
                            unit: {
                                fontSize: 12,
                                padding: [0, 20, 8, 0],
                                fontWeight: 400,
                                color: "#ffffff",
                                lineHeight: 28,
                            },
                        },
                    },

                    formatter: function (val) {
                        console.log(val);
                        return "{name|" + val + "}{unit|次}";
                    },
                },
                data: salvProValue.value,
            },
        ],
        series: [
            {
                name: "值",
                type: "bar",
                zlevel: 1,
                itemStyle: {
                    normal: {
                        // barBorderRadius: 4,
                        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                            {
                                offset: 0,
                                color: "rgba(71, 235, 235, 0.15)",
                            },
                            {
                                offset: 1,
                                color: "rgba(71, 235, 235, 0.60)",
                            },
                        ]),
                    },
                },
                barWidth: 12,

                data: salvProValue.value,
            },
        ],
    };
};
onMounted(() => {
    totalChart = echarts.init(totalwrap.value);
    xData = [];
    yData = [];
    factor_type({ factorType: "水质污染" }).then((res) => {
        options.value = [];
        console.log(res);
        if (res.data.success && res.data.data && res.data.data.length) {
            options.value = res.data.data.map((x) => {
                return {
                    value: x.id,
                    label: x.factor_name,
                };
            });
            selectValueId.value = res.data.data[0].id;
            statisticsWeek(selectValueId.value);
        }
    });
});
onBeforeUnmount(() => {
    // loopShowTooltip(totalChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (totalChart) {
        // setTimeout(() => {
        totalChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.wastewater-discharge {
    // width: 400px;
    // height: 256px;
    width: 416px;
    height: 224px;
    padding: 16px;
    // background-color: antiquewhite;
    .all-wrapper {
        width: 416px;
        height: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
        .img-no {
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
    .select-btns {
        width: 400px;
        height: 24px;
        display: flex;
        justify-content: space-between;
        .left-select {
            width: 152px;
            height: 24px;
            display: flex;
            gap: 8px;
            .active-left-btn {
                width: 72px;
                height: 24px;
                justify-content: center;
                text-align: center;
                align-items: center;
                border-radius: 4px;
                border: 0.5px solid #ffc61a;
                background: rgba(255, 198, 26, 0.3);
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 133.333% */
            }
            .left-btn {
                width: 72px;
                height: 24px;
                justify-content: center;
                text-align: center;
                align-items: center;
                border-radius: 4px;
                border: 0.622px solid #1ab2ff;
                background: rgba(26, 178, 255, 0.3);
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 133.333% */
            }
        }
        .right-select {
            width: 184px;
            height: 24px;
            display: flex;
            gap: 8px;
            .active-btn {
                width: 56px;
                height: 24px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 133.333% */
                background: url("../../../../assets/images/card/active-btn.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .btn {
                width: 56px;
                height: 24px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 133.333% */
                background: url("../../../../assets/images/card/btn.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
    }
    .select {
        width: 416px;
        height: 24px;
        margin-top: 8px;
        margin-left: 278px;
        /deep/ .el-select {
            width: 122px;
            height: 24px !important;
            color: #ffffff;
        }
        /deep/ .el-input__wrapper {
            background-color: rgba(26, 178, 255, 0.3);
            // border: 1px solid #1A9FFF !important;
            // border-radius: 4px;
            height: 24px !important;
        }
        /deep/ .el-input__inner {
            // background-color: rgba(26, 178, 255, 0.30);
            color: #ffffff;
            height: 24px !important;
        }
    }
    .totalwrap {
        width: 416px;
        height: 160px;
        margin-top: 8px;
    }
    .progress {
        width: 416px;
        height: 160px;
        margin-top: 8px;
        .progress-box {
            width: 416px;
            height: 33px;
        }
        .title-box {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            display: flex;
            justify-content: space-between;
            .title-box-left {
                display: flex;
                .ranking {
                    color: #ffc61a;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 22px; /* 157.143% */
                }
            }
            .title-box-right {
                display: flex;
                .num {
                    color: #fff;
                    text-align: right;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 22px; /* 157.143% */
                }
                .unit {
                    color: #fff;
                    text-align: right;
                    font-family: Noto Sans SC;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 150% */
                }
            }
        }

        ::v-deep .el-progress-bar__inner {
            background: linear-gradient(
                270deg,
                rgba(255, 198, 26, 0.6) 0%,
                rgba(255, 198, 26, 0.15) 100%
            );
            height: 12px;
            margin: 2px;
            border-image-source: linear-gradient(
                270deg,
                rgba(255, 198, 26, 0.6) 0%,
                rgba(255, 198, 26, 0.15) 100%
            );
            border-radius: 0%;
        }
        ::v-deep .el-progress {
            height: 16px;
        }
        ::v-deep .el-progress-bar__outer {
            width: 400px;
            height: 16px !important;
            background-color: rgba(255, 255, 255, 0);
            // border: 1px solid #4C94FF;
            border-radius: 0%;
        }
        ::v-deep .el-progress__text {
            display: none;
        }
        .progress-height {
            height: 16px;
        }
    }
}
</style>
