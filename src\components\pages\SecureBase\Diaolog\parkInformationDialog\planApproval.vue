<template>
    <div class="bottom">
        <el-table class="tablebox" :data="tableData" style="width: 100%">
            <el-table-column
                prop="replyDate"
                label="批复时间"
                 width="100"
                show-overflow-tooltip
            />
            <el-table-column
                prop="replyName"
                label="批复名称"
                 width="700"
                show-overflow-tooltip
            />
            <el-table-column
                prop="examineDepart"
                label="审查部门"
                 width="140"
                show-overflow-tooltip
            />
            <el-table-column
                prop="replyNumber"
                label="批复文号"
                 width="160"
                show-overflow-tooltip
            />
            <el-table-column
                prop="replyArea"
                label="批复面积（平方米）"
                 width="150"
                show-overflow-tooltip
            />
            <el-table-column
                prop="approvalOpinion"
                label="审批意见"
                 width="300"
                show-overflow-tooltip
            />
            <el-table-column
                prop="planTextObj"
                label="规划文本"
                 width="220"
                show-overflow-tooltip
            >
            <template #default="scope">
                <span
                    v-if="scope.row.planTextObj && scope.row.planTextObj.fileName"
                    class="file-download-link"
                    @click="downloadFile(scope.row.planTextObj)"
                    :title="scope.row.planTextObj.fileName"
                >
                    {{ scope.row.planTextObj.fileName }}
                </span>
                <span v-else>-</span>
            </template>
            </el-table-column>
            <el-table-column
                prop="replyTextObj"
                label="批复文件"
                 width="220"
                show-overflow-tooltip
            >
            <template #default="scope">
                <span
                    v-if="scope.row.replyTextObj && scope.row.replyTextObj.fileName"
                    class="file-download-link"
                    @click="downloadFile(scope.row.replyTextObj)"
                    :title="scope.row.replyTextObj.fileName"
                >
                    {{ scope.row.replyTextObj.fileName }}
                </span>
                <span v-else>-</span>
            </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            background
            v-model:currentPage="pageNum"
            :page-size="pageSize"
            @current-change="handleCurrentChange"
            layout="->,total, prev, pager, next"
            :total="total"
        />
    </div>
</template>

<script setup>
import {
    plan_approval_page,
    dict_allList,
} from "@/assets/js/api/dialog/secureBase";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    getCurrentInstance,
} from "vue";
const { proxy } = getCurrentInstance();
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
//字典
const dictList = ref([]);

const getDict = () => {
    dict_allList({
        pid: 115,
    }).then((res) => {
        if (res.data && res.data.data) {
            dictList.value = res.data.data;
        }
    });
};
const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData();
};
const getData = () => {
    plan_approval_page({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    }).then((res) => {
        console.log(res);
        if (res.data && res.data.data) {
            // tableData.value=res.data.data.list;
            total.value = res.data.data.total;
            tableData.value = res.data.data.list.map((i) => {
                let replyTextObj = "";
                let planTextObj = "";
                if (i.replyText) {
                    try {
                        // 尝试第一次解析
                        let firstParse = JSON.parse(i.replyText);
                        // 如果第一次解析的结果是字符串，说明需要再次解析
                        if (typeof firstParse === 'string') {
                            replyTextObj = JSON.parse(firstParse);
                        } else {
                            replyTextObj = firstParse;
                        }
                    } catch (error) {
                        console.warn('解析replyText失败:', error, i.replyText);
                        replyTextObj = "";
                    }
                }
                if(i.planText){
                    try {
                        // 尝试第一次解析
                        let firstParse = JSON.parse(i.planText);
                        // 如果第一次解析的结果是字符串，说明需要再次解析
                        if (typeof firstParse === 'string') {
                            planTextObj = JSON.parse(firstParse);
                        } else {
                            planTextObj = firstParse;
                        }
                    } catch (error) {
                        console.warn('解析planText失败:', error, i.planText);
                        planTextObj = "";
                    }

                }
                return {
                    ...i,
                    replyTextObj: replyTextObj,
                    planTextObj: planTextObj,
                };
            });
        }
    });
};

const downloadFile = (fileObj) => {
    if (!fileObj || !fileObj.fileName) {
        console.warn('文件信息不完整');
        return;
    }
    
    let fileName = fileObj.fileName;
    let fileUrl;
    
    // 构建文件下载URL
    if (fileObj.viewPath || fileObj.filePath || fileObj.url) {
        let filePath = fileObj.viewPath || fileObj.filePath || fileObj.url;
        let font = window.location.protocol;
        let host = window.location.hostname;
        
        if (filePath.indexOf("city189.cn") != -1) {
            fileUrl = filePath;
        } else {
            // 根据项目配置构建文件URL
            fileUrl = font + "//" + host + ":10000/" + 
                     filePath.split("/").splice(3, filePath.split("/").length).join("/");
        }
    }
    
    // 创建下载链接并触发下载
    try {
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = fileUrl;
        link.setAttribute("download", fileName);
        link.setAttribute("target", "_blank");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('文件下载失败:', error);
        window.open(fileUrl, '_blank');
    }
};


onMounted(() => {
    getData();
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.bottom {
    width: 1120px;
    height: 504px;
    .tablebox {
        //表格四个边框的颜色
        // border: 1px solid #30abe8 !important;
        border: none !important;
        border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

        th.el-table__cell {
            border: none !important;
        }
    }

    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        // border-bottom: 1px solid #30abe8 !important;
        height: 40px;
        background-color: rgba(25, 159, 255, 0.1) !important;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        text-align: center;
        color: rgba(128, 234, 255, 1);
    }

    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: none !important;
        }
    }

    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
    }

    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    // 表头样式（去掉底部边框）
    :deep(.el-table__header) {
        .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
        }
    }

    // 表格内容样式（保留底部边框）
    :deep(.el-table__body) {
        .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(255, 255, 255, 1);
        }
    }

    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }

    .pagination {
        margin-top: 16px;
    }

    // 文件下载链接样式
    .file-download-link {
        color: #1A9FFF;
        cursor: pointer;
        text-decoration: underline;
        transition: color 0.3s ease;

        &:hover {
            color: #FFC61A;
            text-decoration: underline;
        }

        &:active {
            color: #0080CC;
        }
    }

    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .btn-next) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .btn-prev) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination__total) {
        background-color: transparent;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: right;
        vertical-align: middle;
        color: rgba(26, 159, 255, 1);
    }
}
</style>
