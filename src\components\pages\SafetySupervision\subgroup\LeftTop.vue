<template>
    <Card title="重大危险源">
        <template v-slot>
            <div class="major-hazard">
                <div class="title" @click="clickTodayAlarm" style="cursor: pointer">
                    今日告警：<span class="red">{{ alarmToday }}</span
                    >处/{{ alarmAll }}处
                </div>
                <div class="wrapper-one">
                    <div class="inner-one" @click="major('一级')" style="cursor: pointer">
                        <div class="num">{{ num.one }}</div>
                        <div class="text">一级重大危险源</div>
                    </div>
                    <div class="inner-two" @click="major('二级')" style="cursor: pointer">
                        <div class="num">{{ num.two }}</div>
                        <div class="text">二级重大危险源</div>
                    </div>
                </div>
                <div class="wrapper-two">
                    <div class="inner-three" @click="major('三级')" style="cursor: pointer">
                        <div class="num">{{ num.three }}</div>
                        <div class="text">三级重大危险源</div>
                    </div>
                    <div class="inner-four" @click="major('四级')" style="cursor: pointer">
                        <div class="num">{{ num.four }}</div>
                        <div class="text">四级重大危险源</div>
                    </div>
                </div>
            </div>
        </template>
        </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import { major_danger_source } from "../../../../assets/js/api/safetySupervision";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
const emit = defineEmits(["major"]);
const num = reactive({
    one: 0,
    two: 0,
    three: 0,
    four: 0,
});
const alarmToday = ref(0);
const alarmAll = ref(0);
onMounted(() => {
    major_danger_source({ "": "" }).then((res) => {
        console.log(res.data.data);
        if (res.data.success && res.data.data && res.data.data.length) {
            res.data.data.forEach((item) => {
                if (item.source_level === "一级") {
                    num.one = item.count;
                }
                if (item.source_level === "二级") {
                    num.two = item.count;
                }
                if (item.source_level === "三级") {
                    num.three = item.count;
                }
                if (item.source_level === "四级") {
                    num.four = item.count;
                }
            });
        }
    });
    audioOption();
});
onBeforeUnmount(() => {
    socket && socket.close();
    socket = null;
});
let socket = null;
const audioOption = () => {
    let hostname = window.location.hostname;
    console.log(hostname);

    if (window.location.protocol === "http:") {
        const wsuri = `ws://${hostname}:32362/safety-v2/ws/alarmCount`;

        socket = new WebSocket(wsuri);
    } else if (window.location.protocol === "https:") {
        socket = new WebSocket("wss://city189.cn:2960/safety-v2/ws/alarmCount");
    }
    //    socket = new WebSocket("wss://city189.cn:2960/safety-v2/ws/alarmCount");

    socket.addEventListener("message", function (event) {
        console.log(event);
        console.log(event.data);
        console.log();
        let type = JSON.parse(event.data);
        alarmToday.value = type.today;
        alarmAll.value = type.total;
    });
};
const major = (val) => {
    console.log(val);
    emit("major",val);
}

</script>

<style lang="less" scoped>
.major-hazard {
    width: 416px;
    height: 224px;
    padding: 16px;
    // background-color: antiquewhite;
    .title {
        width: 400px;
        height: 24px;
        // margin-top: 8px;
        text-align: center;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 133.333% */
        .red {
            color: red;
            margin: 0 6 px;
        }
    }
    .wrapper-one {
        width: 416px;
        height: 100px;
        display: flex;
        // gap: 16px;
        justify-content: space-between;
    }
    .wrapper-two {
        width: 416px;
        height: 100px;
        display: flex;
        // gap: 16px;
        justify-content: space-between;
    }
    .inner-one {
        width: 192px;
        height: 100px;
        background: url("../../../../assets/images/card/hazard-one.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
    }
    .inner-two {
        width: 192px;
        height: 100px;
        background: url("../../../../assets/images/card/hazard-two.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
    }
    .inner-three {
        width: 192px;
        height: 100px;
        background: url("../../../../assets/images/card/hazard-three.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
    }
    .inner-four {
        width: 192px;
        height: 100px;
        background: url("../../../../assets/images/card/hazard-four.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
    }
    .num {
        color: #fff;
        text-align: left;
        font-family: Digital Numbers;
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 133.333% */
        margin-top: 33px;
        margin-left: 84px;
    }
    .text {
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        margin-left: 84px;
    }
}
</style>
