<template>
    <div>
        <div class="park-archives-navigation" v-if="showFlag != 1">
            <div
                :class="active === 1 ? 'active-btn' : 'btn'"
                @click="setPage(1)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/infrastructure.svg"
                />
                <div class="text">基础设施</div>
            </div>
            <div
                :class="active === 2 ? 'active-btn' : 'btn'"
                @click="setPage(2)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/surroundingEnvironment.svg"
                />
                <div class="text">周边环境</div>
            </div>
        </div>
        <!-- <div :class='`enterprise${size}`'> -->
        <div class="enterprise1" id="enterprise" v-if="enterpriseDialog">
            <div class="header">
                <div class="title">企业详情</div>
                <div class="close" @click="closeDialog('enterprise')"></div>
            </div>
            <div class="container">
                <div class="top">
                    <div>{{ enterpriseInfoCountData.enterprise_name }}</div>
                    <div>
                        <span class="title">法定代表人：</span
                        >{{ enterpriseInfoCountData.legal_person_name }}
                    </div>
                    <div>
                        <span class="title">联系方式：</span
                        >{{ enterpriseInfoCountData.contact_way }}
                    </div>
                </div>
                <div class="liner"></div>
                <div class="center">
                    <div class="one">
                        <div class="details">
                            <div class="icon1"></div>
                            <div class="value">
                                <div>
                                    <span class="num">{{
                                        enterpriseInfoCountData.mds_count
                                    }}</span
                                    >个
                                </div>
                                <div>重大危险源</div>
                            </div>
                        </div>
                        <div class="details">
                            <div class="icon2"></div>
                            <div class="value">
                                <div>
                                    <span class="num">{{
                                        enterpriseInfoCountData.dcp_count
                                    }}</span
                                    >个
                                </div>
                                <div>重点工艺</div>
                            </div>
                        </div>
                        <div class="details">
                            <div class="icon3"></div>
                            <div class="value">
                                <div>
                                    <span class="num">{{
                                        enterpriseInfoCountData.dc_count
                                    }}</span
                                    >个
                                </div>
                                <div>重点监管化学品</div>
                            </div>
                        </div>
                    </div>
                    <div class="two">
                        <div class="details">
                            <div class="icon1"></div>
                            <div class="value">
                                <div>
                                    <span class="num">{{
                                        enterpriseInfoCountData.rr_count
                                    }}</span
                                    >个
                                </div>
                                <div>风险点</div>
                            </div>
                        </div>
                        <div class="details">
                            <div class="icon2"></div>
                            <div class="value">
                                <div>
                                    <span class="num">{{
                                        enterpriseInfoCountData.dr_count
                                    }}</span
                                    >个
                                </div>
                                <div>当月隐患</div>
                            </div>
                        </div>
                        <div class="details">
                            <div class="icon3"></div>
                            <div class="value">
                                <div>
                                    <span class="num">{{
                                        enterpriseInfoCountData.sws_count
                                    }}</span
                                    >个
                                </div>
                                <div>当月特殊作业</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="liner"></div>
                <div class="bottom">
                    <div
                        :class="
                            typeActive === 1 ? 'type-active-btn' : 'type-btn'
                        "
                        @click="selectType(1)"
                    >
                        基础档案
                    </div>
                    <div
                        :class="
                            typeActive === 2 ? 'type-active-btn' : 'type-btn'
                        "
                        @click="selectType(2)"
                    >
                        两重点一重大
                    </div>
                    <div
                        :class="
                            typeActive === 3 ? 'type-active-btn' : 'type-btn'
                        "
                        @click="selectType(3)"
                    >
                        安全类监管档案
                    </div>
                    <div
                        :class="
                            typeActive === 4 ? 'type-active-btn' : 'type-btn'
                        "
                        @click="selectType(4)"
                    >
                        应急类监管档案
                    </div>
                </div>
                <div class="change">
                    <component
                        :pickId="pickId"
                        :is="changeType"
                        :key="Math.random() * 1000"
                        @sizeChange="sizeChange"
                    ></component>
                </div>
            </div>
        </div>
        <infrastructure-dialog
            :pickId="infrastructureId"
            v-if="infrastructureDialogShow"
            @closeDialog="closeDialog"
        ></infrastructure-dialog>
        <surrounding-environment-dialog
            :pickId="surroundingId"
            v-if="surroundingDialogShow"
            @closeDialog="closeDialog"
        ></surrounding-environment-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    nextTick,
} from "vue";
import * as Cesium from "cesium";
import BasicArchives from "./subgroup/BasicArchives.vue";
import TwoKeynote from "./subgroup/TwoKeynote.vue";
import SecurityArchives from "./subgroup/SecurityArchives.vue";
import EmergencyArchives from "./subgroup/EmergencyArchives.vue";
import infrastructureDialog from "./subgroup/infrastructureDialog.vue";
import SurroundingEnvironmentDialog from "./subgroup/SurroundingEnvironmentDialog.vue";
import {
    enterprise_industry,
    enterprise_list,
    enterprise_info_count,
    enterprise_info,
    enterprise_staff_files,
    construct_meanwhile,
    product_certificate,
    safety_evaluation_management,
    emergency_management,
    emergency_plan,
    emergency_rehearsal,
    nj_equipments,
    sensitive_target_list,
    sensitive_target_info,
} from "../../../assets/js/api/parkArchives";
const showFlag = ref(1);
const infrastructureDialogShow = ref(false);
const infrastructureId = ref(null);
const surroundingDialogShow = ref(false);
const surroundingId = ref(null);
let changeType = ref(BasicArchives);
let obj = reactive({
    1: BasicArchives, // 综合监管
    2: TwoKeynote, //两重点一重大
    3: SecurityArchives, //安全类监管档案
    4: EmergencyArchives, //应急类监管档案
});
let size = ref(1);
const enterpriseDialog = ref(false);
const closeDialog = (x) => {
    if (x == "enterprise") {
        enterpriseDialog.value = false;
    } else if (x == "infrastructure") {
        infrastructureDialogShow.value = false;
    } else if (x == "surrounding") {
        surroundingDialogShow.value = false;
    }
};
const sizeChange = (x) => {
    console.log(x);
    size.value = x;
    if (x == 2) {
        document.getElementById("enterprise").style.height = "728px";
    } else if (x == 1) {
        document.getElementById("enterprise").style.height = "728px";
    } else if (x == 3) {
        document.getElementById("enterprise").style.height = "780px";
    } else if (x == 4) {
        document.getElementById("enterprise").style.height = "816px";
    } else if (x == 5) {
        document.getElementById("enterprise").style.height = "584px";
    } else if (x == 6) {
        document.getElementById("enterprise").style.height = "944px";
    }
    // changeType.value = null
    // // selectType(typeActive.value)
    // changeType.value = obj[typeActive.value]
    // console.log(changeType.value);
};
const active = ref(0);
var zIndex = 30;
const setPage = (type) => {
    if (type === active.value) {
        active.value = 0;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        console.log(window.viewer.entities);
        initBoundary();
    } else {
        active.value = type;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        initBoundary();
        if (type == 1) {
            positionData.value = [];
            nj_equipments({ enterprise_name: "" }).then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    console.log(res.data.data);
                    // positionData.value = res.data.data
                    res.data.data.forEach((item) => {
                        if (item.longitude != null && item.latitude != null) {
                            positionData.value.push({
                                name: item.id,
                                id: item.id,
                                longitude: Number(item.longitude),
                                latitude: Number(item.latitude),
                            });
                        }
                    });
                    if (window.toggle == 2) {
                        putIcons(positionData.value, infrastructureImg);
                        console.log(window.viewer.entities);
                        // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                        window.viewer.screenSpaceEventHandler.setInputAction(
                            function (e) {
                                var pick = viewer.scene.pick(e.position);
                                if (pick && pick.id) {
                                    console.log(pick.id.id);

                                    infrastructureId.value = pick.id.id;
                                    infrastructureDialogShow.value = true;
                                    window.viewer.camera.setView({
                                        destination:
                                            Cesium.Cartesian3.fromDegrees(
                                                pick.id._longitude,
                                                pick.id._latitude,
                                                800,
                                            ),
                                    });
                                }
                            },
                            Cesium.ScreenSpaceEventType.LEFT_CLICK,
                        );
                    } else if (window.toggle == 3) {
                        console.log(window.map1, positionData.value);
                        positionData.value.forEach((item) => {
                            let marker = new AMap.Marker({
                                // content: '<div class="two-icon-infrastructure"></div>',
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: "/static/poi/infrastructure-new.svg", //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                window.map1.setZoomAndCenter(
                                    22,
                                    e.target._opts.position,
                                );

                                infrastructureId.value = item.name;
                                infrastructureDialogShow.value = true;

                                console.log(infrastructureId.value);
                            });
                            marker.setMap(window.map1);
                        });
                        window.map1.setFitView();
                    }
                }
            });
        } else if (type == 2) {
            positionData.value = [];
            areaData.value = [];
            areaDataThree.value = [];
            sensitive_target_list({ "": "" }).then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    // positionData.value = res.data.data
                    res.data.data.forEach((item) => {
                        if (item.is_point == 1) {
                            if (
                                item.longitude != null &&
                                item.latitude != null
                            ) {
                                positionData.value.push({
                                    name: item.id,
                                    id: item.id,
                                    longitude: Number(item.longitude),
                                    latitude: Number(item.latitude),
                                });
                            }
                        } else {
                            if (item.points != null) {
                                let threeMiddle = [];
                                eval(item.points).forEach((innerItem) => {
                                    innerItem.forEach((inner, index) => {
                                        if (index == 0) {
                                            threeMiddle.push(
                                                Number(inner) - 0.0062,
                                            );
                                        } else {
                                            threeMiddle.push(
                                                Number(inner) - 0.00085,
                                            );
                                        }
                                    });
                                    threeMiddle.push(30);
                                });
                                let obj = {};
                                Reflect.set(obj, "id", item.id);
                                Reflect.set(obj, "path", threeMiddle);
                                areaDataThree.value.push(obj);
                                areaData.value.push({
                                    name: item.id,
                                    id: item.id,
                                    path: item.points,
                                });
                            }
                        }
                    });
                    //   console.log(positionData.value);
                    //   console.log(areaData.value);
                    //   console.log(areaDataThree.value);

                    if (window.toggle == 2) {
                        putIcons(positionData.value, surroundingImg);
                        console.log(window.viewer.entities);
                        // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                        window.viewer.screenSpaceEventHandler.setInputAction(
                            function (e) {
                                var pick = viewer.scene.pick(e.position);
                                if (pick && pick.id) {
                                    console.log(pick.id.id);

                                    surroundingId.value = pick.id.id;
                                    surroundingDialogShow.value = true;
                                    window.viewer.camera.setView({
                                        destination:
                                            Cesium.Cartesian3.fromDegrees(
                                                pick.id._longitude,
                                                pick.id._latitude,
                                                800,
                                            ),
                                    });
                                }
                            },
                            Cesium.ScreenSpaceEventType.LEFT_CLICK,
                        );
                        areaDataThree.value.forEach((item) => {
                            const entity = window.viewer.entities.add({
                                id: item.id,
                                name: item.id,
                                longitude: Number(item.path[0]),
                                latitude: Number(item.path[1]),
                                show: true,
                                polygon: {
                                    hierarchy:
                                        Cesium.Cartesian3.fromDegreesArrayHeights(
                                            item.path,
                                        ), //参数为四个角点坐标
                                    zIndex: zIndex,
                                    height: zIndex, //多层次
                                    outline: true,
                                    outlineColor:
                                        Cesium.Color.fromCssColorString(
                                            "#47EBEB",
                                        ),
                                    outlineWidth: 15.0,
                                    material: Cesium.Color.fromCssColorString(
                                        "rgba(71, 235, 235, 0.30)",
                                    ),
                                },
                            });
                            viewer.zoomTo(entity);
                        });
                    } else if (window.toggle == 3) {
                        console.log(window.map1, positionData.value);
                        positionData.value.forEach((item) => {
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: "/static/poi/surrounding-new.svg", //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                window.map1.setZoomAndCenter(
                                    22,
                                    e.target._opts.position,
                                );

                                surroundingId.value = item.name;
                                surroundingDialogShow.value = true;

                                console.log(surroundingId.value);
                            });
                            marker.setMap(window.map1);
                        });
                        areaData.value.forEach((item) => {
                            console.log(item.path);
                            let polygon = new AMap.Polygon({
                                path: eval(item.path),
                                strokeColor: "#FF33FF",
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: "#1791fc",
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                console.log(e);
                                surroundingId.value = item.name;
                                surroundingDialogShow.value = true;
                                window.map1.setZoomAndCenter(
                                    18,
                                    e.target._opts.path[0],
                                );
                            });
                            window.map1.add([polygon]);
                        });
                        window.map1.setFitView();
                    }
                }
            });
        }

        //cesium操作测试
        // if (type === 1) {
        //   const entity = window.viewer.entities.add({
        //     id: "line",
        //     name: "线",
        //     show: true,
        //     polyline: {
        //       positions: Cesium.Cartesian3.fromDegreesArrayHeights([
        //         115.545, 38.89, 30, 115.545, 38.8908, 30, 115.5447, 38.89, 30,
        //         115.545, 38.89, 30,
        //       ]),
        //       width: 3,
        //       material: Cesium.Color.BLUE.withAlpha(0.5),
        //     },
        //   });
        //   viewer.zoomTo(entity);
        // } else {
        //   // 创建矩形
        //   const entity = window.viewer.entities.add({
        //     id: "polygon",
        //     name: "面",
        //     show: true,
        //     polygon: {
        //       hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights([
        //         115.545, 38.89, 30, 115.545, 38.8908, 30, 115.5447, 38.89, 30,
        //         115.545, 38.89, 30,
        //       ]), //参数为四个角点坐标
        //       material: Cesium.Color.BLUE.withAlpha(0.5),
        //     },
        //   });
        //   viewer.zoomTo(entity);
        // }
    }
};
const adjustField = () => {
    const arrs = []; //经纬度数组
    const lonArr = []; //经纬度数组
    const latArr = []; //经纬度数组

    positionData.value.forEach((item) => {
        //循环数据 push经纬度
        arrs.push(item.longitude);
        lonArr.push(item.longitude);
        arrs.push(item.latitude);
        latArr.push(item.latitude);
    });

    let adr = Cesium.Cartesian3.fromDegreesArray(arrs);
    let polys = Cesium.BoundingSphere.fromPoints(adr).center;
    polys = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polys);
    let ellipso = window.viewer.scene.globe.ellipsoid;
    let cartesian = new Cesium.Cartesian3(polys.x, polys.y, polys.z);
    let cartographic2 = ellipso.cartesianToCartographic(cartesian);

    let obj2 = {};
    obj2.lat = Cesium.Math.toDegrees(cartographic2.latitude);
    obj2.lng = Cesium.Math.toDegrees(cartographic2.longitude);
    obj2.alt = cartographic2.height;
    const maxLon = Math.max.apply(Math, lonArr);
    const minLon = Math.min.apply(Math, lonArr);
    const maxLat = Math.max.apply(Math, latArr);
    const minLat = Math.min.apply(Math, latArr);
    const lat = maxLat - minLat;
    const lon = maxLon - minLon;
    const radius = lat > lon ? lat : lon;
    let h = radius * 9550000;
    if (radius > 1) {
        console.log(11111111);
        h = radius * 250000; //高度 自行调整  250000
    }

    nextTick(() => {
        window.viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(obj2.lng, obj2.lat, h),
        });
    });
};

import workshopImg from "../../../assets/images/poi/workshop.svg";

const typeActive = ref(1);
const selectType = (type) => {
    typeActive.value = type;
    changeType.value = obj[type];
    document.getElementById("enterprise").style.height = "728px";
    //   if (type === 1 || type === 2 || type === 3 || type === 4) {
    //     // size.value = 1
    //     document.getElementById("enterprise").style.height = "728px";
    //   }
    //   else if (type === 2) {
    //     // size.value = 2
    //     document.getElementById("enterprise").style.height = "968px";
    //   }
    //   else if (type === 3) {
    //     document.getElementById("enterprise").style.height = "816px";
    //   }
    //   else if (type === 4) {
    //     document.getElementById("enterprise").style.height = "944px";
    //   }
};
const innerActive = ref(1);
const selectInnerType = (type) => {
    innerActive.value = type;
};
const pickId = ref();
import infrastructureImg from "/static/poi/infrastructure-new.svg";
import surroundingImg from "/static/poi/surrounding-new.svg";
import ParkArchivesImg from "/static/poi/ParkArchives-poi-new.svg";

// const aliveImg = ref('../../../assets/images/card/interval.svg')
const positionData = ref([]);
const areaData = ref([]);
const areaDataThree = ref([]);

const putIcons = (_datas, img, _parent) => {
    console.log("添加视频icon");
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        console.log(data);
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        const entity = window.viewer.entities.add({
            name: "cameraPoint",
            // 参数顺序：经度、纬度
            id: data.name,
            name: data.id,
            longitude: Number(data.longitude) - 0.0062,
            latitude: Number(data.latitude) - 0.00085,
            position: Cesium.Cartesian3.fromDegrees(
                Number(data.longitude) - 0.0062,
                Number(data.latitude) - 0.00085,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 72,
                height: 92,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                pixelOffset: new Cesium.Cartesian2(0, -46),
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            type: "text", // 自定义属性
        });
        // window.viewer.zoomTo(entity);
        adjustField();
    }
};
const enterpriseInfoCountData = ref({
    enterprise_name: "",
    legal_person_name: "",
    mds_count: "",
    dcp_count: "",
    dc_count: "",
    rr_count: "",
    dr_count: "",
    sws_count: "",
    contact_way: "",
});
const enterpriseInfoCount = (name) => {
    enterprise_info_count({ enterprise_name: name }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res);
            enterpriseInfoCountData.value = res.data.data[0];
            console.log(enterpriseInfoCountData.value);
        }
    });
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
onMounted(() => {
    showFlag.value = window.toggle;
    positionData.value = [];
    areaData.value = [];
    initBoundary();
    enterprise_list({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            // positionData.value = res.data.data
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.enterprise_name,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            console.log(positionData.value);
            console.log(areaData.value);
            if (window.toggle == 2) {
                console.log(positionData.value);
                putIcons(positionData.value, ParkArchivesImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    console.log(e.position);
                    if (pick && pick.id) {
                        console.log(pick);
                        console.log(pick.id.id);
                        console.log(pick.id.name);
                        enterpriseInfoCount(pick.id.name);
                        pickId.value = pick.id.name;
                        // surroundingId.value = pick.id.id;
                        enterpriseDialog.value = true;
                        console.log(pick.id._longitude);

                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                console.log(window.map1, positionData.value);
                positionData.value.forEach((item) => {
                    let marker = new AMap.Marker({
                        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                        icon: new AMap.Icon({
                            size: new AMap.Size(72, 92), // 图标尺寸
                            image: "/static/poi/ParkArchives-poi-new.svg", //绝对路径
                            imageSize: new AMap.Size(72, 92),
                        }),
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-36, -92),
                    });
                    marker.on("click", (e) => {
                        // surroundingId.value = item.name;
                        console.log(e.target._opts.position);
                        console.log(item);
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                        enterpriseInfoCount(item.id);
                        pickId.value = item.id;
                        enterpriseDialog.value = true;
                    });
                    const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                    const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 16px;
`;

                    // const box = `<div style="${boxStyle}"></div>`;
                    let infoWindow = new AMap.InfoWindow({
                        isCustom: true,
                        // 设置信息窗口的内容
                        content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.id}</div>
                       </div>`,
                        // 设置信息窗口的偏移量
                        offset: new AMap.Pixel(0, -85),
                    });
                    // 添加鼠标移入事件监听器
                    marker.on("mouseover", (e) => {
                        console.log(item, "eeeee");

                        infoWindow.open(window.map1, e.target.getPosition());
                    });

                    // 添加鼠标移出事件监听器
                    marker.on("mouseout", (e) => {
                        infoWindow.close();
                    });
                    marker.setMap(window.map1);
                    marker.setMap(window.map1);
                });
                window.map1.setFitView();
            }
        }
    });
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
});
</script>

<style lang="less" scoped>
.park-archives-navigation {
    width: 130px;
    height: 92px;
    z-index: 1000;
    position: absolute;
    top: 864px;
    left: 496px;

    > div:not(:first-child) {
        margin-top: 24px;
    }
    .btn {
        width: 130px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }
    .active-btn {
        width: 130px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/active-btn.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        // gap: 24px;
    }
    .icon {
        width: 24px;
        height: 24px;
        // margin: auto 0;
        margin-top: 4px;
        margin-left: 12px;
    }
    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        margin: auto 0;
        margin-left: 24px;
    }
}
.enterprise1 {
    width: 840px;
    height: 728px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}
.enterprise2 {
    width: 840px;
    height: 968px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}
.enterprise3 {
    width: 840px;
    height: 780px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}
.header {
    width: 840px;
    height: 52px;
    flex-shrink: 0;
    background: url("../../../assets/images/dialog/header1.svg") no-repeat
        center center;
    background-size: cover;
    background-position: center;
    display: flex;
    .title {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 52px; /* 150% */
        margin-left: 32px;
    }
    .close {
        width: 24px;
        height: 24px;
        background: url("../../../assets/images/dialog/close.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: auto 0;
        margin-left: 696px;
    }
}
.container {
    width: 792px;
    height: 644px;
    padding: 16px 24px;
    .top {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        text-align: left;
        .title {
            color: #47ebeb;
        }
    }
    .liner {
        width: 792px;
        height: 2px;
        background: url("../../../assets/images/dialog/liner.svg") no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
    .center {
        // height: 48px;
        .one {
            width: 792px;
            height: 48px;
            display: flex;
            justify-content: space-between;
        }
        .two {
            width: 792px;
            height: 48px;
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
        }
        .details {
            width: 150px;
            height: 48px;
            display: flex;
            gap: 4px;
            .icon1 {
                width: 56px;
                height: 48px;
                background: url("../../../assets/images/dialog/icon1.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .icon2 {
                width: 56px;
                height: 48px;
                background: url("../../../assets/images/dialog/icon2.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .icon3 {
                width: 56px;
                height: 48px;
                background: url("../../../assets/images/dialog/icon3.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .value {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 10px;
                font-style: normal;
                font-weight: 400;
                line-height: 14px; /* 140% */
                .num {
                    color: #fff;
                    text-align: center;
                    font-family: Digital Numbers;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 28px; /* 140% */
                }
            }
        }
    }
    .bottom {
        width: 792px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        color: #fff;
        text-align: center;
        text-shadow: 0px 0px 8px rgba(77, 57, 0, 0.6);
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px; /* 150% */
        .type-active-btn {
            width: 144px;
            height: 40px;
            background: url("../../../assets/images/dialog/type-active-btn.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .type-btn {
            width: 144px;
            height: 40px;
            background: url("../../../assets/images/dialog/type-btn.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
    }
}
.two-icon-infrastructure {
    width: 48px;
    height: 48px;
    background: url("../../../assets/images/dialog/type-active-btn.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    z-index: 99999;
}
</style>
