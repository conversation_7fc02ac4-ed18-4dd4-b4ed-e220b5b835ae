<template>
    <div class="recordDialog">
        <div class="command_title">
            <div>
                <img
                    class="title_image"
                    src="../../../../assets/images/card/title_left.svg"
                />
            </div>
            执行记录
            <img
                src="../../../../assets/images/card/index2_7.png"
                alt=""
                srcset=""
                class="closeImg"
                @click.stop="handleClose"
            />
        </div>
        <div class="record_content">
            <div
                class="recordList"
                v-if="
                    recordList.eventReport &&
                    Object.keys(recordList.eventReport).length != 0
                "
            >
                <div style="display: flex; position: relative">
                    <div class="round"></div>
                    <div class="recordTitle">事件接报</div>
                </div>
                <div class="subTitle">
                    <div>
                        上报时间：{{
                            recordList.eventReport.reportTime
                                ? recordList.eventReport.reportTime
                                : ""
                        }}
                    </div>
                    <div>
                        上报人：{{ recordList.eventReport.submitPerson }}
                        {{ recordList.eventReport.contactNumber }}
                    </div>
                    <div>
                        审核时间：{{ recordList.eventReport.auditTime }}
                    </div>
                    <div>
                        审核人：{{ recordList.eventReport.auditPerson }}
                    </div>
                </div>
            </div>
            <div
                class="recordList"
                v-if="
                    recordList.planStarts &&
                    recordList.planStarts.length != 0
                "
            >
                <div style="display: flex; position: relative">
                    <div class="round"></div>
                    <div class="recordTitle">预案启动</div>
                </div>
                <div class="subTitle">
                    <div
                        v-for="(item, index) in recordList.planStarts"
                        :key="index"
                        style="margin: 10px 0"
                    >
                        <div>预案名称：{{ item.planName }}</div>
                        <div>开始时间：{{ item.startTime }}</div>
                        <div>处理人员：{{ item.handler }}</div>
                    </div>
                </div>
            </div>
            <div
                class="recordList"
                v-if="
                    recordList.continuousDispatch &&
                    recordList.continuousDispatch.length != 0
                "
            >
                <div style="display: flex; position: relative">
                    <div class="round"></div>
                    <div class="recordTitle">持续调度</div>
                </div>
                <div class="subTitle">
                    <div
                        v-for="(
                            item, index
                        ) in recordList.continuousDispatch"
                        :key="index"
                        style="margin: 10px 0"
                    >
                        <div>
                            任务开始时间：{{
                                item.startTime ? item.startTime : ""
                            }}
                        </div>
                        <div
                            :style="
                                item.assignTaskType == 5012901
                                    ? 'color:red'
                                    : 'color:green'
                            "
                        >
                            {{
                                dicts.assign_task_type.find(
                                    (ele) =>
                                        ele.code == item.assignTaskType,
                                )
                                    ? dicts.assign_task_type.find(
                                          (ele) =>
                                              ele.code ==
                                              item.assignTaskType,
                                      ).name
                                    : ""
                            }}
                        </div>
                        <div>指派队伍：{{ item.contingentName }}</div>
                        <div>任务结束时间：{{ item.endTime }}</div>
                    </div>
                </div>
            </div>
            <div
                class="recordList"
                v-if="
                    recordList.endRescue &&
                    Object.keys(recordList.endRescue).length != 0
                "
            >
                <div style="display: flex; position: relative">
                    <div class="round"></div>
                    <div class="recordTitle">结束救援</div>
                </div>
                <div class="subTitle">
                    <div>
                        结束时间：{{
                            recordList.endRescue
                                ? recordList.endRescue.endTime
                                : ""
                        }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted, getCurrentInstance } from 'vue'
import { recordDetile } from '../../../../assets/js/api/fire.js'

const { proxy } = getCurrentInstance()

// 定义props
const props = defineProps({
    fireEvent: {
        type: Object,
        required: true
    },
    dicts: {
        type: Object,
        default: () => ({})
    }
})

// 定义emits
const emit = defineEmits(['close'])

// 响应式数据
const recordList = ref({})

// 获取执行记录数据
const fetchRecordData = () => {
    recordDetile({
        businessTypeId: props.fireEvent.businessTypeId,
    }).then((res) => {
        recordList.value = res.data.data
        proxy.$loading.show()
    })
}

// 关闭弹窗
const handleClose = () => {
    proxy.$loading.hide()
    emit('close')
}

// 组件挂载时获取数据
onMounted(() => {
    fetchRecordData()
})
</script>

<style scoped>
.title_image {
    width: 12px;
    height: 12px;
    margin: auto 4px auto 16px;
}

.recordDialog {
    background: url("../../../../assets/images/card/fire_2_2.png");
    background-size: cover;
    width: 339px;
    height: 795px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    z-index: 1005;
}

.command_title {
    margin-left: 17px;
    margin-top: 12px;
    font-style: normal;
    width: 100%;
    display: flex;
    margin-bottom: 30px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
}

.closeImg {
    width: 14px;
    height: 14px;
    margin-right: 40px;
    margin-top: 10px;
    margin-left: auto;
}

.record_content {
    position: relative;
    margin-top: 24px;
    margin-left: 23px;
    height: 690px;
    overflow-y: auto;
    overflow-x: hidden;
}

.recordList {
    margin-bottom: 24px;
}

.round {
    width: 16px;
    height: 16px;
    border-radius: 999px;
    border: 1px solid #47ebeb;
    background: rgba(71, 235, 235, 0.5);
    position: absolute;
    top: 0;
    text-align: center;
    line-height: 16px;
}

.recordTitle {
    color: rgba(255, 255, 255, 0.9);
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    padding-left: 20px;
    margin-bottom: 6px;
    margin-left: 3px;
}

.subTitle {
    padding-left: 16px;
    padding-bottom: 16px;
    border-left: 1px solid #47ebeb;
    margin-left: 9px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

::-webkit-scrollbar {
    width: 5px;
    float: right;
}

::-webkit-scrollbar-track {
    border-radius: 4px;
    background: #004f4f;
    width: 5px;
}

::-webkit-scrollbar-thumb {
    background: #fff;
    border-radius: 8px;
    width: 5px;
}
</style>
