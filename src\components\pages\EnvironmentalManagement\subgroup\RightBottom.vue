<template>
    <Card title="危险废物分析">
        <template v-slot>
            <div class="hazardous-waste">
                <div class="select-btns">
                    <div
                        :class="leftActive === 1 ? 'active-left-btn' : 'left-btn'"
                        @click="changeShow(1)"
                    >
                        数据分析
                    </div>
                    <div
                        :class="leftActive === 2 ? 'active-left-btn' : 'left-btn'"
                        @click="changeShow(2)"
                    >
                        环比分析
                    </div>
                </div>
                <div class="bottom">
                    <component :is="pageType"></component>
                </div>
            </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import DataAnalysis from "./DataAnalysis.vue";
import DataAnalysisNew from "./DataAnalysisNew.vue";
import MonthAnalysis from "./MonthAnalysis.vue";
const leftActive = ref(1);
const changeShow = (type) => {
    leftActive.value = type;
    if (type === 1) {
        pageType.value = DataAnalysisNew;
    } else {
        pageType.value = MonthAnalysis;
    }
};
const pageType = ref(DataAnalysisNew);
</script>

<style lang="less" scoped>
.hazardous-waste {
    // width: 400px;
    // height: 256px;
    width: 416px;
    height: 224px;
    padding: 16px;
    .select-btns {
        width: 152px;
        height: 24px;
        display: flex;
        gap: 8px;
        .active-left-btn {
            width: 72px;
            height: 24px;
            justify-content: center;
            text-align: center;
            align-items: center;
            border-radius: 4px;
            border: 0.5px solid #ffc61a;
            background: rgba(255, 198, 26, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
        .left-btn {
            width: 72px;
            height: 24px;
            justify-content: center;
            text-align: center;
            align-items: center;
            border-radius: 4px;
            border: 0.622px solid #1ab2ff;
            background: rgba(26, 178, 255, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
    }
    .bottom {
        width: 416px;
        height: 192px;
        margin-top: 8px;
        // background-color: antiquewhite;
    }
}
</style>
