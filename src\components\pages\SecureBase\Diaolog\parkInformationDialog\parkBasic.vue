<template>
    <div class="table">
                <table>
                    <tr>
                        <td class="header-cell">
                            园区名称
                        </td>
                        <td class="header-cell">
                            园区等级
                        </td>
                        <td class="header-cell">
                            成立日期
                        </td>
                        <td class="header-cell">
                            管理部门
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div
                                class="cell-inner"
                                :title="dialogData.parkName"
                            >
                                {{ dialogData.parkName }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div
                                class="cell-inner"
                                :title="dialogData.rank"
                            >
                                {{ dialogData.rank }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.establishDate">
                                {{ dialogData.establishDate }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.manageDepart">
                                {{ dictList&&dictList.find(i=>i.dictEncoding==dialogData.manageDepart)!=undefined?dictList.find(i=>i.dictEncoding==dialogData.manageDepart).dictName:'-' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell">
                            实际面积（公顷）
                        </td>
                        <td class="header-cell">
                            注册资金（万元）
                        </td>
                        <td class="header-cell">
                            产业定位
                        </td>
                        <td class="header-cell">
                            园区内注册企业（家）
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.planArea">
                                {{ dialogData.planArea }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.capital">
                                {{ dialogData.capital }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.industryPosition">
                                {{ dialogData.industryPosition }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.registerNum">
                                {{ dialogData.registerNum }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell">
                            实际生产企业（家）
                        </td>
                        <td class="header-cell">
                            从业人员（个）
                        </td>
                        <td class="header-cell">
                            重大危险源（个）
                        </td>
                        <td class="header-cell">
                            高危工艺种类（个）
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.productionNum">
                                {{ dialogData.productionNum }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.staff">
                                {{ dialogData.staff }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.highRiskTypes">
                                {{ dialogData.highRiskTypes }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.highRiskTypes">
                                {{ dialogData.highRiskTypes }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell">
                            危险化学品类（个）
                        </td>
                        <td class="header-cell">
                            水域监测站数量（个）
                        </td>
                        <td class="header-cell">
                            
                        </td>
                        <td class="header-cell">
                            
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.chemistryTypes">
                                {{ dialogData.chemistryTypes }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.waterMonitorNum">
                                {{ dialogData.waterMonitorNum }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner">
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" >
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
</template>

<script setup>
import { park_basic_page,dict_allList } from "@/assets/js/api/dialog/secureBase";
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance  } from "vue";
const { proxy } = getCurrentInstance();
let dialogData = ref({
    parkName: "",
    rank: "",
    establishDate: "",
    manageDepart:"",
    planArea: "",
    capital: "",
    industryPosition: "",
    registerNum:"",
    productionNum: "",
    staff: "",
    highRiskTypes: "",
    chemistryTypes:"",
    waterMonitorNum:""
});
let dictList = ref([])
//获取字典
const getDict = () =>{
dict_allList({
    pid:111
}).then((res)=>{
    if(res.data&&res.data.data){
        dictList.value = res.data.data;
    }
})
}

const getData = () =>{
    park_basic_page({
        pageNum:1,
        pageSize:10
    }).then((res)=>{
        console.log(res.data.data.list,'res.data.data------------');
        if(res.data&&res.data.data&&res.data.data.list&&res.data.data.list.length>0){
            dialogData.value=res.data.data.list[0]
        }
    })
}
getDict();
onMounted(() => {
    getData();
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>
.table {
        width: 1120px;
        height: 320px;
        // background-color: antiquewhite;
        table {
            border-collapse: collapse;
        }
        table td {
            border: 1px solid rgba(25, 159, 255, 1);
        }

        .header-cell {
            width: 248px;
height: 40px;
padding-right: 16px;
padding-left: 16px;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: left;
color: rgba(128, 234, 255, 1);
            background-color: rgba(25, 159, 255, 0.1);
        }
        .data-cell {
            width: 248px;
height: 40px;
padding-right: 16px;
padding-left: 16px;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: left;
color: rgba(255, 255, 255, 1);
            .cell-inner {
                width: 248px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
</style>
  