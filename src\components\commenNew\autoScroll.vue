<template>
    <div class="scroll-container scroll-box" ref="container">
      <div class="inner-container" ref="inner-container">
        <slot />
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      deltaTime: {
        default: 100,
      },
      step: {
        default: 1,
      },
    },
    data() {
      return {
        stop: false,
        element: null,
        prevTimestamp:null,
      };
    },
    methods: {
      autoScroll() {
        // console.log("??调用滚动函数");
        const that = this;
        that.element = that.$refs["container"];
        // const r = Math.random();
        const innerScroll = function (timestamp) {
          // console.log("??开始滚动", r, timestamp);
          if (that.stop) {
            return;
          }
  
          if (that.prevTimestamp) {
            const delta = timestamp - that.prevTimestamp;
            if (delta < that.deltaTime) {
              requestAnimationFrame(innerScroll);
              return;
            }
            // console.log("??delta", delta);
          }
          that.prevTimestamp = timestamp;
  
          that.element.scrollTo(0, that.element.scrollTop + that.step);
          if (
            that.element.clientHeight + that.element.scrollTop >=
            that.element.scrollHeight
          ) {
            that.element.scrollTop = 0;
          }
          requestAnimationFrame(innerScroll);
        };
  
        requestAnimationFrame(innerScroll);
      },
      handleOver() {
        this.stop = true;
      },
      handleOut() {
        this.stop = false;
        this.autoScroll();
      },
    },
    mounted() {
      this.autoScroll();
      this.element.addEventListener("mouseover", this.handleOver);
      this.element.addEventListener("mouseout", this.handleOut);
    },
    beforeUnmount() {
      this.stop = true;
      this.element.removeEventListener("mouseover", this.handleOver);
      this.element.removeEventListener("mouseout", this.handleOut);
    },
  };
  </script>
  
  <style lang="less" scoped>
  * {
    box-sizing: border-box;
  }
  
  .scroll-container {
    width: 100%;
    height: 100%;
    overflow: hidden scroll;
    .inner-container {
      padding-bottom: 2em;
    }
  }
  </style>
  