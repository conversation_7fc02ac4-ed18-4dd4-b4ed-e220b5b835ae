<template>
    <Card title="封闭化管理/今日">
        <template v-slot>
            <div class="manage">
                <div class="left">
                    <div class="num">{{ dataObj.weight?dataObj.weight:0 }}</div>
                    <div class="unit">吨/今日</div>
                    <div class="title">危险品流转量</div>
                </div>
                <div class="right">
                    <div class="chemical-car">
                        <div class="num">{{ dataObj.heavyTruck?dataObj.heavyTruck:0 }}</div>
                        <div class="title">入园危化车(辆)</div>
                    </div>
                    <div class="society-car">
                        <div class="num">{{ dataObj.other?dataObj.other:0 }}</div>
                        <div class="title">入园普通车辆(辆)</div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import {
    securityToday
} from "@/assets/js/api/closedManagement";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    onUnmounted,
} from "vue";
const { proxy } = getCurrentInstance();

let dataObj = ref({
    weight:0,
    other:0,
    heavyTruck:0
})
const getData = () => {
    securityToday().then((res) => {
        console.log(res);
        if(res.data&&res.data.data){
            dataObj.value = res.data.data;
        }
    });
};
proxy.$bus.on("security_car_statistics", () => {
    obtainData();
});
onMounted(() => {
   
    getData();
});
onUnmounted(() => {
    proxy.$bus.off("security_car_statistics");
});
</script>

<style lang="less" scoped>
.manage {
    width: 416px;
    height: 224px;
    padding: 16px;
    display: flex;
    gap: 44px;
    .left {
        width: 162px;
        height: 176px;
        background: url("../../../../assets/images/card/turnover-volume.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
        margin-left: 52px;
        margin-top: 27px;
        .num {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
            margin-top: 36px;
        }
        .unit {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 171.429% */
        }
        .title {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
            margin-top: 24px;
        }
    }
    .right {
        .chemical-car {
            width: 106px;
            height: 90px;
            background: url("../../../../assets/images/card/chemical-car.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            margin-top: 19px;
        }
        .society-car {
            width: 106px;
            height: 90px;
            background: url("../../../../assets/images/card/society-car.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            margin-top: 16px;
        }
        .num {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 28px; /* 140% */
        }
        .title {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px; /* 150% */
        }
    }
}
</style>
