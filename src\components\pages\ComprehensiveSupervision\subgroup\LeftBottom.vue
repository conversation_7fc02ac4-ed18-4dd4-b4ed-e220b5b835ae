<template>
    <Card title="企业概况">
        <template v-slot>
    <div class="company-profile">
        <div class="num">
            <div class="content">
                <div class="img"></div>
                <div class="total">
                    <div class="title">总数</div>
                    <div>
                        <span class="size">{{ num.total }}</span
                        >家
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="img"></div>
                <div class="total">
                    <div class="title">正常</div>
                    <div>
                        <span class="size">{{ num.normal_count }}</span
                        >家
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="img"></div>
                <div class="total">
                    <div class="title">停产</div>
                    <div>
                        <span class="size">{{ num.abnormal_count }}</span
                        >家
                    </div>
                </div>
            </div>
        </div>
        <div class="liner"></div>
        <div class="bottom">
            <div class="piewrap" ref="piewrap"></div>
            <div class="show">
                <div
                    class="item"
                    v-for="(item, index) in showData"
                    :key="index"
                >
                    <div class="block"></div>
                    <div class="name">{{ item.name }}</div>
                    <div class="value">{{ item.value }}</div>
                </div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { njenterprise_state } from "../../../../assets/js/api/comprehensiveSupervision";
import { enterprise_industry } from "../../../../assets/js/api/parkArchives";

import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
const num = ref({
    total: 0,
    normal_count: 0,
    abnormal_count: 0,
});
let option;
let piewrap = ref(null);
let pieChart;
let xData = ref([]);
const showData = ref([]);
let chartData = ref([]);
const initChart = () => {
    option = {
        tooltip: {},
        radar: {
            // radius: "50%", //大小
            nameGap: 10, // 图中工艺等字距离图的距离
            center: ["50%", "50%"], // 图的位置
            name: {
                textStyle: {
                    color: "#ffffff",
                    fontSize: 12,
                },
                formatter: function (name) {
                    if (name != null && name.length > 4) {
                        return name.substr(0, 4) + "...";
                    } else {
                        return name;
                    }
                },
            },
            indicator: xData,
            axisLine: {
                lineStyle: {
                    color: "#30ABE8",
                },
            },
            splitArea: {
                show: false,
                areaStyle: {
                    color: "rgba(255,0,0,0)", // 图表背景的颜色
                },
            },
            splitLine: {
                show: true,
                lineStyle: {
                    width: 1,
                    color: "#30ABE8", // 设置网格的颜色
                },
            },
        },

        series: [
            {
                name: "企业概况",
                type: "radar",
                symbol: "angle",
                itemStyle: {
                    normal: {
                        areaStyle: { type: "default" },
                    },
                },
                tooltip: {
                    trigger: "item",
                    position: "top",
                },
                data: [
                    {
                        symbol: "circle",
                        symbolSize: 3,
                        value: chartData.value,
                        areaStyle: { color: "#FFC5194D" },
                        itemStyle: {
                            normal: {
                                borderWidth: 1,
                                color: "#FFC61A",
                                borderColor: "#FFC61A",
                            },
                        },
                        lineStyle: {
                            color: "#FFC61A",
                            width: 1,
                        },
                    },
                ],
            },
        ],
    };
};
onMounted(() => {
    pieChart = echarts.init(piewrap.value);
    njenterprise_state({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            num.value = res.data.data[0];
        }
    });
    enterprise_industry({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            let max = Number(res.data.data[0].count);
            for (let i = 1; i < res.data.data.length; i++) {
                max =
                    max > Number(res.data.data[i].count)
                        ? max
                        : Number(res.data.data[i].count);
            }
            xData = res.data.data.map((x) => {
                return {
                    name: x.sd_business_scope,
                    value: Number(x.count),
                    max: Number(max),
                };
            });
            showData.value = xData;
            xData.forEach((item) => {
                chartData.value.push(item.value);
            });
            initChart();
            pieChart.setOption(option);
            window.addEventListener("resize", () => {
                pieChart.resize();
            });
        }
    });
});
onBeforeUnmount(() => {
    if (pieChart) {
        pieChart.dispose();
    }
});
</script>

<style lang="less" scoped>
.company-profile {
    width: 416px;
    height: 224px;
    padding: 16px;
    .num {
        display: flex;
        justify-content: space-around;
        .content {
            width: 112px;
            height: 48px;
            display: flex;
            gap: 8px;
            .img {
                width: 56px;
                height: 48px;
                background: url("../../../../assets/images/card/company-new.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .total {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 18px;
                .size {
                    color: #fff;
                    font-family: Noto Sans SC;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 28px;
                }
            }
        }
    }
    .liner {
        width: 416px;
        height: 2px;
        margin-top: 12px;
        background: url("../../../../assets/images/card/liner.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
    }
    .bottom {
        width: 416px;
        height: 151px;
        display: flex;
        justify-content: space-around;
        .piewrap {
            width: 290px;
            height: 140px;
            margin-top: 10px;
        }
        .show {
            width: 100px;
            height: 121px;
            margin-top: 30px;
            margin-left: 10px;
            //   display: flex;
            //   flex-direction: column;
            //   justify-content: end;
            overflow: auto;
            // background-color: aqua;
            .item {
                display: flex;
                gap: 4px;

                .block {
                    width: 8px;
                    height: 4px;
                    background: #ffc619;
                    margin-top: 10px;
                }
                .name {
                    color: #fff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
                .value {
                    color: #47ebeb;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 22px;
                }
            }
        }
        .show::-webkit-scrollbar {
            width: 3px;
            height: 10px;
            background-color: transparent;
        }
    }
}
</style>
