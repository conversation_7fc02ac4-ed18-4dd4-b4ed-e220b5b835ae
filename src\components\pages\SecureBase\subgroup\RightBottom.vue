<template>
  <Card title="执法管理">
      <template v-slot:title>
          <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
      </template>
      <template v-slot>
          <div class="law-management-wrapper">
              <div class="law-management-item" style="cursor: pointer;" @click="openDialog('supervisionDocumentDialogShow')">
                 <div class="title">监督检查</div>
                 <div class="center">
                    <div class="center-text">总数</div>
                    <div class="center-num blue-font">{{ supervisionObj.total_count }}</div>
                    <div class="center-text">已完成</div>
                    <div class="center-num blue-font">{{ supervisionObj.pass_count }}</div>
                 </div>
                 <div class="bottom-img bottom-img-blue">
                  <div class="bottom-img-num blue-font">{{ supervisionObj.percent }}%</div>
                  <div class="bottom-img-text">完成率</div>
                 </div>
                 <div class="name">监督检查</div>
              </div>
              <div class="law-management-item" style="cursor: pointer;" @click="openDialog('lawTaskDialogShow')">
                 <div class="title">执法</div>
                 <div class="center">
                    <div class="center-text">总数</div>
                    <div class="center-num yellow-font">{{ enforcementObj.total_count }}</div>
                    <div class="center-text">已完成</div>
                    <div class="center-num yellow-font">{{ enforcementObj.pass_count }}</div>
                 </div>
                 <div class="bottom-img bottom-img-yellow">
                  <div class="bottom-img-num yellow-font">{{ enforcementObj.percent }}%</div>
                  <div class="bottom-img-text">完成率</div>
                 </div>
                 <div class="name">执法检查</div>
              </div>
          </div>
      </template>
  </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue"
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance  } from "vue";
import { supervision_document,law_document } from "@/assets/js/api/secureBase.js";

const { proxy } = getCurrentInstance();
import selectVue from "@/components/commenNew/selectVue.vue";
   const emit = defineEmits(["openDialog"]);
    const openDialog = (val) => {
        console.log(val, '打开弹窗');
        emit('openDialog',val,nowChoose.value);
    }
//按钮选项
const btnOptions = ref([
        {
            label:'今日',
            value:'today'
        },
        {
            label:'本周',
            value:'this_week'
        },
        {
            label:'本月',
            value:'this_month'
        },
        {
            label:'本年',
            value:'this_year'
        }
    ])
//按钮
let chooseValue = ref('本年');
//当前选择
let nowChoose = ref('this_year');
const selectBtn = (val) => {
  console.log(val,'vallllllllll');
  nowChoose.value=val.value;
  getDataSupervision(val.value);
  getDataEnforcement(val.value);
};
//监督检查任务
let supervisionObj = ref({
  total_count:0,
  pass_count:0,
  percent:0
})
//执法检查任务
let enforcementObj = ref({
  total_count:0,
  pass_count:0,
  percent:0
})
//监督检查任务
const getDataSupervision = (val) => {
  supervision_document({
    type:val
  }).then(res => {
    console.log(res);
    if(res.data&&res.data.data&&res.data.data.length>0){
      if(res.data.data[0].total_count&&res.data.data[0].pass_count){

      
      supervisionObj.value=res.data.data[0];
        if(supervisionObj.value.total_count==0&&supervisionObj.value.pass_count==0){
          supervisionObj.value.percent = 0; 
        }else{
          supervisionObj.value.percent = ((Number(supervisionObj.value.pass_count) / Number(supervisionObj.value.total_count))*100).toFixed(2);
        }

      }
    }
  })
}
//执法检查任务
const getDataEnforcement = (val) => {
  law_document({
    type:val
  }).then(res => {
    console.log(res);
    if(res.data&&res.data.data&&res.data.data.length>0){
      if(res.data.data[0].total_count&&res.data.data[0].pass_count){
      enforcementObj.value=res.data.data[0];
      if(enforcementObj.value.total_count==0&&enforcementObj.value.pass_count==0){
        enforcementObj.value.percent = 0; 
      }else{
        enforcementObj.value.percent = ((Number(enforcementObj.value.pass_count) / Number(enforcementObj.value.total_count))*100).toFixed(2);
      }
      }
    }
  })
}
onMounted(() => {
  getDataSupervision('this_year');
  getDataEnforcement('this_year');
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>

  .law-management-wrapper{
      width: 416px;
  height: 224px;
  padding: 16px;
  display: flex;
 .law-management-item{
  width:208px;
  height:224px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .title{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
text-align: center;
color: rgba(255, 255, 255, 1);

  }
  .center{
  width:176px;
padding:0 16px ;
    display: flex;
    justify-content: space-between;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);

  }
  .bottom-img-blue{
  background: url("@/assets/newImages/SecureBase/law-management-blue.svg") no-repeat;
            background-size: cover;
            background-position: center;
}
  .bottom-img-yellow{
  background: url("@/assets/newImages/SecureBase/law-management-yellow.svg") no-repeat;
            background-size: cover;
            background-position: center;
}
  .bottom-img{
    width: 140px;
height: 140px;
margin-top: 6px;
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
.bottom-img-num{
  font-family: Noto Sans SC;
font-weight: 700;
font-size: 24px;
line-height: 32px;
text-align: center;
// color: rgba(128, 234, 255, 1);

}

.bottom-img-text{
  font-family: Noto Sans SC;
font-weight: 500;
font-size: 14px;
line-height: 22px;
text-align: center;
color: rgba(255, 255, 255, 1);
}

  }
  .name{
    margin-top: 8px;
    font-family: Noto Sans SC;
font-weight: 700;
font-size: 16px;
line-height: 22px;
text-align: center;
color: rgba(255, 255, 255, 1);

  }

 }
 .blue-font{
    color:rgba(128, 234, 255, 1)
  }
  .yellow-font{
    color:rgba(255, 223, 128, 1)
  }
  }
</style>
