<template>
    <Card title="环境监测点总览">
        <template v-slot>
            <div class="monitoring-overview">
                <div class="top">
                    <div class="top-img"></div>
            <div class="title">监测点总数</div>
            <div class="num">
                {{ num.monitor_total }}<span class="unit">个</span>
            </div>
        </div>
        <div class="linner"></div>
        <div class="bottom">
            <div class="wrapper-top">
                <div
                    :class="active === '106002' ? 'inner-active' : 'inner'"
                    @click="clickAlarm('106002')"
                >
                    <div class="left">
                        <div
                            :class="
                                active === 1 ? 'left-text-active' : 'left-text'
                            "
                        >
                            污水
                        </div>
                        <div class="left-number">
                            {{ num.waste_water_count
                            }}<span class="left-unit">个</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="right-text">报警</div>
                        <div class="right-number">
                            {{ surface }}<span class="right-unit">次</span>
                        </div>
                    </div>
                </div>
                <div
                    :class="active === '106006' ? 'inner-active' : 'inner'"
                    @click="clickAlarm('106006')"
                >
                    <div class="left">
                        <div
                            :class="
                                active === 1 ? 'left-text-active' : 'left-text'
                            "
                        >
                            废气
                        </div>
                        <div class="left-number">
                            {{ num.waste_gas_count
                            }}<span class="left-unit">个</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="right-text">报警</div>
                        <div class="right-number">
                            {{ gas }}<span class="right-unit">次</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wrapper-bottom">
                <div
                    :class="active === '106001' ? 'inner-active' : 'inner'"
                    @click="clickAlarm('106001')"
                >
                    <div class="left">
                        <div
                            :class="
                                active === 1 ? 'left-text-active' : 'left-text'
                            "
                        >
                            大气
                        </div>
                        <div class="left-number">
                            {{ num.gas_count }}<span class="left-unit">个</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="right-text">报警</div>
                        <div class="right-number">
                            {{ atmosphere }}<span class="right-unit">次</span>
                        </div>
                    </div>
                </div>
                <div
                    :class="active === '106007' ? 'inner-active' : 'inner'"
                    @click="clickAlarm('106007')"
                >
                    <div class="left">
                        <div
                            :class="
                                active === 1 ? 'left-text-active' : 'left-text'
                            "
                        >
                            雨水
                        </div>
                        <div class="left-number">
                            {{ num.rain_count
                            }}<span class="left-unit">个</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="right-text">报警</div>
                        <div class="right-number">
                            {{ rain }}<span class="right-unit">次</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
        </template>
    </Card>
</template>

<script setup>
import {
    monitor_etotal,
    alarm_rain,
    alarm_gas,
    enviroment_alarms,
    equipment_num,
    monitoring_station_num,
    alarm_surface_num,
    alarm_num,
    alarm_gas_num,
    alarm_rain_num,
} from "../../../../assets/js/api/environmentalManagement";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import Card from "@/components/commenNew/Card.vue";
const emit = defineEmits(["alarm"]);
const clickAlarm = (val) => {
    console.log(val);
    active.value = val;

    emit("alarm", val);
};
const active = ref(1);
const setPage = (type) => {
    //   active.value = type;
};
let num = ref({
    monitor_total: 0,
    gas_count: 0,
    rain_count: 0,
    waste_water_count: 0,
    waste_gas_count: 0,
});
const atmosphere = ref(0);
const rain = ref(0);
const surface = ref(0);
const gas = ref(0);
onMounted(() => {
    equipment_num({ type: 106001 }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            num.value.gas_count = res.data.data[0].num;
        }
    });
    equipment_num({ type: "106002" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            num.value.waste_water_count = res.data.data[0].num;
        }
    });
    equipment_num({ type: "106006" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            num.value.waste_gas_count = res.data.data[0].num;
        }
    });
    equipment_num({ type: "106007" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            num.value.rain_count = res.data.data[0].num;
        }
    });
    monitoring_station_num({}).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data[0].num);
            num.value.monitor_total = res.data.data[0].num;
        }
    });
    alarm_surface_num({}).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            surface.value = res.data.data[0].num;
        }
    });
    alarm_num({}).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            atmosphere.value = res.data.data[0].num;
        }
    });
    alarm_gas_num({}).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            gas.value = res.data.data[0].num;
        }
    });
    alarm_rain_num({}).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            rain.value = res.data.data[0].num;
        }
    });
});
</script>

<style lang="less" scoped>
.monitoring-overview {
    // width: 400px;
    // height: 256px;
    width: 416px;
    height: 224px;
    padding: 16px;
    .top {
        width: 304px;
        height: 48px;
        margin: 0 auto;
        display: flex;
        gap: 16px;
        .top-img {
            width: 48px;
            height: 48px;
            background: url("../../../../assets/images/card/monitoring.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .title {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 48px; /* 157.143% */
            text-align: left;
            // vertical-align: bottom;
        }
        .num {
            color: #47ebeb;
            font-family: Digital Numbers;
            font-size: 24px;
            font-style: normal;
            font-weight: 400;
            line-height: 48px; /* 133.333% */
            text-align: right;
            margin: 0 0 0 auto;
        }
        .unit {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 48px; /* 157.143% */
            text-align: left;
            // vertical-align: bottom;
        }
    }
    .linner {
        width: 304px;
        height: 2px;
        margin: 10px auto;
        background: url("../../../../assets/images/card/liner-center.svg")
            no-repeat center center;
        background-size: cover;
        background-position: center;
    }
    .bottom {
        width: 416px;
        height: 164px;
        // background-color: antiquewhite;
        .wrapper-top {
            width: 416px;
            height: 76px;
            display: flex;
            // gap: 12px;
            justify-content: space-between;
        }
        .wrapper-bottom {
            width: 416px;
            height: 76px;
            display: flex;
            // gap: 12px;
            justify-content: space-between;
            margin-top: 10px;
        }
        .inner {
            width: 194px;
            height: 76px;
            background: url("../../../../assets/images/card/border-blue.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            display: flex;
            gap: 12px;
        }
        .inner-active {
            width: 194px;
            height: 76px;
            background: url("../../../../assets/images/card/border-yellow.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            display: flex;
            gap: 12px;
        }
        .left {
            width: 62px;
            height: 52px;
            margin: 14px 0 10px 19px;
        }
        .right {
            width: 80px;
            height: 52px;
            margin: 14px 17px 10px 0;
        }
        .left-text-active {
            color: #fff04c;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .left-text {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .left-number {
            color: #fff;
            font-family: Digital Numbers;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px; /* 140% */
        }
        .left-unit {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
        }
        .right-text {
            color: #ff4c4d;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .right-number {
            color: #ff4c4d;
            font-family: Digital Numbers;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px; /* 140% */
        }
        .right-unit {
            color: #ff4c4d;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
}
</style>
