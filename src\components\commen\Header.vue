<template>
    <div class="header">
        <div class="date-wrapper">
            <p class="special">{{ date.time }}</p>
            <div></div>
            <p>{{ date.day }}</p>
            <div></div>
            <p>{{ date.week }}</p>
        </div>
        <div class="tittle">{{ title }}</div>
        <div class="weather-wrapper">
            <p>晋州</p>
            <div></div>
            <p>{{ weather }}</p>
            <div></div>
            <p>
                <span>{{ temperature.low }}℃</span>/<span
                    >{{ temperature.high }}℃</span
                >
            </p>
        </div>
        <a :href="toWeb">
            <div class="enter-back" @click="enterBack">
                <div class="enter-back-img"></div>
                <p>&nbsp;进入后台</p>
            </div>
        </a>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    nextTick,
} from "vue";
const props = defineProps({
    totalTitle: {
        type: String,
    },
    title1: {
        type: String,
    },
    title2: {
        type: String,
    },
    title3: {
        type: String,
    },
});
const title = ref("晋州马于园智慧化工园区");
const w = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
];
const timer = ref(null);
const date = reactive({
    day: "",
    week: "",
    time: "",
});
const toWeb = ref("http://***********:13003/");

const address = ref(null);
const weather = ref(null);
const temperature = reactive({
    low: "",
    high: "",
});
const getLocation = () => {
    AMap.plugin("AMap.CitySearch", function () {
        var citySearch = new AMap.CitySearch();
        citySearch.getLocalCity(function (status, result) {
            if (status === "complete" && result.info === "OK") {
                address.value = result.city;
                AMap.plugin("AMap.Weather", function () {
                    var wea = new AMap.Weather();

                    wea.getForecast(result.city, function (err, data) {
                        const forecasts = data.forecasts[0];
                        console.log(forecasts);
                        temperature.low = forecasts.nightTemp;
                        temperature.high = forecasts.dayTemp;
                        weather.value = forecasts.dayWeather;
                        console.log(weather);
                    });
                });
            }
        });
    });
};
const enterBack = () => {
    // toWeb.value =
    //     window.location.protocol + "//" + window.location.hostname + ":13003";
    toWeb.value ='https://city189.cn:2926/';
};
onMounted(() => {
    timer.value = setInterval(() => {
        var today = new Date();
        var month = ("0" + (today.getMonth() + 1)).slice(-2);
        var day1 = ("0" + today.getDate()).slice(-2);
        date.day = today.getFullYear() + "-" + month + "-" + day1;
        date.week = w[today.getDay()];
        var hour = ("0" + today.getHours()).slice(-2);
        var minutes = ("0" + today.getMinutes()).slice(-2);
        var second = ("0" + today.getSeconds()).slice(-2);
        date.time = hour + ":" + minutes + ":" + second;
    }, 1000);
    nextTick(() => {
        getLocation();
    });
    // address.value = '宁晋';
    // weather.value = '多云'
    // temperature.low = '18',
    // temperature.high = '38'
});
onBeforeUnmount(() => {
    clearInterval(timer.value);
});
</script>

<style lang="less" scoped>
.header {
    width: 100%;
    // height: 160px;
    height: 72px;
    // background: url('../../assets/images/header-masking.svg') no-repeat;
    background: url("@/assets/newImages/header.svg") no-repeat;
    // background: url("../../assets/images/header-bg.svg") no-repeat, linear-gradient(180deg, rgba(10, 37, 92, 0.45) 0%, rgba(10, 37, 92, 0.45) 71%, rgba(10, 37, 92, 0) 100%);
    background-size: cover;
    background-position: center;
    // position: relative;
    position: absolute;
    top: 0;
    left: 0;
    // z-index: 1000000002;
    z-index: 1001;
    .date-wrapper {
        position: absolute;
        top: 22px;
        left: 24px;
        display: flex;
        .special {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 28px;
        }
        > p {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px;
        }

        > div {
            width: 1px;
            height: 16px;
            background: #30abe8;
            margin: 0 16px;
        }
    }
    .tittle {
        width: 451px;
        height: 48px;
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        text-shadow: 0px 2px 2px rgba(5, 32, 46, 0.25);
        font-family: MStiffHei PRC;
        font-size: 40px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
    .weather-wrapper {
        position: absolute;
        top: 22px;
        right: 154px;
        display: flex;
        .special {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 28px;
        }
        > p {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px;
        }

        > div {
            width: 1px;
            height: 16px;
            background: #30abe8;
            margin: 0 16px;
        }
    }
    .enter-back {
        position: absolute;
        top: 22px;
        right: 24px;
        display: flex;
        .enter-back-img {
            width: 28px;
            height: 28px;
            background: url("../../assets/images/icon/enter.svg") no-repeat;
            // background: url("../../assets/images/header-bg.svg") no-repeat, linear-gradient(180deg, rgba(10, 37, 92, 0.45) 0%, rgba(10, 37, 92, 0.45) 71%, rgba(10, 37, 92, 0) 100%);
            background-size: cover;
            background-position: center;
        }
        > p {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 28px;
        }
    }
}
</style>
