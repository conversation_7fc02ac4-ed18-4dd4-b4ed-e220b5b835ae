    <template>
        <Card title="安全生产行政许可">
            
            <template v-slot>
                <div class="administrative-license-wrapper">
                    <div class="top">
                        <div class="top-item" style="cursor: pointer;" @click="openDialog('productCertificateDialogShow')">
                            <img class="top-item-img" src="@/assets/newImages/SecureBase/administrative-license-top-1.svg"/>
                            <div class="top-item-text">
                                <div class="top-item-text-title">许可证数量</div>
                                <div class="top-item-text-num">{{ top1 }}</div>
                            </div>
                        </div>
                        <div class="top-item" style="cursor: pointer;" @click="openDialog('productCertificateExpireDialogShow')">
                            <img class="top-item-img" src="@/assets/newImages/SecureBase/administrative-license-top-2.svg"/>
                            <div class="top-item-text">
                                <div class="top-item-text-title">即将到期数量</div>
                                <div class="top-item-text-num">{{top2}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom">
                        <div class="header">
                            <img class="header-icon" src="@/assets/newImages/SecureBase/administrative-license-icon.svg"/>
                            <div class="header-title">建设项目三同时</div>
                            <img class="header-line" src="@/assets/newImages/SecureBase/administrative-license-line.svg"/>
                            <div class="header-btns">
                                <div v-for="item in selectList" :key="item.value" class="header-btn" :class="{'header-active-btn':item.value === selectValue}" @click="clickSelect(item)">{{item.name}}</div>
                            </div>
                        </div>
                        <div class="bottom-content">
                            <div class="piewrap" ref="piewrap"></div>
                            <div class="bottom-content-right">
                                <div class="bottom-content-right-item" v-for="item in rightList" :key="item.value">
                                    <div class="item-left">
                                        <div class="left-circle" :style="{borderColor:item.color}"></div>
                                        <div class="left-text">{{item.name}}</div>
                                    </div>
                                    <div class="item-right">
                                        <div class="right-text">{{item.value}}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Card>
    </template>

    <script setup>
import * as echarts from "echarts";

    import Card from "@/components/commenNew/Card.vue"
    import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance  } from "vue";
    import { setSize } from "@/assets/js/echartsSetSizeNew";
    import {product_certificate,construct_meanwhile } from "@/assets/js/api/secureBase";
    const { proxy } = getCurrentInstance();
    const emit = defineEmits(["openDialog"]);
    const openDialog = (val) => {
        console.log(val, '打开弹窗');
        emit('openDialog',val);
    }
    //许可证数量
    let top1 = ref(0);
    //即将到期数量
    let top2 = ref(0);
    let rightList = ref([
        {
            name:'新建项目',
            value:0,
            color:'rgba(26, 160, 255, 1)',
            percent:0
        },
        {
            name:'扩建项目',
            value:0,
            color:'rgba(23, 207, 191, 1)',
            percent:0
        },
        {
            name:'改建项目',
            value:0,
            color:'rgba(255, 128, 0, 1)',
            percent:0
        }
        
    ])
    let selectList = ref([
        {
            name:'今日',
            value:'today'
        },
        {
            name:'本周',
            value:'this_week'
        },
        {
            name:'本月',
            value:'this_month'
        },
        {
            name:'本年',
            value:'this_year'
        }
    ])
    let selectValue = ref('this_year');
    
    const getDataTop = (val) => {
            product_certificate({}).then(res =>{
            console.log(res);
            if(res.data&&res.data.data&&res.data.data.length>0){
                let result = res.data.data;
                top1.value = result.find(item => item.table_name === 'product_certificate').record_count;
                top2.value = result.find(item => item.table_name === 'product_certificate_deadLine').record_count;
            }
        })
    }
    const clickSelect = (item) => {
        selectValue.value = item.value;
        getDataBottom();
    }
    //建设三同时总数
    let total = ref(0);
    //建设项目三同时
    const getDataBottom = (val) => {
        construct_meanwhile({
            time_type:selectValue.value
        }).then(res =>{
            console.log(res);
            if(res.data&&res.data.data&&res.data.data.length>0){
                let result = res.data.data[0];
                rightList.value[0].value = result.new_build_count;
                rightList.value[1].value = result.expand_build_count;
                rightList.value[2].value = result.rebuild_count;
                total.value = Number(result.new_build_count) + Number(result.expand_build_count) + Number(result.rebuild_count);
                rightList.value[0].percent = Number(result.new_build_count) / total.value;
                rightList.value[1].percent = Number(result.expand_build_count) / total.value;
                rightList.value[2].percent = Number(result.rebuild_count) / total.value;
                if (pieChart) {
                    pieChart.dispose();
                }
                pieChart = echarts.init(piewrap.value);
                initChart();
                pieChart.setOption(option);
                window.addEventListener("resize", () => {
                    pieChart.resize();
                });
            }
            
        })
    }
    let option;
let piewrap = ref(null);
let pieChart;
let xData = ref([]);
const showData = ref([]);
let chartData = ref([]);
//建设项目三同时数据
let obj = ref({
    expand_build_count:0,
    new_build_count:0,
    rebuild_count:0
})
const initChart = () => {
    option = {
            // 大标题（主标题）
        title: [{
            text: total.value,  // 大标题
            left: 'center',
            top: '30%',
            textStyle: {
                fontSize: setSize(20),
                fontWeight: 700,
                lineHeight: setSize(28),
                color: 'rgba(255, 255, 255, 1)'
            }
        }, 
        // 小标题（副标题）
        {
            text: '项目总数',  // 小标题
            left: 'center',
            top: '55%',
            textStyle: {
                fontSize: setSize(12),
                fontWeight: 400,
                lineHeight: setSize(16),
                color: 'rgba(255, 255, 255, 1)'
            }
        },
        ],
        tooltip: {
            show: true,
            trigger: "item",
            formatter: function (params) {
                // 获取当前数据项的颜色
        const color = params.color || params.data.itemStyle.normal.color;
        
        // 创建彩色圆点图标
        const icon = `<span style="display:inline-block;margin-right:6px;width:8px;height:8px;border-radius:50%;background:${color}"></span>`;
        
        return `${icon} ${params.name}<br/>${params.value}个`;
            },
            backgroundColor: 'rgba(19, 67, 134, 0.6)',
            borderColor: 'rgba(25, 159, 255, 1)',
            extraCssText: `
                backdrop-filter: blur(4px);
                -webkit-backdrop-filter: blur(4px);
                border-radius: 4px;
                padding: 8px 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            `,
            textStyle: {
                color: '#fff',
                fontSize: setSize(12),
                lineHeight: setSize(16),
            }
        },
        
            color: ['rgba(176, 212, 251, 1)'],
           

            series: [{
              name: 'Line 1',
              type: 'pie',
              clockWise: true,
              radius: ['73%', '93%'],
              itemStyle: {
                normal: {
                  label: {
                    show: false
                  },
                  labelLine: {
                    show: false
                  }
                }
              },
              hoverAnimation: false,
              data: [{
                value: rightList.value[0].value,
                name: '新建项目',
                itemStyle: {
                  normal: {
                    color:  'rgba(26, 160, 255, 1)',
                    
                  }
                }
              }, {
                name: '扩建项目',
                value: rightList.value[1].value,
                itemStyle: {
                  normal: {
                    color:  'rgba(23, 207, 191, 1)',
                    
                  }
                }
              }, {
                name: '改建项目',
                value: rightList.value[2].value,
                itemStyle: {
                  normal: {
                    color:  'rgba(255, 128, 0, 1)',
                    
                  }
                }
              }]
            },{
        itemStyle: {
            normal: {
                color: 'rgba(62,109,255,0.4)',
            }
        },
        type: 'pie',
        hoverAnimation: false,
        radius: ['66%', '100%'],
        center: ["50%", "50%"],
        label: {
            normal: {
                show:false
            }
        },
        data: [{
                value: 0,
            }],
        z:-1
    }]
        };
};
    onMounted(() => {
        getDataTop();
        
        getDataBottom();
        
    });
    onBeforeUnmount(() => {
    if (pieChart) {
        pieChart.dispose();
    }
});
    </script>

    <style lang="less" scoped>

        .administrative-license-wrapper{
            width: 416px;
        height: 224px;
        padding: 16px;
            .top{
            width: 416px;
    height: 56px;
    display: flex;
    justify-content: space-between;
    .top-item{
        width: 160px;
        height: 56px;
        padding:0 20px;
        display: flex;
        gap: 8px;
        .top-item-img{
            width: 56px;
            height: 56px;
        }
        .top-item-text{
            display: flex;
            flex-direction: column;
            justify-content: center;
            .top-item-text-title{
                font-family: Noto Sans SC;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: rgba(255, 255, 255, 1);
            }
            .top-item-text-num{
                font-family: DINPro;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    color: rgba(255, 255, 255, 1);
            }

        }

    }
            }
            .bottom{
                width: 416px;
    height: 152px;
    margin-top: 16px;
    .header{
        display: flex;
        align-items: center;
        .header-icon{
        width:6px;
        height: 8px;
    }
    .header-title{
        margin-left: 8px;
        font-family: Noto Sans SC;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 1);

    }
    .header-line{
        width: 74px;
        height: 2px;
        margin-left: 16px;
    }
    .header-btns{
        width: 184px;
    height: 24px;
    display: flex;
    gap: 8px;
    font-family: Noto Sans SC;
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    .header-btn{
        width: 40px;
    height: 24px;
    border-radius: 2px;
    border: 0.5px solid rgba(26, 178, 255, 1);
    background: rgba(26, 178, 255, 0.3);
    }
    .header-active-btn{
        width: 40px;
    height: 24px;
    border-radius: 2px;
    background:rgba(255, 198, 26, 0.3);
    border: 0.5px solid rgba(255, 198, 26, 1)
    }
    }
    }
    .bottom-content{
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        .piewrap {
            width: 120px;
            height: 120px;
            margin-left: 40px;
        }
        .bottom-content-right{
            width: 200px;
            // padding:0 12px;
            height: 104px;
            margin: auto 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .bottom-content-right-item{
                width: 176px;
                height: 24px;
            padding:0 12px;

                background: linear-gradient(90deg, rgba(128, 234, 255, 0.15) 0%, rgba(128, 234, 255, 0) 100%);
                display: flex;
                align-items: center;
                justify-content: space-between;
                .item-left{
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    .left-circle{
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        border: 2px solid ;
                    }
                    .left-text{
                        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
                    }
                    
                }
                .right-text{
                        font-family: DIN Alternate;
                        font-weight: 700;
                        font-size: 14px;
                        line-height: 20px;
                        color: rgba(255, 255, 255, 1);

                    }
            }
        }
    }
            }
        }
    </style>
