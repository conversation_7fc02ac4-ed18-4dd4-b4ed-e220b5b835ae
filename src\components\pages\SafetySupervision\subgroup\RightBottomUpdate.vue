<template>
  <Card title="重大危险源监控设备">
    <template v-slot>
      <div class="special-assignments-wrapper">
  <div class="special-assignments">
    <div class="all-message">
      <div class="rightTop">
        <div class="rightItem">
          <div class="rightTitle">视频监控</div>
          <div class="rightContent all">{{ alarmData.videoOnline + alarmData.videoOffline }}</div>
        </div>
        <div class="rightItem">
          <div class="rightTitle">在线</div>
          <div class="rightContent online">{{ alarmData.videoOnline }}</div>
        </div>
        <div class="rightItem">
          <div class="rightTitle">离线</div>
          <div class="rightContent offline">{{ alarmData.videoOffline }}</div>
        </div>
      </div>
      <div class="rightBottom">
        <div class="rightItems">
          <div class="rightTitles">监测指标设备</div>
          <div class="rightContent all">{{ alarmData.indicatorOnline + alarmData.indicatorOffline }}</div>
        </div>
        <div class="rightItem">
          <div class="rightTitle">在线</div>
          <div class="rightContent online">{{ alarmData.indicatorOnline }}</div>
        </div>
        <div class="rightItem">
          <div class="rightTitle">离线</div>
          <div class="rightContent offline">{{ alarmData.indicatorOffline }}</div>
        </div>
      </div>
    </div>
  </div>
  </div>
  </template>
  </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
  special_work_safety_year,
  special_work_safety_month,
  v2_spcial_work, api_v2_camera, api_v2_gas,
} from "@/assets/js/api/safetySupervision";

import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
  watch,
  shallowRef
} from "vue";

const {proxy} = getCurrentInstance();
const showText = ref(true);
//今日报警
const alarmData = shallowRef({
  videoOnline: 0,
  videoOffline: 0,
  indicatorOnline: 0,
  indicatorOffline: 0
});
onMounted(() => {
  api_v2_camera().then((res) => {
    if (res.data.success && res.data.data) {
      alarmData.value = {
        ...alarmData.value,
        videoOnline:Number(res.data.data.find(t => t.equipment_status == 1).num),
        videoOffline:Number(res.data.data.find(t => t.equipment_status == 0).num),
      }
    }
  });
  api_v2_gas().then((res) => {
    if (res.data.success && res.data.data) {
      alarmData.value = {
        ...alarmData.value,
        indicatorOnline:Number(res.data.data.find(t => t.is_active == 1).num),
        indicatorOffline:Number(res.data.data.find(t => t.is_active == 0).num),
      }
    }
  });
});
</script>

<style lang="less" scoped>
.special-assignments-wrapper{
  width: 416px;
  height: 224px;
  padding: 16px;
}
.special-assignments {
  width: 416px;
  height: 224px;
  position: relative;

  .all-message {
    width: 416px;
    height: 216px;
    background: url("../../../../assets/images/updateNew/equipment-alarm.svg") no-repeat;
    //background: url("../../../../assets/images/header-bg.svg") no-repeat;
    background-size: cover;
    background-position: center;
    margin-top: 1px;
    z-index: 999;
  }

  .rightTop {
    position: absolute;
    left: 150px;
    top: 10px;
    width: 222px;
    height: 48px;
    display: flex;
    justify-content: space-between;

  }

  .rightBottom {
    position: absolute;
    left: 130px;
    top: 140px;
    width: 242px;
    height: 48px;
    display: flex;
    justify-content: space-between;
  }

  .rightItem {
    width: 56px;
    height: 48px;

    .rightTitle {
      width: 56px;
      height: 20px;
      font-family: Noto Sans SC;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      text-align: center;
      color: rgba(255, 255, 255, 1);
    }

    .rightContent {
      font-family: Digital Numbers;
      font-size: 20px;
      font-weight: 400;
      line-height: 28px;
      text-align: center;
      //color: rgba(48, 171, 232, 1);
    }

  }

  .rightItems {
    width: 102px;
    height: 48px;

    .rightTitles {
      width: 102px;
      height: 20px;
      font-family: Noto Sans SC;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      text-align: center;
      color: rgba(255, 255, 255, 1);
    }

    .rightContent {
      font-family: Digital Numbers;
      font-size: 20px;
      font-weight: 400;
      line-height: 28px;
      text-align: center;
      //color: rgba(48, 171, 232, 1);
    }

  }
}

.all {
  color: #95e0d2;
}

.online {
  color: #91d88e;
}

.offline {
  color: red;
}
</style>
