<template>
  <Card title="监测指标告警统计">
    <template v-slot>
  <div class="card-content">
    <div class="tab">
      <div :class="active === 0 ? 'active-btn' : 'btn'" @click="setPage(0)">本日</div>
      <div :class="active === 1 ? 'active-btn' : 'btn'" @click="setPage(1)">本周</div>
      <div :class="active === 2 ? 'active-btn' : 'btn'" @click="setPage(2)">本月</div>
    </div>
    <div class="linewrap" ref="linewrap"></div>
  </div>
  </template>
  </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import {setSize} from "@/assets/js/echartsSetSizeNew";
import {ref, reactive, onMounted, onBeforeUnmount} from "vue";
import {count} from "@/assets/js/api/specialAssignments";
import {api_v2_alarm_count} from "@/assets/js/api/safetySupervision";

const active = ref(0);
let option;
let linewrap = ref(null);
let lineChart;
let echartData = reactive({
  total: [447.46, 774.46, 779.12, 705.34, 793.92, 523.20],
  xData: ['2017', '2018', '2019', '2020', '2021', '2022'],
})
const xData = ref([]);
const yData = ref([]);
const initChart = () => {
  option = {
    title: [
      {
        text: '单位：个',
        left: '0%',
        top: '10%',
        textStyle: {
          color: '#fff',
          fontSize: setSize(12),
          fontWeight: 'normal',
        },
      },

    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(13, 81, 115, 0.6)", //通过设置rgba调节背景颜色与透明度
      color: "#FFFFFF",
      borderWidth: setSize(3),
      borderColor: "#1AB3FF",
      textStyle: {
        color: "#FFFFFF",
        fontSize: setSize(14),
      },
      confine: true,
      formatter: function (val) {
        let backString = ''
        val.forEach(item => {
          if (item.seriesName !== '虚线') {
            if (item.componentIndex === 0) {
              backString += item.marker + `${item.seriesName} ${item.data}</br>`
            } else {
              backString += item.marker + `${item.seriesName} ${item.data}`
            }
          }
        })
        return `${val[0]?.name}</br>` + backString
      },
    },
    grid: {
      left: "2%",
      right: "2%",
      bottom: "0%",
      top: "25%",
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'slider',
        show: false,
        startValue: 0,
        endValue: 1,
        xAxisIndex: [0],
        handleSize: 0,//滑动条的 左右2个滑动条的大小
        height: 5,//组件高度
        bottom: 0,//右边的距离
        borderColor: "#e3e3e3",
        fillerColor: '#51B7F9',
        borderRadius: 10,
        backgroundColor: '#e3e3e3',//两边未选中的滑动条区域的颜色
        showDataShadow: false,//是否显示数据阴影 默认auto
        showDetail: false,//即拖拽时候是否显示详细数值信息 默认true
        realtime: true, //是否实时更新
        filterMode: 'filter',
        zlevel: -10,
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        zoomOnMouseWheel: false, //滚轮不触发缩放
        moveOnMouseMove: true,   //鼠标移动触发平移
        moveOnMouseWheel: true,  //鼠标滚轮触发平移
      },
    ],
    xAxis: [
      {
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: "rgba(54, 169, 226, 0.6)",
          },
        },
        axisLabel: {
          interval: 0,
          color: '#FFFFFF',
          fontSize: setSize(12),
        },
        data: xData.value,
      },
      {
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: xData.value,
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          fontSize: setSize(12),
          color: "#FFFFFF",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(255, 255, 255, 0.3)",
          },
        },
      },
      {
        type: "value",
        position: "right",
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: "#FFFFFF",
          fontSize: setSize(12),
          // formatter: "{value}%", //右侧Y轴文字显示
        },
      },
    ],
    series: [
      {
        name: "产值",
        type: "bar",
        barWidth: setSize(24),
        z: 1,
        itemStyle: {
          borderColor: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(71, 235, 235, 1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(71, 235, 235, 0)", // 100% 处的颜色
              }
            ],
            global: false, // 缺省为 false
          },
          borderWidth: setSize(1.5),
          color: function (params) {
            return {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,

              colorStops: [
                {
                  offset: 0,
                  color: "rgba(71, 235, 235, 0.6)", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "rgba(71, 235, 235, 0.15)", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            }
          }
        },
        data: yData.value,
        markPoint: {
          symbol: "diamond",//rect
          symbolSize: [setSize(12), setSize(12)],
          label: {
            show: false,
          },
          data: yData.value
              .map((v, i) => {
                return {
                  value: v,
                  xAxis: i,
                  yAxis: v,
                  itemStyle: {
                    color: "#47EBEB",
                  },
                };
              }),
        },
      },
      {
        name: "虚线",
        type: "bar",
        xAxisIndex: 1,
        data: yData.value,
        barWidth: setSize(1),
        itemStyle: {
          color: "transparent",
          borderColor: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {offset: 0, color: "rgba(71, 235, 235, 1)"},
              {offset: 1, color: "rgba(71, 235, 235, 0)"},
            ],
          },
          borderWidth: setSize(2),
          borderType: [setSize(2), setSize(4)],
        },
      },

    ],
  };
};
const setPage = (value) => {
  active.value = value
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
  const day = String(today.getDate()).padStart(2, '0');
  let endTime = `${year}-${month}-${day}`
  let begins = {
    0: `${year}-${month}-${day}`,
    1: getWeekDay(),
    2: getMonthDay(),
  }
  getData(begins[value], endTime)
}
const getMonthDay = () => {
  const today = new Date();
  const oneMonthAgo = new Date(today);
  const currentDate = today.getDate(); // 当前日期
  oneMonthAgo.setMonth(today.getMonth() - 1); // 设置为一个月前的日期
  if (oneMonthAgo.getDate() !== currentDate) {
    oneMonthAgo.setDate(0); // 设置为上一个月的最后一天
  }
  const year = oneMonthAgo.getFullYear();
  const month = String(oneMonthAgo.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
  const day = String(oneMonthAgo.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`; // 格式化为 yyyy-mm-dd
}
const getWeekDay = () => {
  const today = new Date();
  const oneWeekAgo = new Date(today);
  const currentDate = today.getDate(); // 当前日期
  oneWeekAgo.setDate(today.getDate() - 7); // 设置为一周前的日期
  if (oneWeekAgo.getDate() > currentDate) {
    oneWeekAgo.setDate(0); // 设置为上一个月的最后一天
  }
  const year = oneWeekAgo.getFullYear();
  const month = String(oneWeekAgo.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
  const day = String(oneWeekAgo.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`; // 格式化为 yyyy-mm-dd
}
const getData = (begin, end) => {
  lineChart = echarts.init(linewrap.value);
  console.log(`${begin} 00:00:00`, end, 'begin,end')
  yData.value = []
  xData.value = []
  api_v2_alarm_count({
    beginTime: `${begin} 00:00:00`,
    endTime: `${end} 23:59:59`
  }).then((res) => {
    if (res.data.success && res.data.data && res.data.data.length) {
      res.data.data.forEach((item) => {
        xData.value.push(item.enterprise_name);
        yData.value.push(item.num);
      })
      // console.log(echartData.value);
      initChart();
      lineChart.setOption(option);
      window.addEventListener("resize", () => {
        lineChart.resize();
      });
    }
  });
}
onMounted(() => {
  setPage(0)
});
onBeforeUnmount(() => {
  if (lineChart) {
    lineChart.dispose();
  }
});
</script>
<style lang="less" scoped>
.card-content{
  width: 416px;
    height: 224px;
    padding: 16px;
}
.linewrap {
  width: 416px;
  height: 284px;
  margin-top: 16px;
}

.tab {
  position: absolute;
  display: flex;
  gap: 10px;
  right: 10px;
}

.button {
  color: #fff;
  z-index: 99;
}

.active-btn {
  width: 56px;
  height: 24px;
  color: #fff;
  text-align: center;
  font-family: Noto Sans SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 133.333% */
  background: url("@/assets/images/card/active-btn.svg") no-repeat center center;
  background-size: cover;
  background-position: center;
  z-index: 99;
}

.btn {
  width: 56px;
  height: 24px;
  color: #fff;
  text-align: center;
  font-family: Noto Sans SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 133.333% */
  background: url("@/assets/images/card/btn.svg") no-repeat center center;
  background-size: cover;
  background-position: center;
  z-index: 99;
}
</style>
