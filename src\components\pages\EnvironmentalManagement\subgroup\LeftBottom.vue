<template>
    <Card title="雨水环境质量">
        <template v-slot>
            <div class="rainwater-environment">
                <div class="select">
            <el-select
                v-model="enterpriseValue"
                class="m-2"
                placeholder="请选择"
                @change="changeEnterprise"
                filterable
            >
                <el-option
                    v-for="item in enterpriseOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-model="value"
                class="m-2"
                placeholder="请选择"
                @change="change"
                filterable
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </div>

        <div class="linewrap" ref="linewrap" v-show="!nonFlag"></div>
        <div class="all-wrapper" v-show="nonFlag">
                    <div class="img-no">暂无数据</div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import {
    waste_indicator,
    erain_quality,
    enterpriseList,
    threshold_rain,
} from "../../../../assets/js/api/environmentalManagement";
const value = ref();
const options = ref([]);
const enterpriseValue = ref();
const enterpriseOptions = ref([]);
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let yData = ref([]);
let yData1 = ref([]);
const initChart = () => {
    option = {
        grid: {
            left: 10,
            right: 28,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        tooltip: {
            show: true,
            trigger: "axis",
        },
        legend: {
            show: true,
            x: "right", // 图例水平居中
            y: "top", // 图例垂直居上
            itemStyle: { opacity: 0 }, //去圆点
            textStyle: {
                color: "#fff",
            },
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    color: "#1AB2FF",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#397cbc",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "#195384",
                    },
                },
                data: xData,
            },
        ],
        yAxis: [
            {
                type: "value",
                // name: "单位：",
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#fff",
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#27b4c2",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
            },
        ],
        series: [
            {
                name: "当前水质",
                type: "line",
                showSymbol: false,
                smooth: true,
                markPoint: {
                    symbol: "circle",
                },
                // markLine: {
                //   symbol: "none",
                //   label: {
                //     normal: {
                //       show: false,
                //       color: "#fff",
                //       backgroundColor: "rgba(228,0,54,70)",
                //       fontSize: 16,
                //       padding: 4,
                //       borderRadius: 4,
                //       show: true,
                //       position: "start",
                //       distance: 4,
                //     },
                //   },
                //   lineStyle: {
                //     type: "dotted",
                //     color: "#FF4C4D",
                //     width: 1,
                //   },
                //   data: [
                //     {
                //       yAxis: 0,
                //     },
                //     { yAxis: 0 },
                //   ],
                // },
                itemStyle: {
                    normal: {
                        color: "#1AB2FF",
                        lineStyle: {
                            color: "#30ABE8",
                            width: 1,
                        },
                        areaStyle: {
                            //color: '#94C9EC'
                            color: new echarts.graphic.LinearGradient(
                                0,
                                1,
                                0,
                                0,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(26, 178, 255, 0.00)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(26, 178, 255, 0.30)",
                                    },
                                ],
                            ),
                        },
                    },
                },
                data: yData,
            },
            //   {
            //     name: "目标水质",
            //     type: "line",
            //     showSymbol: false,
            //     smooth: true,
            //     itemStyle: {
            //       normal: {
            //         color: "#1AB2FF",
            //         lineStyle: {
            //           color: "#30ABE8",
            //           width: 1,
            //         },
            //         areaStyle: {
            //           //color: '#94C9EC'
            //           color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            //             {
            //               offset: 0,
            //               color: "rgba(26, 178, 255, 0.00)",
            //             },
            //             {
            //               offset: 1,
            //               color: "rgba(26, 178, 255, 0.30)",
            //             },
            //           ]),
            //         },
            //       },
            //     },
            //     data: yData1,
            //   },
        ],
    };
};
const change = (val) => {
    value.value = val;
    xData = [];
    yData = [];
    yData1 = [];
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
    SearchErainQuality();
};
const changeEnterprise = (val) => {
    console.log("--------------");
    console.log("改变", val);
    enterpriseValue.value = val;
    xData = [];
    yData = [];
    yData1 = [];
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
    SearchErainQuality();
};
//获取企业列表
const getEnterpriseList = () => {};
//暂无数据标志
const nonFlag = ref(true);
//获取当前水质
const SearchErainQuality = () => {
    erain_quality({
        factor_id: value.value,
        enterprise_archives_id: enterpriseValue.value,
    }).then((re) => {
        if (re.data.success && re.data.data && re.data.data.length) {
            nonFlag.value = false;

            re.data.data.forEach((item) => {
                xData.push(item.acquisition_time.split(" ")[1]);
                yData.push(item.monitor_value);
            });
            console.log(yData);
            lineChart = echarts.init(linewrap.value);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        } else {
            nonFlag.value = true;

            //   lineChart = echarts.init(linewrap.value);
            //   initChart();
            //   lineChart.setOption(option);
            //   window.addEventListener("resize", () => {
            //     lineChart.resize();
            //   });
        }
    });
};
// const search = () => {
//     threshold_rain
// }
onMounted(() => {
    xData = [];
    yData = [];
    yData1 = [];
    getEnterpriseList();
    waste_indicator({ equipment_type: 106007 }).then((res) => {
        console.log(res);
        if (res.data.success && res.data.data && res.data.data.length) {
            options.value = res.data.data.map((x) => {
                return {
                    value: x.id,
                    label: x.factor_name,
                };
            });
            value.value = options.value[0].value;
            enterpriseList({}).then((r) => {
                if (r.data.success && r.data.data && r.data.data.length) {
                    enterpriseOptions.value = r.data.data.map((x) => {
                        return {
                            value: x.id,
                            label: x.enterprise_name,
                        };
                    });
                    enterpriseValue.value = enterpriseOptions.value[0].value;
                    SearchErainQuality();
                }
            });
        }
    });

    //   xData = [
    //     "星期一",
    //     "星期二",
    //     "星期三",
    //     "星期四",
    //     "星期五",
    //     "星期六",
    //     "星期日",
    //   ];
    //   yData = [11, 22, 15, 26, 33, 15, 25];
    //   yData1 = [12, 25, 9, 36, 12, 15, 22];
});
onBeforeUnmount(() => {
    // loopShowTooltip(lineChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.rainwater-environment {
    // width: 400px;
    // height: 256px;
    width: 416px;
    height: 224px;
    padding: 16px;
    .select {
        width: 416px;
        height: 24px;
        margin-left: 140px;
        display: flex;
        gap: 16px;
        :deep(.el-select) {
            width: 122px;
            height: 24px !important;
            color: #ffffff;
        }
        :deep(.el-input__wrapper) {
            background-color: rgba(26, 178, 255, 0.3);
            // border: 1px solid #1A9FFF !important;
            // border-radius: 4px;
            height: 24px !important;
        }
        :deep(.el-input__inner) {
            // background-color: rgba(26, 178, 255, 0.30);
            color: #ffffff;
            height: 24px !important;
        }
    }
    .all-wrapper {
        width: 400px;
        height: 192px;
        display: flex;
        align-items: center;
        justify-content: center;
        .img-no {
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
        }
    }
    .linewrap {
        width: 416px;
        height: 224px;
        margin-top: 8px;
    }
}
</style>
