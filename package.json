{"name": "vite-vue", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --mode develop", "build": "vite build --mode production", "preview": "vite preview", "format": "npx prettier . --write"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "aegis-web-sdk": "^1.35.25", "axios": "0.18.0", "cesium": "^1.122.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.2.18", "js-md5": "^0.7.3", "less": "^4.1.3", "pinia": "^2.0.0-rc.10", "pnpm": "^7.6.0", "postcss-px-to-viewport-8-plugin": "^1.1.5", "qs": "^6.11.0", "rollup": "2.25.0", "trtc-js-sdk": "latest", "vue": "^3.2.25", "vue-i18n": "9", "vue-router": "^4.0.16", "vue3-clipboard": "^1.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^2.3.3", "echarts": "^5.3.3", "echarts-gl": "^2.0.9", "js-base64": "^3.7.2", "mitt": "^3.0.0", "postcss-px-to-viewport": "^1.1.1", "stylus": "^0.54.8", "stylus-loader": "^3.0.2", "swiper": "^8.4.7", "vite": "^2.9.16", "vite-plugin-cesium": "^1.2.22", "vue3-seamless-scroll": "^2.0.1"}}