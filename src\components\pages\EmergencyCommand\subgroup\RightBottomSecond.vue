<template>
    <Card title="应急演练">
        <template v-slot>
    <div class="right-bottom-second">
        <!--         <div class="progress-box" v-for="(item,index) in peopleData" :key="index" >-->
        <!--             <div class="title-box">-->
        <!--                 <div class="title-box-left">-->
        <!--                    <div class="ranking">NO.{{ index+1 }}</div>-->
        <!--                    <div class="name">&nbsp;{{item.name}}</div>-->
        <!--                 </div>-->
        <!--                 <div class="title-box-right">-->
        <!--                    <div class="num">-->
        <!--                        {{item.value}}&nbsp;-->
        <!--                    </div>-->
        <!--                    <div class="unit">tce/万元</div>-->
        <!--                 </div>-->
        <!--                 -->
        <!--                 </div>-->
        <!--             <el-progress class="progress-height" :stroke-width="16"  :percentage="item.percentage"></el-progress>-->
        <!--         </div>-->
        <div class="content_bxo">
            <div class="top_box">
                <div class="top_left_box">
                    <img
                        class="iamge_boxs"
                        src="../../../../assets/images/command/jihua.svg"
                    />
                    <div class="top_right_box">
                        <div class="top_left_title">计划总数</div>
                        <div class="top_left_number">{{ toplist.count }}</div>
                    </div>
                </div>
                <div>
                    <div class="echarts_box" id="echars" ref="chartsBox"></div>
                </div>
            </div>
            <div>
                <table border="0" cellspacing="0" class="tables">
                    <tr class="head">
                        <th class="th_1">演练类型</th>
                        <th class="th_2">演练名称</th>
                        <th class="th_3">是否完成</th>
                        <th class="th_4">上报时间</th>
                    </tr>
                    <tr v-for="(item, index) in peopleData" :key="index">
                        <td>
                            <div class="tdFontDiv" style="width: 108px">
                                {{ item.plan_name }}
                            </div>
                        </td>
                        <td>
                            <div class="tdFontDiv" style="width: 10px">
                                {{ item.drill_type }}
                            </div>
                        </td>
                        <td>
                            <div class="tdFontDiv" style="width: 86px">
                                {{ item.is_end_rescue == "1" ? "是" : "否" }}
                            </div>
                        </td>
                        <td>
                            <div class="tdFontDiv" style="width: 86px">
                                {{ item.drill_time }}
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import {
    getrehearsalTotal,
    getrehearsalList,
} from "../../../../assets/js/api/api";
import * as echarts from "echarts";
// 副标题
const subtext = ref("完成率");
// 最大值
const max = ref(100);
const peopleData = ref([]);
const toplist = ref({
    count: "",
    complete_rate: "",
});
const myChart = ref(null);
const chartData = ref({
    planComplete: 100,
    realComplete: 86,
});
const chartsBox = ref(null);
const datas = ref(
    (chartData.value.realComplete / chartData.value.planComplete) * 100 + "",
);
let option = reactive({});
const infoecharts = () => {
    // if (chart.value) {

    // 在这里配置图表选项和数据

    // }
    // totalChart = echarts.init(chartsBox.value);
    // initChartRank();
    // totalChart.setOption(optionRank);
    console.log(datas, datas.value, "测试");
    option = {
        // backgroundColor: '#313131',
        backgroundColor: "transparent",
        title: {
            text: subtext.value,
            subtext: `${toplist.value.complete_rate}%`,
            left: "center",
            top: "22%",
            itemGap: 0,
            textStyle: {
                color: "#FFFFFF",
                fontSize: "10",
                fontWeight: 400,
                lineHeight: "16",
                fontFamily: "Noto Sans SC",
            },
            subtextStyle: {
                color: "#FFF",
                fontSize: "14",
                fontWeight: 700,
                lineHeight: "22",
                fontFamily: "Noto Sans SC",
            },
        },
        angleAxis: {
            max,
            // 隐藏刻度线
            show: false,
            startAngle: 270,
            clockwise: false, // 逆时针
        },
        radiusAxis: {
            type: "category",
            show: true,
            axisLabel: {
                show: false,
            },
            axisLine: {
                show: false,
            },
            axisTick: {
                show: false,
            },
        },
        polar: {
            //radius: '165%' //图形大小
            radius: "150%", //图形大小
        },
        series: [
            {
                type: "bar",
                roundCap: 1,
                data: [toplist.value.complete_rate],
                showBackground: true,
                backgroundStyle: {
                    color: "#666",
                },
                coordinateSystem: "polar",
                barWidth: 10,
                itemStyle: {
                    normal: {
                        // color: 'rgb(28,177,108)'
                        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                            {
                                offset: 0,
                                //color: 'rgb(71, 235, 235,0.15)'
                                color: "rgb(71, 235, 235,1)",
                            },
                            {
                                offset: 1,
                                //color: 'rgb(71, 235, 235,1)'
                                color: "rgb(71, 235, 235,0.15)",
                            },
                        ]),
                    },
                },
            }, // 圆环
            {
                type: "pie",
                //radius: ["88.5%", "89%"],
                radius: ["55.5%", "55%"],
                center: ["50%", "50%"],
                barWidth: 5,
                emptyCircleStyle: {
                    color: "rgba(71, 235, 235, 0.15)",
                },
            },
        ],
    };
    myChart.value.setOption(option);
};

// const peopleData=ref([])
// const serachEnergy = () => {
//    energy_unit_of_output_rank({ "": "" }).then((res) => {
//         if (res.data.success && res.data.data && res.data.data.length) {
//              let max = res.data.data[0].result
//              peopleData.value = res.data.data.map((x) => {
//                   return {
//                        name: x.enterprise_name,
//                        value: x.result,
//                        percentage: (x.result/max)*100
//                   }
//            })
//    }
//  });
//
// }
const getinfo = () => {
    getrehearsalTotal().then((res) => {
        console.log(res, "获取演练数据");
        toplist.value.count = res.data.data[0].count;
        toplist.value.complete_rate = res.data.data[0].complete_rate.replace(
            "%",
            "",
        );
        myChart.value = echarts.init(chartsBox.value);
        infoecharts();
    });
};
const getinfoList = () => {
    getrehearsalList({}).then((res) => {
        peopleData.value = res.data.data.slice(0, 3);
    });
};
onMounted(() => {
    getinfo();
    getinfoList();
    console.log(datas, datas.value, "测试");
});
</script>

<style lang="less" scoped>
.echarts_box {
    width: 84px;
    height: 84px;
}
.content_bxo {
    width: 416px;
    height: 224px;
    .top_box {
        display: flex;
        justify-content: space-around;
        .top_left_box {
            display: flex;
            justify-content: start;
            align-items: center;
            .top_right_box {
                margin-left: 12px;
            }
            .iamge_boxs {
                width: 72px;
                height: 56px;
                flex-shrink: 0;
            }
            .top_left_title {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 142.857% */
            }
            .top_left_number {
                color: #fff;
                font-family: DIN Alternate;
                font-size: 20px;
                font-style: normal;
                font-weight: 700;
                line-height: 28px; /* 140% */
            }
        }
    }
}
.right-bottom-second {
    width: 416px;
    height: 224px;
    padding: 16px;
}
.progress-box {
    width: 400px;
    height: 50px;
}
.title-box {
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    justify-content: space-between;
    .title-box-left {
        display: flex;
        .ranking {
            color: #47ebeb;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 22px; /* 157.143% */
        }
    }
    .title-box-right {
        display: flex;
        .num {
            color: #fff;
            text-align: right;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 22px; /* 157.143% */
        }
        .unit {
            color: #fff;
            text-align: right;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 150% */
        }
    }
}

::v-deep .el-progress-bar__inner {
    background: linear-gradient(
        270deg,
        rgba(71, 235, 235, 0.6) 0%,
        rgba(71, 235, 235, 0.15) 100%
    );
    height: 12px;
    margin: 2px;
    border-image-source: linear-gradient(
        270deg,
        rgba(71, 235, 235, 0.6) 0%,
        rgba(71, 235, 235, 0.15) 100%
    );
    border-radius: 0%;
}
::v-deep .el-progress {
    height: 16px;
}
::v-deep .el-progress-bar__outer {
    width: 400px;
    height: 16px !important;
    background-color: rgba(255, 255, 255, 0);
    // border: 1px solid #4C94FF;
    border-radius: 0%;
}
::v-deep .el-progress__text {
    display: none;
}
.progress-height {
    height: 16px;
}
.tables {
    table-layout: fixed;
    width: 416px !important;
    // height: 232px;
    .head {
        height: 30px !important;
    }
}
tr:nth-child(1) {
    height: 32px;
    color: #fff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400 !important;
    line-height: 22px; /* 137.5% */
    letter-spacing: 0.96px;
    background: linear-gradient(
        90deg,
        rgba(22, 115, 139, 0) 3.2%,
        rgba(22, 115, 139, 0.5) 50.16%,
        rgba(22, 115, 139, 0) 100%
    );

    /* background: linear-gradient(270deg, #16738B 3.16%, rgba(22, 115, 139, 0.00) 100.2%); */
}
th {
    /*font-weight: 400 !important;*/
    /*font-size: 14px;*/
    color: #47ebeb;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 142.857% */
}
.th_1 {
    width: 80px;
    height: 36px !important;
}
.th_2 {
    width: 80px;
}
.th_3 {
    width: 80px;
}
.thFontDiv {
    width: 98px;
}
td {
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
    text-align: center;
    /*color: #9DDCEA;*/
    /*text-align: center;*/
    /*font-size: 14px;*/
    /*font-style: normal;*/
    /*font-weight: 400;*/
    /*line-height: 22px; !* 157.143% *!*/
    letter-spacing: 0.84px;
    height: 32px;
    border-bottom: 1px solid rgba(14, 161, 176, 0.35);
}
.tdFontDiv {
    width: 100% !important;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
</style>
