<template>
  <div class="select_show">
    <div class="select_show_choose">
      {{ chooseValue }}
    </div>
    <div class="select_content" v-if="showSelect">
      <div class="select_content_filter">
        <div
            v-for="item in sonList"
            :key="item"
            class="select_content_little"
            @click="choose(item)"
            :title="item.label"
        >
          <div class="select_content_little_label">
            {{ item.label }}


          </div>
        </div>
      </div>

    </div>
    <img
        src="@/assets/newImages/select-icon.svg"
        alt=""
        class="select_img"
        @click="isShowImg"
    />
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  defineProps,
  toRefs,
  watch,
  defineEmits
} from "vue";

const props = defineProps({
  sonList: {
    type: Array,
  },
  chooseValue: {
    type: String,
  },
});
const chooseValue = ref(props.chooseValue);
const sonList = ref(props.sonList);
const showSelect = ref(false);
const emit = defineEmits()
watch(
    props,
    (newValue, oldValue) => {
      console.log(newValue);
      chooseValue.value = newValue.chooseValue;
      sonList.value = newValue.sonList;
    },
    {deep: true, immediate: true}
);
onMounted(() => {
});
const isShowImg = () => {
  showSelect.value = !showSelect.value;
};
const choose = (e) => {
  console.log(chooseValue.value, e,'lallal');
  chooseValue.value = e.label;
  showSelect.value = false;
  emit('choose', e);

};
</script>

<style lang="less" scoped>
.select_show {
  width: 68px;
height: 16px;
padding: 4px 8px;
  display: flex;
  border-radius: 2px;
  border: 0.62px solid #1A9FFF;
  position: relative;
  font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 16px;
letter-spacing: 0%;
color:#80EAFF;
}

.select_show_choose {
  /* width:84px; */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.select_content_little_label {
  /* width: 103px; */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.select_img {
  width: 20px;
  height: 20px;
  margin-left: auto;
}

.select_content {
  width: 100%;
  max-height: 100px;
  overflow-y: auto;
  border: 1px solid #1A9FFF99;
  // background: #051D2E99;
  background: #051D2E99;

  // background: rgba(64, 181, 255, 0.3);
  position: absolute;
  top: 32px;
  left: 0px;
  padding: 5px 0;
  z-index: 999;
}

.select_content_filter {
  position: relative;
  // background: rgba(13, 72, 85, 0.5);
  //backdrop-filter: blur(1px);
  color: #fff;
  font-family: Noto Sans SC;
  font-size: 14px;
  backdrop-filter: blur(4px)

}

// .select_content_filter::before{
//  content: '';
//   position: absolute;
//   top: 0;
//   right: 0;
//   bottom: 0;
//   left: 0;
// background: rgba(64, 181, 255, 0.3);
// filter: blur(10px);
// }
.select_content_little {
  padding-left: 8px;
  box-sizing: border-box;
  position: relative;
  font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 24px;
letter-spacing: 0%;
color:#199FFF;
  height: 24px;
  width: 100%;
}

// .select_content_little::before{
// height: 24px;
//   width: 100%;
//   position: absolute;
//   left: 0;
//   top: 0;
//    filter: blur(4px);
//   background: rgba(64, 181, 255, 0.3);
//   z-index: -1;
//     content: '';
// }
// .select_content_little::after{
// height: 24px;
//   width: 100%;
//   position: absolute;
//   left: 0;
//   top: 0;
//    filter: blur(4px);
//   background: rgba(64, 181, 255, 0.3);
//   z-index: -1;
//     content: '';
// }
</style>