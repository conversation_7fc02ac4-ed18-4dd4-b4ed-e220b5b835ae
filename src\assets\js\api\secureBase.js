import { fetch } from "../request";
//园区基础信息
export const park_basicInfo = (params) =>fetch("/api-v2/park_basicInfo", { ...params }, "POST");
//园区企业
export const park_enterpriseStatistics = (params) =>fetch("/api-v2/park_enterpriseStatistics", { ...params }, "POST");
//第三方单位
export const park_thirdEnterprise = (params) =>fetch("/api-v2/park_thirdEnterprise", { ...params }, "POST");
//装置开停车和大检修
export const park_outageMaintenanceRecord = (params) =>fetch("/api-v2/park_outageMaintenanceRecord", { ...params }, "POST");
//两重点一重大
export const park_majorDanger = (params) =>fetch("/api-v2/park_majorDanger", { ...params }, "POST");
//企业列表
export const selectEnterprise = (params) => fetch("/information-v2/parkIocApi/enterprise/select-page", { ...params }, "POST");
//字典
export const parkIocApi_allList = (params) => fetch("information-v2/parkIocApi/allList", { ...params }, "POST");
//企业详情
export const enterpriseDetail = (params) => fetch(`/information-v2/parkIocApi/enterprise/select-detail/${params}`, {}, "GET");
//企业监测预警
export const equipmentAlarm = (params) => fetch("/api-v2/equipmentAlarm", { ...params }, "POST");
//企业敏捷应急
export const emergencyStatistic = (params) => fetch("/api-v2/emergencyStatistic", { ...params }, "POST");
//企业重大危险源
export const park_majorDangerSource = (params) => fetch("/api-v2/park_majorDangerSource", { ...params }, "POST");
//企业双重运行效果
export const warn_result = (params) => fetch("/api-v2/information/warn-result", { ...params }, "POST");
//企业特殊作业
export const sepcial_work = (params) => fetch("/api-v2/information/sepcial-work", { ...params }, "POST");
//企业安全评估报告
export const assess_report = (params) => fetch("/api-v2/information/assess-report", { ...params }, "POST");
//企业SIL等级评估报告
export const level_report = (params) => fetch("/api-v2/information/level-report", { ...params }, "POST");
//安全承诺公告
export const promise = (params) => fetch("/api-v2/information/promise", { ...params }, "POST");
//企业信息相关字典
export const getDict = (params) => fetch("/information-v2/parkIocApi/allList", { ...params }, "POST");


//企业详情内部详细弹窗
//告警列表
export const alarmStatistics = (params) => fetch("/safety-v2/alarm-statistics/select-page", { ...params }, "POST");
//设备列表
export const pageByAttribute = (params) => fetch("/equipment/maintain-standing-book/pageByAttribute", { ...params }, "POST");

//应急相关
//字典
export const dictionary = (params) => fetch("/emergency-v2/dict/dictionary", { ...params }, "GET");
//企业预案
export const emergencyPlan = (params) => fetch("/emergency-v2/emergency-plan-manage-firm/pageList", { ...params }, "POST");
//救援物资库
export const emergencySupply = (params) => fetch("/emergency-v2/emergency-supply-depot/queryByPage", { ...params }, "GET");
//救援物资
export const emergencyMaterial = (params) => fetch("/emergency-v2/emergency-material/page", { ...params }, "GET");
//应急队伍
export const emergencyExpertContingent = (params) => fetch("/emergency-v2/emergency_expert_contingent/pageList", { ...params }, "POST");
//应急专家
export const emergencyExpert = (params) => fetch("/emergency-v2/emergency-expert/page", { ...params }, "GET");
//事件类型列表
export const emergencyEventType = (params) => fetch("/emergency-v2/emergency-event-type/list", { ...params }, "GET");
//企业演练
export const enterpriseDrill = (params) => fetch("/emergency-v2/enterpriseDrill/page", { ...params }, "GET");
/*
企业详细信息表格
*/

//厂房车间
export const plantWorkshop = (params) => fetch("/information-v2/parkIocApi/plantWorkshop/select-page", { ...params }, "POST");
//生产线
export const productLine = (params) => fetch("/information-v2/parkIocApi/productLine/select-page", { ...params }, "POST");
//生产设备
export const productEquipment = (params) => fetch("/information-v2/parkIocApi/productEquipment/select-page", { ...params }, "POST");
//消防设备
export const firefightEquipment = (params) => fetch("/information-v2/parkIocApi/firefightEquipment/select-page", { ...params }, "POST");
//特种设备
export const specialEquipment = (params) => fetch("/information-v2/parkIocApi/specialEquipment/select-page", { ...params }, "POST");
//工艺
export const craft = (params) => fetch("/information-v2/parkIocApi/craft/select-page", { ...params }, "POST");
//仓库
export const entrepo = (params) => fetch("/information-v2/parkIocApi/entrepo/select-page", { ...params }, "POST");
//罐区
export const tankFarm = (params) => fetch("/information-v2/parkIocApi/tankFarm/select-page", { ...params }, "POST");
//储罐
export const storageTank = (params) => fetch("/information-v2/parkIocApi/storageTank/select-page", { ...params }, "POST");
//产品
export const product = (params) => fetch("/information-v2/parkIocApi/product/select-page", { ...params }, "POST");
//原辅料
export const rawMaterial = (params) => fetch("/information-v2/parkIocApi/rawMaterial/select-page", { ...params }, "POST");
//产能耗
export const energyReportData = (params) => fetch("/information-v2/parkIocApi/energyReportData/page", { ...params }, "GET");
//用电量
export const equipmentData = (params) => fetch("/information-v2/parkIocApi/equipmentData/current", { ...params }, "GET");



//新接口
//园区基础信息
export const park_basicInfo_new = (params) =>fetch("/api-v2/jz-information/park_basicInfo", { ...params }, "POST");
//禁限控目录
export const forbidden_control = (params) =>fetch("/api-v2/jz-information/forbidden_control", { ...params }, "POST");
//两重点一重大
export const major_danger = (params) =>fetch("/api-v2/jz-information/major_danger", { ...params }, "POST");
//安全生产许可
export const product_certificate = (params) =>fetch("/api-v2/jz-information/product_certificate", { ...params }, "POST");
//建设项目三同时
export const construct_meanwhile = (params) =>fetch("/api-v2/jz-information/construct_meanwhile", { ...params }, "POST");
//执法管理-监督检查
export const supervision_document = (params) =>fetch("/api-v2/jz-information/supervision_document", { ...params }, "POST");
//执法管理-执法
export const law_document = (params) =>fetch("/api-v2/jz-information/law_document", { ...params }, "POST");
//装置开停车
export const outage_maintenance = (params) =>fetch("/api-v2/jz-information/outage_maintenance", { ...params }, "POST");
