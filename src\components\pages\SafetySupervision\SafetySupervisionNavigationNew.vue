<template>
    <div>
        <div class="input-wrapper" v-if="showFlag == 3">
            <el-select
                v-model="enterpriseName"
                class="m-2"
                placeholder="请选择"
                clearable
                @change="changeEnterpriseName"
                filterable
            >
                <el-option
                    v-for="item in enterpriseNameOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </div>
        <div class="safety-supervision-navigation" v-if="showFlag != 1">
            <div
                v-if="allinshow && !allType"
                :class="active === 11 ? 'active-btn' : 'btn'"
                @click="setPage(11)"
            >
                <img class="icon" src="../../../assets/images/icon/video.svg" />
                <div class="text">全部</div>
            </div>
            <div v-else-if="allType" class="noactive">
                <img class="icon" src="../../../assets/images/icon/video.svg" />
                <div class="text">全部</div>
            </div>
            <div
                v-if="typeStatus[4].count != '0'"
                :class="
                    active === 4 || active === 1 || active === 2 || active === 3
                        ? 'active-btn'
                        : 'btn'
                "
                @click="setPage(4)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/majorHazardh.svg"
                />
                <div class="text">重大危险源</div>
            </div>
            <div v-else class="noactive">
                <img
                    class="icon"
                    src="../../../assets/images/icon/majorHazardh.svg"
                />
                <div class="text">重大危险源</div>
            </div>
            <div
                v-if="typeStatus[5].count != '0'"
                :class="active === 6 ? 'active-btn' : 'btn'"
                @click="setPage(6)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/highRiskProcesses.svg"
                />
                <div class="text">重点工艺</div>
            </div>
            <div v-else class="noactive">
                <img
                    class="icon"
                    src="../../../assets/images/icon/highRiskProcesses.svg"
                />
                <div class="text">重点工艺</div>
            </div>
            <div
                v-if="typeStatus[6].count != '0'"
                :class="active === 8 ? 'active-btn' : 'btn'"
                @click="setPage(8)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/hazardousChemicals.svg"
                />
                <div class="text">监管化学品</div>
            </div>
            <div v-else class="noactive">
                <img
                    class="icon"
                    src="../../../assets/images/icon/hazardousChemicals.svg"
                />
                <div class="text">监管化学品</div>
            </div>

            <div
                v-if="typeStatus[7].count != '0'"
                :class="active === 5 ? 'active-btn' : 'btn'"
                @click="setPage(5)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/riskPoints.svg"
                />
                <div class="text">风险点</div>
            </div>
            <div v-else class="noactive">
                <img
                    class="icon"
                    src="../../../assets/images/icon/riskPoints.svg"
                />
                <div class="text">风险点</div>
            </div>
            <!-- <div :class="active === 7 ? 'active-btn' : 'btn'" @click="setPage(7)">
        <img class="icon" src="../../../assets/images/icon/toxicGas.svg" />
        <div class="text">可燃有毒气体</div>
      </div> -->

            <div
                v-if="typeStatus[8].count != '0'"
                :class="active === 10 ? 'active-btn' : 'btn'"
                @click="setPage(10)"
            >
                <img class="icon" src="../../../assets/images/icon/video.svg" />
                <div class="text">视频</div>
            </div>
            <div v-else class="noactive">
                <img class="icon" src="../../../assets/images/icon/video.svg" />
                <div class="text">视频</div>
            </div>
            <div v-if="allinshow && !allType">
                <div
                    v-if="typeStatus[8].count != '0'"
                    :class="active === 12 ? 'active-btn' : 'btn'"
                    @click="setPage(12)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/monitor.svg"
                    />
                    <div class="text">全方位监控</div>
                </div>
                <div v-else class="noactive">
                    <img
                        class="icon"
                        src="../../../assets/images/icon/monitor.svg"
                    />
                    <div class="text">全方位监控</div>
                </div>
            </div>
        </div>
        <div class="major-sub" id="sub" v-show="majorSubShow">
            <div
                v-if="typeStatus[0].count != '0'"
                :class="active === 1 ? 'active-btn' : 'btn'"
                @click="setPage(1)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/workshop.svg"
                />
                <div class="text">厂房车间</div>
            </div>
            <div v-else class="noactive">
                <img
                    class="icon"
                    src="../../../assets/images/icon/workshop.svg"
                />
                <div class="text">厂房车间</div>
            </div>

            <div
                v-if="typeStatus[1].count != '0'"
                :class="active === 2 ? 'active-btn' : 'btn'"
                @click="setPage(2)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/warehouse.svg"
                />
                <div class="text">仓库</div>
            </div>
            <div v-else class="noactive">
                <img
                    class="icon"
                    src="../../../assets/images/icon/warehouse.svg"
                />
                <div class="text">仓库</div>
            </div>

            <div
                v-if="typeStatus[2].count != '0'"
                :class="active === 3 ? 'active-btn' : 'btn'"
                @click="setPage(3)"
            >
                <img class="icon" src="../../../assets/images/icon/tank.svg" />
                <div class="text">罐区</div>
            </div>
            <div v-else class="noactive">
                <img class="icon" src="../../../assets/images/icon/tank.svg" />
                <div class="text">罐区</div>
            </div>
        </div>

        <workshop-dialog
            :pickWorkshopkIsGrey="pickWorkshopkIsGrey"
            :pickId="pickWorkshopkId"
            part="安全监管"
            v-if="workshopDialogShow"
            @closeDialog="closeDialog"
        ></workshop-dialog>
        <warehouse-dialog
            :pickId="warehouseId"
            part="安全监管"
            :warehouseIsGrey="warehouseIsGrey"
            v-if="warehouseDialogShow"
            @closeDialog="closeDialog"
        ></warehouse-dialog>
        <tank-dialog
            :pickId="tankId"
            part="安全监管"
            :tankIsGrey="tankIsGrey"
            v-if="tankDialogShow"
            @closeDialog="closeDialog"
        ></tank-dialog>
        <risk-points-dialog-new
            :pickId="riskPointsId"
            :pickName="riskPointsName"
            v-if="riskPointsDialogShow"
            @closeDialog="closeDialog"
        ></risk-points-dialog-new>
        <highrisk-processes-dialog
            :pickId="highriskProcessesId"
            v-if="highriskProcessesDialogShow"
            @closeDialog="closeDialog"
        ></highrisk-processes-dialog>
        <toxic-gas-dialog
            v-if="ToxicGasDialogShow"
            @closeDialog="closeDialog"
        ></toxic-gas-dialog>
        <hazardous-chemicals-dialog
            :pickId="hazardousChemicalsId"
            v-if="hazardousChemicalsDialogShow"
            @closeDialog="closeDialog"
        ></hazardous-chemicals-dialog>
        <monitoring-points-dialog
            part="安全监管"
            :pickId="monitoringPointskId"
            :monitoringPointskIsGrey="monitoringPointskIsGrey"
            v-if="monitoringPointsDialogShow"
            @closeDialog="closeDialog"
        ></monitoring-points-dialog>
        <major-hazard-dialog
            :pickId="pickMajorId"
            v-if="majorHazardDialogShow"
            @closeDialog="closeDialog"
        ></major-hazard-dialog>
        <infrastructure-dialog
            :pickId="videoId"
            v-if="videoDialogShow"
            @closeDialog="closeDialog"
        ></infrastructure-dialog>
        <special-assignments-dialog
            :pickId="specialId"
            v-if="specialDialogShow"
            @closeDialog="closeDialog"
        ></special-assignments-dialog>
    </div>
</template>

<script setup>
import * as echarts from "echarts";
import { setSize } from "../../../assets/js/echartsSetSize";
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    nextTick,
} from "vue";
import * as Cesium from "cesium";
import WorkshopDialog from "./subgroup/WorkshopDialog.vue";
import WarehouseDialog from "./subgroup/WarehouseDialog.vue";
import TankDialog from "./subgroup/TankDialog.vue";
import RiskPointsDialog from "./subgroup/RiskPointsDialog.vue";
import RiskPointsDialogNew from "./subgroup/RiskPointsDialogNew.vue";
import HighriskProcessesDialog from "./subgroup/HighriskProcessesDialog.vue";
import ToxicGasDialog from "./subgroup/ToxicGasDialog.vue";
import HazardousChemicalsDialog from "./subgroup/HazardousChemicalsDialog.vue";
import MonitoringPointsDialog from "./subgroup/MonitoringPointsDialog.vue";
import MajorHazardDialog from "./subgroup/MajorHazardDialog.vue";
import videoDialog from "./subgroup/videoDialog.vue";
import infrastructureDialog from "./subgroup/infrastructureDialog.vue";
import {
    getEquipmentInfo,
    getFlv,
    major_danger_source_info,
    major_danger_source_info_all,
    plant_workshop,
    entrepo,
    tank_farm,
    storage_tank,
    storage_tank_info,
    risk_register,
    risk_register_info,
    danger_chemical_process_list,
    danger_chemical_process_info,
    danger_chemical_list,
    getInformationStatus,
    getInformationStatusMds,
    data_plant_workshop,
    data_entrepo,
    data_tank_farm,
    data_storage_tank,
    data_major_danger_source,
    mds_workshop_list,
    mds_entrepo_list,
    mds_tank_farm_list,
    mds_storage_tank_list,
} from "../../../assets/js/api/safetySupervision";
import {
    nj_equipments,
    enterprise_list,
} from "../../../assets/js/api/parkArchives";
import { defineProps } from "vue";
const { proxy } = getCurrentInstance();

//是否展示重大危险源附属
const majorSubShow = ref(false);

//判断是否置灰
const typeStatus = ref([
    {
        table_name: "plant_workshop",
        count: "1",
    },
    {
        table_name: "entrepo",
        count: "1",
    },
    {
        table_name: "tank_farm",
        count: "1",
    },
    {
        table_name: "storage_tank",
        count: "1",
    },
    {
        table_name: "major_danger_source",
        count: "1",
    },
    {
        table_name: "danger_chemical_process",
        count: "1",
    },
    {
        table_name: "danger_chemical",
        count: "1",
    },
    {
        table_name: "risk_register",
        count: "1",
    },
    {
        table_name: "camera",
        count: "1",
    },
]);
const showFlag = ref(1);

const allType = ref(false);
const workshopDialogShow = ref(false);
const warehouseDialogShow = ref(false);
const tankDialogShow = ref(false);
const riskPointsDialogShow = ref(false);
const highriskProcessesDialogShow = ref(false);
const ToxicGasDialogShow = ref(false);
const hazardousChemicalsDialogShow = ref(false);
const monitoringPointsDialogShow = ref(false);
const majorHazardDialogShow = ref(false);
const videoDialogShow = ref(false);
const specialDialogShow = ref(false);
const props = defineProps({
    map: Object,
});
const active = ref(0);
const dialogActive = ref(1);
const allinshow = ref(false);
const hazardDialog = ref(false);
const img = ref(null);
const pickMajorId = ref(null);
const pickWorkshopkId = ref(null);
const pickWorkshopkIsGrey = ref(false);
const warehouseId = ref(null);
const warehouseIsGrey = ref(false);
const monitoringPointskId = ref(null);
const monitoringPointskIsGrey = ref(false);
const riskPointsId = ref(null);
const riskPointsName = ref(null);
const highriskProcessesId = ref(null);
const tankId = ref(null);
const tankIsGrey = ref(false);
const hazardousChemicalsId = ref(null);
const videoId = ref(null);
const specialId = ref(null);

//厂房车间不置灰数组
const plantWorkshopNoGrey = ref([]);
//仓库不置灰数组
const entrepoNoGrey = ref([]);
//罐区不置灰数组
const tankNoGrey = ref([]);
//储罐不置灰数组
const storageTankNoGrey = ref([]);
//重大危险源不置灰数组
const majorDangerSourceNoGrey = ref([]);
const enterpriseName = ref("");
const enterpriseContentOptions = ref([]);
//改变企业控制置灰
const changeEnterpriseName = (e) => {
    majorSubShow.value = false;
    //有企业判断置灰哪些按钮并默认选中全部
    if (e) {
        document.getElementById("sub").style.top = "632px"; //570px没企业 632px有企业
        allinshow.value = true;
        if (!active.value || (active.value == 0 && !allType.value)) {
            active.value = 11;
        }
        getInformationStatus({ enterprise_name: e }).then((res) => {
            console.log(res.data.data, "测试接口");
            res.data.data.forEach((items) => {
                typeStatus.value.forEach((item) => {
                    if (items.table_name === item.table_name) {
                        item.count = items.count;
                    }
                });
            });
            allType.value = typeStatus.value.every((item) => item.count == "0");
            console.log(typeStatus.value, "typeStatus.value", allType.value);

            getInformationStatusMds({ enterprise_name: e }).then((res1) => {
                res1.data.data.forEach((items) => {
                    typeStatus.value.forEach((item) => {
                        if (items.table_name === item.table_name) {
                            item.count = items.count;
                        }
                    });
                });
                allType.value = typeStatus.value.every(
                    (item) => item.count == "0",
                );
            });
        });
    } else {
        document.getElementById("sub").style.top = "570px"; //458px没企业 516px有企业
        active.value = 0;
        typeStatus.value.forEach((item) => {
            item.count = "1";
        });
        allinshow.value = false;
        allType.value = false;
    }

    window.viewer.entities.removeAll(); //删除所有
    window.map1.clearMap();
    if (window.cluster) window.cluster.setMap(null);
    // initBoundary()
    console.log(
        positionData.value,
        "positionData.value",
        areaData.value,
        "areaData.value",
        areaDataThree.value,
    );
    determineLayer(active.value);
};
//关闭弹窗
const closeDialog = (value) => {
    console.log(value);
    switch (value) {
        case "workshop":
            workshopDialogShow.value = false;
            break;
        case "warehouse":
            warehouseDialogShow.value = false;
            break;
        case "tank":
            tankDialogShow.value = false;
            break;
        case "riskPoints":
            riskPointsDialogShow.value = false;
            break;
        case "highriskProcesses":
            highriskProcessesDialogShow.value = false;
            break;
        case "toxicGas":
            ToxicGasDialogShow.value = false;
            break;
        case "hazardousChemicals":
            hazardousChemicalsDialogShow.value = false;

            break;
        case "monitoringPoints":
            monitoringPointsDialogShow.value = false;
            break;
        case "majorHazar":
            majorHazardDialogShow.value = false;
            break;
        case "video":
            videoDialogShow.value = false;

            break;
        case "specialDialogShow":
            specialDialogShow.value = false;

            break;

        default:
            break;
    }
    proxy.$loading.hide();
};
var zIndex = 30;
//改变按钮
const setPage = (type) => {
    console.log(positionData.value, "数据");
    if (active.value === type) {
        active.value = 0;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        if (window.cluster) {
            window.cluster.setMap(null);
        }
        //   initBoundary()
        majorSubShow.value = false;
    } else {
        active.value = type;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        if (window.cluster) window.cluster.setMap(null);
        //   initBoundary()
        // 打点前临时
        if (type == 4 || type == 1 || type == 2 || type == 3) {
            majorSubShow.value = true;
        } else {
            majorSubShow.value = false;
        }
        determineLayer(type);
    }
};
//确定查找哪个图层
const determineLayer = (type) => {
    switch (type) {
        case 1:
            data_plant_workshop({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                    }
                    searchPlantWorkshop("", 1, "");
                },
            );
            break;
        case 2:
            data_entrepo({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        entrepoNoGrey.value = re1.data.data;
                    }
                    searchEntrepo("", 2, "");
                },
            );
            break;
        case 3:
            data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        tankNoGrey.value = re1.data.data;
                    }
                    searchTankForm("", 3, "");
                },
            );
            break;
        case 4:
            majorSubShow.value = true;
            positionData.value = [];
            areaData.value = [];
            areaDataThree.value = [];
            data_plant_workshop({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                    }
                    searchPlantWorkshop("all", 1, "");
                },
            );
            data_entrepo({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        entrepoNoGrey.value = re1.data.data;
                    }
                    searchEntrepo("all", 2, "");
                },
            );
            data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        tankNoGrey.value = re1.data.data;
                    }
                    searchTankForm("all", 3, "");
                },
            );
            break;
        case 5:
            majorSubShow.value = false;

            searchRiskRegister("", 5);
            break;
        case 6:
            searchDangerChemicalProcess("", 6);
            break;
        case 8:
            searchDangerChemicalList("", 8);

            break;
        case 10:
            majorSubShow.value = false;
            searchNjEquipments("", 10);
            break;
        case 11:
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
            majorSubShow.value = false;

            data_plant_workshop({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        plantWorkshopNoGrey.value = re1.data.data;
                    }
                    searchPlantWorkshop("all", 1, "");
                },
            );
            data_entrepo({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        entrepoNoGrey.value = re1.data.data;
                    }
                    searchEntrepo("all", 2, "");
                },
            );
            data_tank_farm({ enterprise_name: enterpriseName.value }).then(
                (re1) => {
                    if (
                        re1.data.success &&
                        re1.data.data &&
                        re1.data.data.length
                    ) {
                        tankNoGrey.value = re1.data.data;
                    }
                    searchTankForm("all", 3, "");
                },
            );
            searchRiskRegister("all", 5);
            searchDangerChemicalProcess("all", 6);
            searchDangerChemicalList("all", 8);
            searchNjEquipments("all", 10);
            break;
        case 12:
            console.log(999, enterpriseName.value);
            proxy.$bus.emit("safetyUesMonitor", enterpriseName.value);
            break;
        default:
            break;
    }
};
//查找重点工艺
const searchDangerChemicalProcess = (val, type) => {
    //不是查找全部则置空
    if (!val) {
        positionData.value = [];
    }
    danger_chemical_process_list({
        enterprise_name: enterpriseName.value,
    }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data
            res.data.data.forEach((item) => {
                if (item.is_point == 1) {
                    if (item.longitude != null && item.latitude != null) {
                        positionData.value.push({
                            name: item.id,
                            craftName: item.craft_name,
                            type: type,
                            id: item.id,
                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                    }
                }
            });
            if (window.toggle == 2) {
                putIcons(positionData.value, highRiskProcessesImg);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);

                        openCesiumDialog(pick.id.type, pick.id._chuancan);

                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                positionData.value.forEach((item) => {
                    if (item.type == 6) {
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: "/static/poi/highRiskProcesses-new.svg", //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            highriskProcessesId.value = item.name;
                            highriskProcessesDialogShow.value = true;
                            proxy.$loading.show();
                            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                        });
                        const boxStyle = `
                width: 120px;
                height: 40px;
                border: 2px solid #47EBEB;
                text-align: center;
                background-color: rgba(0, 0, 0, 0.5);
                border-radius: 10px;
                line-height: 40px;
                `;
                        const sizeColor = `
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #FFFFFF;
                font-size: 14px;
                `;

                        // const box = `<div style="${boxStyle}"></div>`;
                        let infoWindow = new AMap.InfoWindow({
                            isCustom: true,
                            // 设置信息窗口的内容
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.craftName}</div>
                       </div>`,
                            // 设置信息窗口的偏移量
                            offset: new AMap.Pixel(0, -85),
                        });
                        // 添加鼠标移入事件监听器
                        marker.on("mouseover", (e) => {
                            infoWindow.open(
                                window.map1,
                                e.target.getPosition(),
                            );
                        });

                        // 添加鼠标移出事件监听器
                        marker.on("mouseout", (e) => {
                            infoWindow.close();
                        });
                        marker.setMap(window.map1);
                    }
                });

                window.map1.setFitView();
            }
        }
    });
};
//查找监管化学品
const searchDangerChemicalList = (val, type) => {
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    // positionData.value = [];
    //     areaData.value = [];
    //     areaDataThree.value = []
    danger_chemical_list({ enterprise_name: enterpriseName.value }).then(
        (res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                // positionData.value = res.data.data
                res.data.data.forEach((item) => {
                    if (item.judge == "1") {
                        if (
                            item.position != undefined &&
                            item.position != null
                        ) {
                            positionData.value.push({
                                name: item.id,
                                chemicalName: item.chemical_name,
                                id: item.id,
                                type: type,
                                longitude: Number(eval(item.position)[0].lng),
                                latitude: Number(eval(item.position)[0].lat),
                            });
                        }
                    } else {
                        if (item.position != null) {
                            let points = [];
                            let threeMiddle = [];
                            eval(item.position).forEach((x) => {
                                let middlePoints = [];
                                middlePoints.push(Number(x.lng) - 0.0062);
                                middlePoints.push(Number(x.lat) - 0.00085);
                                threeMiddle.push(Number(x.lng) - 0.0062);
                                threeMiddle.push(Number(x.lat) - 0.00085);
                                threeMiddle.push(30);
                                points.push(middlePoints);
                                //
                            });
                            let obj = {};
                            Reflect.set(obj, "id", item.id);
                            Reflect.set(obj, "type", type);
                            Reflect.set(
                                obj,
                                "chemicalName",
                                item.chemical_name,
                            );
                            Reflect.set(obj, "path", threeMiddle);
                            areaDataThree.value.push(obj);
                            areaData.value.push({
                                name: item.id,
                                id: item.source_name,
                                chemicalName: item.chemical_name,
                                type: type,
                                path: points,
                            });
                        }
                    }
                });

                if (window.toggle == 2) {
                    putIcons(positionData.value, hazardousChemicalsImg);
                    // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                    window.viewer.screenSpaceEventHandler.setInputAction(
                        function (e) {
                            var pick = viewer.scene.pick(e.position);
                            if (pick && pick.id) {
                                console.log(pick.id);

                                openCesiumDialog(
                                    pick.id.type,
                                    pick.id._chuancan,
                                );

                                window.viewer.camera.setView({
                                    destination: Cesium.Cartesian3.fromDegrees(
                                        pick.id._longitude,
                                        pick.id._latitude,
                                        800,
                                    ),
                                });
                            }
                        },
                        Cesium.ScreenSpaceEventType.LEFT_CLICK,
                    );
                    areaDataThree.value.forEach((item) => {
                        // if (item.type ==8){

                        const entity = window.viewer.entities.add({
                            //   id: item.id,
                            chuancan: item.id,
                            type: item.type,

                            name: item.id,
                            longitude: Number(item.path[0]),
                            latitude: Number(item.path[1]),
                            show: true,
                            polygon: {
                                hierarchy:
                                    Cesium.Cartesian3.fromDegreesArrayHeights(
                                        item.path,
                                    ), //参数为四个角点坐标
                                zIndex: zIndex,
                                height: zIndex, //多层次
                                outline: true,
                                outlineColor:
                                    Cesium.Color.fromCssColorString("#47EBEB"),
                                outlineWidth: 15.0,
                                material: Cesium.Color.fromCssColorString(
                                    "rgba(71, 235, 235, 0.30)",
                                ),
                            },
                        });
                        viewer.zoomTo(entity);
                        // }
                    });
                } else if (window.toggle == 3) {
                    positionData.value.forEach((item) => {
                        if (item.type == 8) {
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: "/static/poi/hazardousChemicals-new.svg", //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                hazardousChemicalsId.value = item.name;
                                hazardousChemicalsDialogShow.value = true;
                                proxy.$loading.show();
                                //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                            });
                            const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                            const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

                            // const box = `<div style="${boxStyle}"></div>`;
                            let infoWindow = new AMap.InfoWindow({
                                isCustom: true,
                                // 设置信息窗口的内容
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.chemicalName}</div>
                       </div>`,
                                // 设置信息窗口的偏移量
                                offset: new AMap.Pixel(0, -85),
                            });
                            // 添加鼠标移入事件监听器
                            marker.on("mouseover", (e) => {
                                infoWindow.open(
                                    window.map1,
                                    e.target.getPosition(),
                                );
                            });

                            // 添加鼠标移出事件监听器
                            marker.on("mouseout", (e) => {
                                infoWindow.close();
                            });
                            marker.setMap(window.map1);
                        }
                    });
                    areaData.value.forEach((item) => {
                        if (item.type == 8) {
                            let polygon = new AMap.Polygon({
                                path: item.path,
                                strokeColor: "#FF33FF",
                                strokeWeight: 6,
                                strokeOpacity: 0.2,
                                fillOpacity: 0.4,
                                fillColor: "#1791fc",
                                zIndex: 50,
                                bubble: true,
                            });
                            polygon.on("click", (e) => {
                                hazardousChemicalsId.value = item.name;
                                hazardousChemicalsDialogShow.value = true;
                                proxy.$loading.show();
                                //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                            });
                            window.map1.add([polygon]);
                        }
                    });
                    window.map1.setFitView();
                }
            }
        },
    );
};
//厂房车间
const searchPlantWorkshop = (val, type, uid) => {
    //传''则查单个 置空
    if (!val) {
        console.log("ceshi cesium");
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    console.log(positionData.value);
    mds_workshop_list({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            res.data.data.forEach((item) => {
                if (item.is_point == 1) {
                    positionData.value.push({
                        name: item.id,
                        type: type,
                        source_level: item.source_level,
                        is_alarm: item.is_alarm,
                        id: item.workshop_name,
                        isGrey:
                            plantWorkshopNoGrey.value.findIndex(
                                (v) => v.id == item.id,
                            ) == -1
                                ? true
                                : false,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });

            if (window.toggle == 2) {
                console.log(positionData.value);
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    selectLsit.forEach((item) => {
                        if (item.isGrey) {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                }

                putIcons(positionNoGreyData, workshopImg);
                putIcons(positionGreyData, workshopGreyImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);
                        console.log(e.position);
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                        openCesiumDialog(pick.id.type, pick.id._chuancan);
                        pickWorkshopkIsGrey.value = pick.id.isGrey;
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                console.log(window.toggle, "window.toggle");
                if (!uid) {
                    positionData.value.forEach((item) => {
                        PlantWorkshopMarker(item);
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        PlantWorkshopMarker(item);
                    });
                }

                window.map1.setFitView();
            }
        }
    });
};
//厂房车间打点
const PlantWorkshopMarker = (item) => {
    console.log("厂房车间打点", item);
    if (item.type == 1) {
        var imgUrl1 = "/static/poi/workshop-new.svg";
        if (item.source_level == "一级") {
            imgUrl1 = "/static/poi/workshop-one.svg";
        } else if (item.source_level == "二级") {
            imgUrl1 = "/static/poi/workshop-two.svg";
        } else if (item.source_level == "三级") {
            imgUrl1 = "/static/poi/workshop-three.svg";
        }
        if (item.isGrey) {
            imgUrl1 = "/static/poi/workshop-grey.svg";
        }
        let marker = new AMap.Marker({
            icon: new AMap.Icon({
                size: new AMap.Size(72, 92), // 图标尺寸
                image: imgUrl1, //绝对路径
                imageSize: new AMap.Size(72, 92),
            }),
            position: [item.longitude, item.latitude],
            offset: new AMap.Pixel(-36, -92),
        });
        if (item.is_alarm == "1") {
            marker.dom.classList.add("abnormal_marker");
        }
        marker.on("click", (e) => {
            pickWorkshopkId.value = item.name;
            workshopDialogShow.value = true;
            proxy.$loading.show();
            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
            console.log(pickWorkshopkId.value);
            pickWorkshopkIsGrey.value = item.isGrey;
        });
        const boxStyle = `
                  width: 120px;
                  height: 40px;
                  border: 2px solid #47EBEB;
                  text-align: center;
                  background-color: rgba(0, 0, 0, 0.5);
                      border-radius: 10px;
                      line-height: 40px;
                  `;
        const sizeColor = `
                  width: 100%;
                                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  color: #FFFFFF;
                      font-size: 14px;
                  `;

        // const box = `<div style="${boxStyle}"></div>`;
        let infoWindow = new AMap.InfoWindow({
            isCustom: true,
            // 设置信息窗口的内容
            content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.id}</div>
                         </div>`,
            // 设置信息窗口的偏移量
            offset: new AMap.Pixel(0, -85),
        });
        // 添加鼠标移入事件监听器
        marker.on("mouseover", (e) => {
            infoWindow.open(window.map1, e.target.getPosition());
        });

        // 添加鼠标移出事件监听器
        marker.on("mouseout", (e) => {
            infoWindow.close();
        });
        marker.setMap(window.map1);
    }
};
//查找仓库
const searchEntrepo = (val, type, uid) => {
    if (!val) {
        positionData.value = [];
    }
    mds_entrepo_list({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // positionData.value = res.data.data
            for (let i = 0; i < res.data.data.length; i++) {
                if (res.data.data[i].is_point == 1) {
                    if (
                        res.data.data[i].longitude != null &&
                        res.data.data[i].latitude != null
                    ) {
                        console.log(res.data.data[i].id);
                        console.log(
                            entrepoNoGrey.value.findIndex(
                                (v) => v.id == res.data.data[i].id,
                            ),
                        );
                        positionData.value.push({
                            name: res.data.data[i].id,
                            lablename: res.data.data[i].entrepo_name,
                            type: type,
                            id: res.data.data[i].workshop_name,
                            isGrey:
                                entrepoNoGrey.value.findIndex(
                                    (v) => v.id == res.data.data[i].id,
                                ) == -1
                                    ? true
                                    : false,
                            longitude: Number(res.data.data[i].longitude),
                            latitude: Number(res.data.data[i].latitude),
                        });
                    }
                }
            }
            nextTick(() => {
                if (window.toggle == 2) {
                    //不置灰数组
                    let positionNoGreyData = [];
                    let positionGreyData = [];
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    } else {
                        let selectLsit = resetData(
                            positionData.value,
                            "name",
                            uid,
                        );
                        selectLsit.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    }

                    putIcons(positionNoGreyData, warehouseImg);
                    putIcons(positionGreyData, warehouseGreyImg);
                    // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                    window.viewer.screenSpaceEventHandler.setInputAction(
                        function (e) {
                            var pick = viewer.scene.pick(e.position);
                            if (pick && pick.id) {
                                console.log(pick.id);
                                openCesiumDialog(
                                    pick.id.type,
                                    pick.id._chuancan,
                                );

                                window.viewer.camera.setView({
                                    destination: Cesium.Cartesian3.fromDegrees(
                                        pick.id._longitude,
                                        pick.id._latitude,
                                        800,
                                    ),
                                });
                            }
                        },
                        Cesium.ScreenSpaceEventType.LEFT_CLICK,
                    );
                } else if (window.toggle == 3) {
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            entrepoMarker(item);
                        });
                    } else {
                        let selectLsit = resetData(
                            positionData.value,
                            "id",
                            uid,
                        );
                        selectLsit.forEach((item) => {
                            entrepoMarker(item);
                        });
                    }

                    window.map1.setFitView();
                }
            });
        }
    });
};
//仓库打点
const entrepoMarker = (item) => {
    if (item.type == 2) {
        var imgUrl1 = "/static/poi/warehouse-new.svg";
        if (item.source_level == "一级") {
            imgUrl1 = "/static/poi/warehouse-one.svg";
        } else if (item.source_level == "二级") {
            imgUrl1 = "/static/poi/warehouse-two.svg";
        } else if (item.source_level == "三级") {
            imgUrl1 = "/static/poi/warehouse-three.svg";
        }
        if (item.isGrey) {
            imgUrl1 = "/static/poi/warehouse-grey.svg";
        }
        let marker = new AMap.Marker({
            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            icon: new AMap.Icon({
                size: new AMap.Size(72, 92), // 图标尺寸
                image: imgUrl1, //绝对路径
                imageSize: new AMap.Size(72, 92),
            }),
            position: [item.longitude, item.latitude],
            offset: new AMap.Pixel(-36, -92),
        });
        marker.on("click", (e) => {
            warehouseId.value = item.name;
            console.log("ceshihhahahhahahahha", item.isGrey);
            warehouseIsGrey.value = item.isGrey;
            warehouseDialogShow.value = true;
            proxy.$loading.show();
            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
        });
        const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;
  `;
        const sizeColor = `
                   width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 14px;
  `;

        // const box = `<div style="${boxStyle}"></div>`;
        let infoWindow = new AMap.InfoWindow({
            isCustom: true,
            // 设置信息窗口的内容
            content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.lablename}</div>
                         </div>`,
            // 设置信息窗口的偏移量
            offset: new AMap.Pixel(0, -85),
        });
        // 添加鼠标移入事件监听器
        marker.on("mouseover", (e) => {
            infoWindow.open(window.map1, e.target.getPosition());
        });

        // 添加鼠标移出事件监听器
        marker.on("mouseout", (e) => {
            infoWindow.close();
        });
        marker.setMap(window.map1);
    }
};
//查找罐区
const searchTankForm = (val, type, uid) => {
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    // positionData.value = [];
    //   areaData.value = [];
    //   areaDataThree.value = []
    mds_tank_farm_list({ enterprise_name: enterpriseName.value }).then(
        (res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                // positionData.value = res.data.data
                res.data.data.forEach((item) => {
                    if (item.is_point == 1) {
                        positionData.value.push({
                            name: item.id,
                            type: type,
                            source_level: item.source_level,
                            is_alarm: item.is_alarm,
                            id: item.tank_farm_name,
                            isGrey:
                                tankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,

                            longitude: Number(item.longitude),
                            latitude: Number(item.latitude),
                        });
                    } else {
                        if (item.points != null) {
                            let threeMiddle = [];
                            eval(item.points).forEach((innerItem) => {
                                innerItem.forEach((inner, index) => {
                                    if (index == 0) {
                                        threeMiddle.push(
                                            Number(inner) - 0.0062,
                                        );
                                    } else {
                                        threeMiddle.push(
                                            Number(inner) - 0.00085,
                                        );
                                    }
                                });
                                threeMiddle.push(30);
                            });
                            let obj = {};
                            Reflect.set(obj, "id", item.id);
                            Reflect.set(obj, "type", type);
                            Reflect.set(
                                obj,
                                "isGrey",
                                tankNoGrey.value.findIndex(
                                    (v) => v.id == item.id,
                                ) == -1
                                    ? true
                                    : false,
                            );
                            Reflect.set(obj, "path", threeMiddle);
                            areaDataThree.value.push(obj);
                            areaData.value.push({
                                name: item.id,
                                type: type,
                                source_level: item.source_level,
                                is_alarm: item.is_alarm,
                                isGrey:
                                    tankNoGrey.value.findIndex(
                                        (v) => v.id == item.id,
                                    ) == -1
                                        ? true
                                        : false,
                                id: item.tank_farm_name,
                                path: item.points,
                            });
                        }
                    }
                });
                if (window.toggle == 2) {
                    //不置灰数组
                    let positionNoGreyData = [];
                    let positionGreyData = [];

                    if (!uid) {
                        positionData.value.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    } else {
                        selectLsit = resetData(positionData.value, "id", uid);
                        if (selectLsit.length) {
                            selectLsit.forEach((item) => {
                                if (item.isGrey) {
                                    positionGreyData.push(item);
                                } else {
                                    positionNoGreyData.push(item);
                                }
                            });
                        } else {
                            positionGreyData = [];
                            positionNoGreyData = [];
                        }
                    }
                    putIcons(positionNoGreyData, tankNoGreyImg);
                    putIcons(positionGreyData, tankGreyImg);
                    // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                    window.viewer.screenSpaceEventHandler.setInputAction(
                        function (e) {
                            var pick = viewer.scene.pick(e.position);
                            if (pick && pick.id) {
                                console.log(pick.id);

                                openCesiumDialog(
                                    pick.id.type,
                                    pick.id._chuancan,
                                );

                                tankIsGrey.value = pick.id.isGrey;
                                window.viewer.camera.setView({
                                    destination: Cesium.Cartesian3.fromDegrees(
                                        pick.id._longitude,
                                        pick.id._latitude,
                                        800,
                                    ),
                                });
                            }
                        },
                        Cesium.ScreenSpaceEventType.LEFT_CLICK,
                    );
                    if (!uid) {
                        areaDataThree.value.forEach((item) => {
                            // if (item.type ==3){
                            let polygonColor = "#737B8C";
                            if (!item.isGrey) {
                                polygonColor = "rgba(71, 235, 235, 0.30)";
                            }
                            const entity = window.viewer.entities.add({
                                // id: item.id,
                                chuancan: item.id,
                                type: item.type,
                                isGrey: item.isGrey,
                                name: item.id,
                                longitude: Number(item.path[0]),
                                latitude: Number(item.path[1]),
                                show: true,
                                polygon: {
                                    hierarchy:
                                        Cesium.Cartesian3.fromDegreesArrayHeights(
                                            item.path,
                                        ), //参数为四个角点坐标
                                    zIndex: zIndex,
                                    height: zIndex, //多层次
                                    outline: true,
                                    outlineColor:
                                        Cesium.Color.fromCssColorString(
                                            "#47EBEB",
                                        ),
                                    outlineWidth: 15.0,
                                    material:
                                        Cesium.Color.fromCssColorString(
                                            polygonColor,
                                        ),
                                },
                            });
                            //    viewer.zoomTo(entity);
                            // }
                        });
                    } else {
                        selectLsitOne = resetData(
                            areaDataThree.value,
                            "id",
                            uid,
                        );
                        selectLsitOne.forEach((item) => {
                            // if (item.type ==3){
                            let polygonColor = "#737B8C";
                            if (!item.isGrey) {
                                polygonColor = "rgba(71, 235, 235, 0.30)";
                            }
                            const entity = window.viewer.entities.add({
                                // id: item.id,
                                chuancan: item.id,
                                type: item.type,
                                isGrey: item.isGrey,
                                name: item.id,
                                longitude: Number(item.path[0]),
                                latitude: Number(item.path[1]),
                                show: true,
                                polygon: {
                                    hierarchy:
                                        Cesium.Cartesian3.fromDegreesArrayHeights(
                                            item.path,
                                        ), //参数为四个角点坐标
                                    zIndex: zIndex,
                                    height: zIndex, //多层次
                                    outline: true,
                                    outlineColor:
                                        Cesium.Color.fromCssColorString(
                                            "#47EBEB",
                                        ),
                                    outlineWidth: 15.0,
                                    material:
                                        Cesium.Color.fromCssColorString(
                                            polygonColor,
                                        ),
                                },
                            });
                            //    viewer.zoomTo(entity);
                            // }
                        });
                    }
                } else if (window.toggle == 3) {
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            if (item.type == 3) {
                                var imgUrl1 = "/static/poi/tank-new.svg";
                                if (item.source_level == "一级") {
                                    imgUrl1 = "/static/poi/tank-one.svg";
                                } else if (item.source_level == "二级") {
                                    imgUrl1 = "/static/poi/tank-two.svg";
                                } else if (item.source_level == "三级") {
                                    imgUrl1 = "/static/poi/tank-three.svg";
                                }
                                if (item.isGrey) {
                                    imgUrl1 = "/static/poi/tank-grey.svg";
                                }
                                let marker = new AMap.Marker({
                                    // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                    icon: new AMap.Icon({
                                        size: new AMap.Size(72, 92), // 图标尺寸
                                        image: imgUrl1, //绝对路径
                                        imageSize: new AMap.Size(72, 92),
                                    }),
                                    position: [item.longitude, item.latitude],
                                    offset: new AMap.Pixel(-36, -92),
                                });
                                if (item.is_alarm == "1") {
                                    marker.dom.classList.add("abnormal_marker");
                                }
                                marker.on("click", (e) => {
                                    tankId.value = item.name;
                                    tankDialogShow.value = true;
                                    proxy.$loading.show();
                                    tankIsGrey.value = item.isGrey;
                                    //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                                });
                                const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;
  `;
                                const sizeColor = `
                   width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 16px;
  `;

                                // const box = `<div style="${boxStyle}"></div>`;
                                let infoWindow = new AMap.InfoWindow({
                                    isCustom: true,
                                    // 设置信息窗口的内容
                                    content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.id}</div>
                         </div>`,
                                    // 设置信息窗口的偏移量
                                    offset: new AMap.Pixel(0, -85),
                                });
                                // 添加鼠标移入事件监听器
                                marker.on("mouseover", (e) => {
                                    infoWindow.open(
                                        window.map1,
                                        e.target.getPosition(),
                                    );
                                });

                                // 添加鼠标移出事件监听器
                                marker.on("mouseout", (e) => {
                                    infoWindow.close();
                                });
                                marker.setMap(window.map1);
                            }
                        });
                        areaData.value.forEach((item) => {
                            if (item.type == 3) {
                                let polygonColor = "#1791fc";
                                if (item.source_level == "一级") {
                                    polygonColor = "#F44725";
                                } else if (item.source_level == "二级") {
                                    polygonColor = "#F47B25";
                                } else if (item.source_level == "三级") {
                                    polygonColor = "#F4C025";
                                } else {
                                    polygonColor = "#30ABE8";
                                }
                                if (item.isGrey) {
                                    polygonColor = "#737B8C";
                                }
                                if (item.is_alarm == "1") {
                                    polygonColor = "#FF0000";
                                }
                                let polygon = new AMap.Polygon({
                                    path: eval(item.path),
                                    strokeColor: polygonColor,
                                    strokeWeight: 6,
                                    strokeOpacity: 0.2,
                                    fillOpacity: 0.4,
                                    fillColor: polygonColor,
                                    zIndex: 50,
                                    bubble: true,
                                });
                                console.log(polygon);
                                // polygon.dom.classList.add('abnormal_marker')
                                polygon.on("click", (e) => {
                                    tankId.value = item.name;
                                    tankDialogShow.value = true;
                                    proxy.$loading.show();
                                    tankIsGrey.value = item.isGrey;
                                    //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                                });
                                window.map1.add([polygon]);
                            }
                        });
                    } else {
                        let selectLsit = resetData(areaData.value, "name", uid);
                        let selectLsitOne = resetData(
                            positionData.value,
                            "name",
                            uid,
                        );
                        // console.log(selectLsit,"selectLsit333",changeData.value)
                        selectLsitOne.forEach((item) => {
                            if (item.type == 3) {
                                var imgUrl1 = "/static/poi/tank-new.svg";
                                if (item.source_level == "一级") {
                                    imgUrl1 = "/static/poi/tank-one.svg";
                                } else if (item.source_level == "二级") {
                                    imgUrl1 = "/static/poi/tank-two.svg";
                                } else if (item.source_level == "三级") {
                                    imgUrl1 = "/static/poi/tank-three.svg";
                                }
                                if (item.isGrey) {
                                    imgUrl1 = "/static/poi/tank-grey.svg";
                                }
                                let marker = new AMap.Marker({
                                    // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                    icon: new AMap.Icon({
                                        size: new AMap.Size(72, 92), // 图标尺寸
                                        image: imgUrl1, //绝对路径
                                        imageSize: new AMap.Size(72, 92),
                                    }),
                                    position: [item.longitude, item.latitude],
                                    offset: new AMap.Pixel(-36, -92),
                                });
                                if (item.is_alarm == "1") {
                                    marker.dom.classList.add("abnormal_marker");
                                }
                                marker.on("click", (e) => {
                                    tankId.value = item.name;
                                    tankDialogShow.value = true;
                                    proxy.$loading.show();
                                    tankIsGrey.value = item.isGrey;
                                    //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                                });
                                const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;
  `;
                                const sizeColor = `
                   width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 16px;
  `;

                                // const box = `<div style="${boxStyle}"></div>`;
                                let infoWindow = new AMap.InfoWindow({
                                    isCustom: true,
                                    // 设置信息窗口的内容
                                    content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.id}</div>
                         </div>`,
                                    // 设置信息窗口的偏移量
                                    offset: new AMap.Pixel(0, -85),
                                });
                                // 添加鼠标移入事件监听器
                                marker.on("mouseover", (e) => {
                                    infoWindow.open(
                                        window.map1,
                                        e.target.getPosition(),
                                    );
                                });

                                // 添加鼠标移出事件监听器
                                marker.on("mouseout", (e) => {
                                    infoWindow.close();
                                });
                                marker.setMap(window.map1);
                            }
                        });
                        selectLsit.forEach((item) => {
                            if (item.type == 3) {
                                let polygonColor = "#1791fc";
                                if (item.source_level == "一级") {
                                    polygonColor = "#F44725";
                                } else if (item.source_level == "二级") {
                                    polygonColor = "#F47B25";
                                } else if (item.source_level == "三级") {
                                    polygonColor = "#F4C025";
                                } else {
                                    polygonColor = "#30ABE8";
                                }
                                if (item.isGrey) {
                                    polygonColor = "#737B8C";
                                }
                                if (item.is_alarm == "1") {
                                    polygonColor = "#FF0000";
                                }
                                let polygon = new AMap.Polygon({
                                    path: eval(item.path),
                                    strokeColor: polygonColor,
                                    strokeWeight: 6,
                                    strokeOpacity: 0.2,
                                    fillOpacity: 0.4,
                                    fillColor: polygonColor,
                                    zIndex: 50,
                                    bubble: true,
                                });
                                console.log(polygon);
                                // polygon.dom.classList.add('abnormal_marker')
                                polygon.on("click", (e) => {
                                    tankId.value = item.name;
                                    tankDialogShow.value = true;
                                    proxy.$loading.show();
                                    tankIsGrey.value = item.isGrey;
                                    //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                                });
                                window.map1.add([polygon]);
                            }
                        });
                    }
                    window.map1.setFitView();
                }
                if (!uid) {
                    data_storage_tank({
                        enterprise_name: enterpriseName.value,
                    }).then((re1) => {
                        if (
                            re1.data.success &&
                            re1.data.data &&
                            re1.data.data.length
                        ) {
                            storageTankNoGrey.value = re1.data.data;
                        }
                        searchStorageTank("all", 9, "");
                    });
                }
            }
        },
    );
};
//查找储罐
const searchStorageTank = (val, type, uid) => {
    if (!val) {
        positionData.value = [];
    }
    mds_storage_tank_list({ enterprise_name: enterpriseName.value }).then(
        (res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                res.data.data.forEach((item) => {
                    if (item.is_point == 1) {
                        if (item.longitude != null && item.latitude != null) {
                            positionData.value.push({
                                name: item.id,
                                storageName: item.storage_name,
                                id: item.id,
                                type: type,
                                is_alarm: item.is_alarm,
                                source_level: item.source_level,

                                alarm_time: item.alarm_time,
                                isGrey:
                                    storageTankNoGrey.value.findIndex(
                                        (v) => v.id == item.id,
                                    ) == -1
                                        ? true
                                        : false,
                                longitude: Number(item.longitude),
                                latitude: Number(item.latitude),
                            });
                        }
                    }
                });
                if (window.toggle == 2) {
                    //不置灰数组
                    let positionNoGreyData = [];
                    let positionGreyData = [];
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    } else {
                        let selectLsit = resetData(
                            positionData.value,
                            "id",
                            uid,
                        );
                        selectLsit.forEach((item) => {
                            if (item.isGrey) {
                                positionGreyData.push(item);
                            } else {
                                positionNoGreyData.push(item);
                            }
                        });
                    }

                    putIcons(positionNoGreyData, storageNoGreyTankImg);
                    putIcons(positionGreyData, storageGreyTankImg);
                    // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                    window.viewer.screenSpaceEventHandler.setInputAction(
                        function (e) {
                            var pick = viewer.scene.pick(e.position);
                            if (pick && pick.id) {
                                console.log(pick.id);

                                openCesiumDialog(
                                    pick.id.type,
                                    pick.id._chuancan,
                                );

                                monitoringPointskIsGrey.value = pick.id.isGrey;
                                window.viewer.camera.setView({
                                    destination: Cesium.Cartesian3.fromDegrees(
                                        pick.id._longitude,
                                        pick.id._latitude,
                                        800,
                                    ),
                                });
                            }
                        },
                        Cesium.ScreenSpaceEventType.LEFT_CLICK,
                    );
                } else if (window.toggle == 3) {
                    if (!uid) {
                        positionData.value.forEach((item) => {
                            storageTankMarker(item);
                        });
                    } else {
                        let selectLsit = resetData(
                            positionData.value,
                            "id",
                            uid,
                        );
                        selectLsit.forEach((item) => {
                            storageTankMarker(item);
                        });
                    }

                    window.map1.setFitView();
                }
            }
        },
    );
};
//储罐打点
const storageTankMarker = (item) => {
    if (item.type == 9) {
        var imgUrl1 = "/static/poi/storageTank-new.svg";
        if (item.source_level == "一级") {
            imgUrl1 = "/static/poi/storageTank-one.svg";
        } else if (item.source_level == "二级") {
            imgUrl1 = "/static/poi/storageTank-two.svg";
        } else if (item.source_level == "三级") {
            imgUrl1 = "/static/poi/storageTank-three.svg";
        }
        if (item.isGrey) {
            imgUrl1 = "/static/poi/storageTank-grey.svg";
        }
        let marker = new AMap.Marker({
            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            icon: new AMap.Icon({
                size: new AMap.Size(72, 92), // 图标尺寸
                image: imgUrl1, //绝对路径
                imageSize: new AMap.Size(72, 92),
            }),
            // 创建标记点的div

            position: [item.longitude, item.latitude],
            offset: new AMap.Pixel(-36, -92),
        });

        console.log(marker);
        // if(item.alarm_time == '1' &&)
        if (item.is_alarm == "1") {
            marker.dom.classList.add("abnormal_marker");
        }
        console.log(marker);
        //     var markerDiv = document.createElement('div')
        //   // 设置标记点className,用于设置点的样式（动画）
        //         markerDiv.className = 'alarmDevice'
        //         marker.setContent(markerDiv)
        //         console.log(marker);
        marker.on("click", (e) => {
            monitoringPointskId.value = item.name;
            monitoringPointskIsGrey.value = item.isGrey;
            monitoringPointsDialogShow.value = true;
            proxy.$loading.show();
            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
        });
        const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
        const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;

        // const box = `<div style="${boxStyle}"></div>`;
        let infoWindow = new AMap.InfoWindow({
            isCustom: true,
            // 设置信息窗口的内容
            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.storageName}</div>
                       </div>`,
            // 设置信息窗口的偏移量
            offset: new AMap.Pixel(0, -85),
        });
        // 添加鼠标移入事件监听器
        marker.on("mouseover", (e) => {
            infoWindow.open(window.map1, e.target.getPosition());
        });

        // 添加鼠标移出事件监听器
        marker.on("mouseout", (e) => {
            infoWindow.close();
        });
        marker.setMap(window.map1);
    }
};
//查找风险点
const searchRiskRegister = (val, type) => {
    // positionData.value = [];
    //   areaData.value = [];
    //   areaDataThree.value = []
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }

    risk_register({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            totalData.value = res.data.data;
            res.data.data.forEach((item) => {
                if (item.judge == "1") {
                    positionData.value.push({
                        name: item.id,
                        type: type,
                        id: item.risk_name,
                        longitude: Number(eval(item.position)[0].lng),
                        latitude: Number(eval(item.position)[0].lat),
                        risk_level: item.risk_level,
                    });
                } else {
                    if (item.position != null) {
                        let points = [];
                        let threeMiddle = [];
                        eval(item.position).forEach((x) => {
                            let middlePoints = [];
                            middlePoints.push(Number(x.lng) - 0.0062);
                            middlePoints.push(Number(x.lat) - 0.00085);
                            threeMiddle.push(Number(x.lng) - 0.0062);
                            threeMiddle.push(Number(x.lat) - 0.00085);
                            threeMiddle.push(30);
                            points.push(middlePoints);
                            //
                        });
                        let obj = {};
                        Reflect.set(obj, "name", item.id);
                        Reflect.set(obj, "id", item.risk_name);
                        Reflect.set(obj, "type", type);
                        Reflect.set(obj, "path", threeMiddle);
                        areaDataThree.value.push(obj);
                        areaData.value.push({
                            name: item.id,
                            id: item.risk_name,
                            type: type,
                            path: points,
                        });
                        // console.log(eval(item.position));
                        // //处理数据-解析区域 - 地图划区域
                        // let middleData = [];
                        // eval(item.position).forEach((item) => {
                        //   let middle = [];
                        //   middle.push(item.lng);
                        //   middle.push(item.lat);
                        //   console.log(middle);
                        //   middleData.push(middle);
                        // });
                        // let obj = {};
                        // obj.path = middleData;
                        // obj.name = item.id;
                        // // console.log(middleData);
                        // areaData.value.push(obj);
                    }
                }
            });
            if (window.toggle == 2) {
                let positionLevel1 = [];
                let positionLevel2 = [];
                let positionLevel3 = [];
                let positionLevel4 = [];
                positionData.value.forEach((item) => {
                    if (item.risk_level == "重大风险") {
                        positionLevel1.push(item);
                    } else if (item.risk_level == "较大风险") {
                        positionLevel2.push(item);
                    } else if (item.risk_level == "一般风险") {
                        positionLevel3.push(item);
                    } else {
                        positionLevel4.push(item);
                    }
                });
                putIcons(positionLevel1, riskPointsImg1);
                putIcons(positionLevel2, riskPointsImg2);
                putIcons(positionLevel3, riskPointsImg3);
                putIcons(positionLevel4, riskPointsImg4);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);
                        riskPointsName.value = pick.id.name;
                        openCesiumDialog(pick.id.type, pick.id._chuancan);

                        // console.log(totalData.value);
                        // console.log(
                        //   totalData.value.find((x) => x.id == riskPointsId.value)
                        //     .risk_name
                        // );
                        riskPointsDialogShow.value = true;
                        proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
                areaDataThree.value.forEach((item) => {
                    let polygonColor;
                    if (item.risk_level == "重大风险") {
                        polygonColor = "#F44725";
                    } else if (item.risk_level == "较大风险") {
                        polygonColor = "#F47B25";
                    } else if (item.risk_level == "一般风险") {
                        polygonColor = "#F4C025";
                    } else {
                        polygonColor = "#30ABE8";
                    }
                    // if (item.type == 5){
                    const entity = window.viewer.entities.add({
                        // id: item.name,
                        chuancan: item.name,
                        type: item.type,

                        name: item.id,
                        longitude: Number(item.path[0]),
                        latitude: Number(item.path[1]),
                        show: true,
                        polygon: {
                            hierarchy:
                                Cesium.Cartesian3.fromDegreesArrayHeights(
                                    item.path,
                                ), //参数为四个角点坐标
                            zIndex: zIndex,
                            height: zIndex, //多层次
                            outline: true,
                            outlineColor:
                                Cesium.Color.fromCssColorString("#47EBEB"),
                            outlineWidth: 15.0,
                            material:
                                Cesium.Color.fromCssColorString(polygonColor),
                        },
                    });
                    viewer.zoomTo(entity);
                    // }
                });
            } else if (window.toggle == 3) {
                positionData.value.forEach((item) => {
                    let imgIcon;

                    if (item.risk_level == "重大风险") {
                        imgIcon = "/static/poi/riskPoints1.svg";
                    } else if (item.risk_level == "较大风险") {
                        imgIcon = "/static/poi/riskPoints2.svg";
                    } else if (item.risk_level == "一般风险") {
                        imgIcon = "/static/poi/riskPoints3.svg";
                    } else {
                        imgIcon = "/static/poi/riskPoints4.svg";
                    }
                    if (item.type == 5) {
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgIcon, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            riskPointsId.value = item.name;
                            riskPointsName.value = totalData.value.find(
                                (x) => x.id == riskPointsId.value,
                            ).risk_name;

                            riskPointsDialogShow.value = true;
                            proxy.$loading.show();
                            // window.map1.setZoomAndCenter(22, e.target._opts.position)
                        });
                        const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;
  `;
                        const sizeColor = `
                     width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 14px;
  `;

                        // const box = `<div style="${boxStyle}"></div>`;
                        let infoWindow = new AMap.InfoWindow({
                            isCustom: true,
                            // 设置信息窗口的内容
                            content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.id}</div>
                         </div>`,
                            // 设置信息窗口的偏移量
                            offset: new AMap.Pixel(0, -85),
                        });
                        // 添加鼠标移入事件监听器
                        marker.on("mouseover", (e) => {
                            infoWindow.open(
                                window.map1,
                                e.target.getPosition(),
                            );
                        });

                        // 添加鼠标移出事件监听器
                        marker.on("mouseout", (e) => {
                            infoWindow.close();
                        });
                        marker.setMap(window.map1);
                    }
                });

                areaData.value.forEach((item) => {
                    let polygonColor;
                    if (item.risk_level == "重大风险") {
                        polygonColor = "#F44725";
                    } else if (item.risk_level == "较大风险") {
                        polygonColor = "#F47B25";
                    } else if (item.risk_level == "一般风险") {
                        polygonColor = "#F4C025";
                    } else {
                        polygonColor = "#30ABE8";
                    }
                    if (item.type == 5) {
                        let polygon = new AMap.Polygon({
                            path: item.path,
                            strokeColor: polygonColor,
                            strokeWeight: 6,
                            strokeOpacity: 0.2,
                            fillOpacity: 0.4,
                            fillColor: polygonColor,
                            zIndex: 50,
                            bubble: true,
                        });
                        polygon.on("click", (e) => {
                            riskPointsId.value = item.name;
                            riskPointsName.value = totalData.value.find(
                                (x) => x.id == riskPointsId.value,
                            ).risk_name;

                            riskPointsDialogShow.value = true;
                            proxy.$loading.show();
                            //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                        });
                        window.map1.add([polygon]);
                    }
                });
                window.map1.setFitView();
            }
        }
    });
};
const searchMajorDangerSource = (val, type) => {
    // positionData.value = [];
    //     areaData.value = [];
    //   areaDataThree.value = [];
    if (!val) {
        positionData.value = [];
        areaData.value = [];
        areaDataThree.value = [];
    }
    major_danger_source_info_all({
        enterprise_name: enterpriseName.value,
    }).then((res) => {
        //   positionData.value = [];
        // positionData.value = res.data.data
        res.data.data.forEach((item) => {
            if (item.judge == 1) {
                positionData.value.push({
                    name: item.id,
                    id: item.source_name,
                    type: type,
                    isGrey:
                        majorDangerSourceNoGrey.value.findIndex(
                            (v) => v.id == item.id,
                        ) == -1
                            ? true
                            : false,

                    longitude: Number(eval(item.position)[0].lng),
                    latitude: Number(eval(item.position)[0].lat),
                    source_level: item.source_level,
                    is_alarm: item.is_alarm,
                });
            } else {
                let points = [];
                let threeMiddle = [];
                eval(item.position).forEach((x) => {
                    let middlePoints = [];
                    middlePoints.push(Number(x.lng) - 0.0062);
                    middlePoints.push(Number(x.lat) - 0.00085);
                    threeMiddle.push(Number(x.lng) - 0.0062);
                    threeMiddle.push(Number(x.lat) - 0.00085);
                    threeMiddle.push(30);
                    points.push(middlePoints);
                    //
                });
                let obj = {};
                Reflect.set(obj, "id", item.id);
                Reflect.set(obj, "type", type);
                Reflect.set(
                    obj,
                    "isGrey",
                    majorDangerSourceNoGrey.value.findIndex(
                        (v) => v.id == item.id,
                    ) == -1
                        ? true
                        : false,
                );
                Reflect.set(obj, "path", threeMiddle);
                areaDataThree.value.push(obj);
                areaData.value.push({
                    name: item.id,
                    id: item.source_name,
                    type: type,
                    isGrey:
                        majorDangerSourceNoGrey.value.findIndex(
                            (v) => v.id == item.id,
                        ) == -1
                            ? true
                            : false,
                    path: points,
                });
            }
        });
        if (window.toggle == 2) {
            let positionLevel1 = [];
            let positionLevel2 = [];
            let positionLevel3 = [];
            let positionLevel4 = [];
            let positionGrey = [];
            positionData.value.forEach((item) => {
                if (item.isGrey) {
                    positionGrey.push(item);
                } else {
                    if (item.source_level == "一级") {
                        positionLevel1.push(item);
                    } else if (item.source_level == "二级") {
                        positionLevel2.push(item);
                    } else if (item.source_level == "三级") {
                        positionLevel3.push(item);
                    } else {
                        positionLevel4.push(item);
                    }
                }
            });
            putIcons(positionLevel1, majorHazardImg1);
            putIcons(positionLevel2, majorHazardImg2);
            putIcons(positionLevel3, majorHazardImg3);
            putIcons(positionLevel4, majorHazardImg4);
            putIcons(positionGrey, majorHazardImgGrey);
            // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
            window.viewer.screenSpaceEventHandler.setInputAction(function (e) {
                var pick = viewer.scene.pick(e.position);
                if (pick && pick.id) {
                    console.log(pick.id);

                    openCesiumDialog(pick.id.type, pick.id._chuancan);

                    window.viewer.camera.setView({
                        destination: Cesium.Cartesian3.fromDegrees(
                            pick.id._longitude,
                            pick.id._latitude,
                            800,
                        ),
                    });
                }
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

            areaDataThree.value.forEach((item) => {
                let polygonColor;

                if (item.source_level == "一级") {
                    polygonColor = "#F44725";
                } else if (item.source_level == "二级") {
                    polygonColor = "#F47B25";
                } else if (item.source_level == "三级") {
                    polygonColor = "#F4C025";
                } else {
                    polygonColor = "#30ABE8";
                }
                if (item.isGrey) {
                    polygonColor = "#737B8C";
                }
                if (item.type == 4) {
                    const entity = window.viewer.entities.add({
                        //   id: item.id,
                        chuancan: item.id,
                        type: item.type,

                        name: item.id,
                        longitude: Number(item.path[0]),
                        latitude: Number(item.path[1]),
                        show: true,
                        polygon: {
                            hierarchy:
                                Cesium.Cartesian3.fromDegreesArrayHeights(
                                    item.path,
                                ), //参数为四个角点坐标
                            zIndex: zIndex,
                            height: zIndex, //多层次
                            outline: true,
                            outlineColor:
                                Cesium.Color.fromCssColorString("#47EBEB"),
                            outlineWidth: 15.0,
                            material:
                                Cesium.Color.fromCssColorString(polygonColor),
                        },
                    });
                    viewer.zoomTo(entity);
                }
            });
        } else if (window.toggle == 3) {
            positionData.value.forEach((item) => {
                let imgIcon;

                if (item.source_level == "一级") {
                    imgIcon = "/static/poi/majorHazard1.svg";
                } else if (item.source_level == "二级") {
                    imgIcon = "/static/poi/majorHazard2.svg";
                } else if (item.source_level == "三级") {
                    imgIcon = "/static/poi/majorHazard3.svg";
                } else {
                    imgIcon = "/static/poi/majorHazard4.svg";
                }
                if (item.isGrey) {
                    imgIcon = "/static/poi/majorHazard-grey.svg";
                }
                if (item.type == 4) {
                    let marker = new AMap.Marker({
                        //   icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",

                        icon: new AMap.Icon({
                            size: new AMap.Size(72, 92), // 图标尺寸
                            image: imgIcon, //绝对路径
                            imageSize: new AMap.Size(72, 92),
                        }),
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-36, -92),
                    });
                    if (item.is_alarm == "1") {
                        marker.dom.classList.add("abnormal_marker");
                    }
                    marker.on("click", (e) => {
                        pickMajorId.value = item.name;
                        majorHazardDialogShow.value = true;
                        //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                    });
                    const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;
  `;
                    const sizeColor = `
                   width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 14px;
  `;

                    // const box = `<div style="${boxStyle}"></div>`;
                    let infoWindow = new AMap.InfoWindow({
                        isCustom: true,
                        // 设置信息窗口的内容
                        content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.id}</div>
                         </div>`,
                        // 设置信息窗口的偏移量
                        offset: new AMap.Pixel(0, -85),
                    });
                    // 添加鼠标移入事件监听器
                    marker.on("mouseover", (e) => {
                        infoWindow.open(window.map1, e.target.getPosition());
                    });

                    // 添加鼠标移出事件监听器
                    marker.on("mouseout", (e) => {
                        infoWindow.close();
                    });
                    marker.setMap(window.map1);
                }
            });
            window.map1.setFitView();
            areaData.value.forEach((item) => {
                let polygonColor;
                if (item.source_level == "一级") {
                    polygonColor = "#F44725";
                } else if (item.source_level == "二级") {
                    polygonColor = "#F47B25";
                } else if (item.source_level == "三级") {
                    polygonColor = "#F4C025";
                } else {
                    polygonColor = "#30ABE8";
                }
                if (item.isGrey) {
                    polygonColor = "#737B8C";
                }
                if (item.is_alarm == "1") {
                    polygonColor = "#FF0000";
                }
                if (item.type == 4) {
                    let polygon = new AMap.Polygon({
                        path: eval(item.path),
                        strokeColor: polygonColor,
                        strokeWeight: 6,
                        strokeOpacity: 0.2,
                        fillOpacity: 0.4,
                        fillColor: polygonColor,
                        zIndex: 50,
                        bubble: true,
                    });
                    polygon.on("click", (e) => {
                        pickMajorId.value = item.name;
                        majorHazardDialogShow.value = true;
                        //   window.map1.setZoomAndCenter(18, e.target._opts.path[0])
                    });
                    window.map1.add([polygon]);
                }
            });
            window.map1.setFitView();
        }
    });
};
//查找摄像头
const searchNjEquipments = (val, type) => {
    if (!val) {
        positionData.value = [];
    }
    nj_equipments({ enterprise_name: enterpriseName.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        type: type,
                        equipmentName: item.equipment_name,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            if (window.toggle == 2) {
                putIcons(positionData.value, videoImg);
                // console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id);

                        openCesiumDialog(pick.id.type, pick.id._chuancan);

                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                let points = [];
                positionData.value.forEach((item) => {
                    if (item.type == 10) {
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: "/static/poi/video-new.svg", //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        points.push({
                            lnglat: [item.longitude, item.latitude],
                            data: item,
                        });
                        marker.on("click", (e) => {
                            videoId.value = item.name;
                            videoDialogShow.value = true;
                            proxy.$loading.show();
                            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                        });
                        const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;

  `;
                        const sizeColor = `
                   width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 14px;
  `;

                        // const box = `<div style="${boxStyle}"></div>`;
                        let infoWindow = new AMap.InfoWindow({
                            isCustom: true,
                            // 设置信息窗口的内容
                            content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.equipmentName}</div>
                         </div>`,
                            // 设置信息窗口的偏移量
                            offset: new AMap.Pixel(0, -85),
                        });
                        // 添加鼠标移入事件监听器
                        marker.on("mouseover", (e) => {
                            infoWindow.open(
                                window.map1,
                                e.target.getPosition(),
                            );
                        });

                        // 添加鼠标移出事件监听器
                        marker.on("mouseout", (e) => {
                            infoWindow.close();
                        });
                        if (
                            enterpriseName.value &&
                            enterpriseName.value != null &&
                            enterpriseName.value != ""
                        )
                            marker.setMap(window.map1);
                    }
                });
                console.log(enterpriseName, "enterpriseName");
                if (
                    enterpriseName.value == null ||
                    enterpriseName.value == ""
                ) {
                    var count = points.length;
                    // var cluster
                    var gridSize = 60;
                    var _renderMarker = (context) => {
                        let item = context.data[0].data;
                        var offset = new AMap.Pixel(-36, -92);
                        context.marker.setOffset(offset);
                        context.marker.setIcon(
                            new AMap.Icon({
                                image: "/static/poi/video-new.svg",
                                imageSize: new AMap.Size(72, 92),
                            }),
                        );
                        // context.marker.on("click",(e)=>{
                        //   console.log(e)
                        //   console.log(item)
                        // })
                        context.marker.on("click", (e) => {
                            videoId.value = item.name;
                            videoDialogShow.value = true;
                            proxy.$loading.show();
                            //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                        });
                        const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                        const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 16px;
`;

                        const box = `<div style="${boxStyle}"></div>`;
                        let infoWindow = new AMap.InfoWindow({
                            isCustom: true,
                            // 设置信息窗口的内容
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${item.equipmentName}</div>
                       </div>`,
                            // 设置信息窗口的偏移量
                            offset: new AMap.Pixel(0, -85),
                        });
                        // 添加鼠标移入事件监听器
                        context.marker.on("mouseover", (e) => {
                            infoWindow.open(
                                window.map1,
                                e.target.getPosition(),
                            );
                        });

                        // 添加鼠标移出事件监听器
                        context.marker.on("mouseout", (e) => {
                            infoWindow.close();
                        });
                        context.marker.setExtData(item);
                    };
                    var addCluster = (tag) => {
                        if (window.cluster) {
                            window.cluster.setMap(null);
                        }
                        window.cluster = new AMap.MarkerCluster(
                            window.map1,
                            points,
                            {
                                gridSize: gridSize, // 设置网格像素大小
                                renderMarker: _renderMarker, // 自定义非聚合点样式
                            },
                        );
                        window.cluster.on("click", (item) => {
                            if (item.clusterData.length <= 1) {
                                return;
                            }
                            let alllng = 0,
                                alllat = 0;
                            for (const mo of item.clusterData) {
                                alllng += mo.lnglat.lng;
                                alllat += mo.lnglat.lat;
                            }
                            const lat = alllat / item.clusterData.length;
                            const lng = alllng / item.clusterData.length;
                            window.map1.setFitView();
                            window.map1.setZoomAndCenter(
                                window.map1.getZoom() + 2,
                                [lng, lat],
                            );
                        });
                    };
                    addCluster(2);
                }
                window.map1.setFitView();
            }
        }
    });
};
//特殊作业今日报警
proxy.$bus.on("alarmTodayList", (val) => {
    console.log("test ", val.length);
    positionData.value = [];
    areaData.value = [];
    areaDataThree.value = [];
    if (val.length > 0) {
        val.forEach((item) => {
            console.log(item);
            console.log(JSON.parse(item.location));
            let da = JSON.parse(item.location);
            if (item.location != null && item.location != undefined) {
                positionData.value.push({
                    name: item.id,
                    equipmentName: item.work_type,
                    id: item.id,
                    type: 15,
                    longitude: Number(da.lng),
                    latitude: Number(da.lat),
                });
            }
        });
        if (window.toggle == 2) {
            putIcons(positionData.value, videoImg);
            // console.log(window.viewer.entities);
            // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
            window.viewer.screenSpaceEventHandler.setInputAction(function (e) {
                var pick = viewer.scene.pick(e.position);
                if (pick && pick.id) {
                    console.log(pick.id);

                    openCesiumDialog(pick.id.type, pick.id._chuancan);

                    window.viewer.camera.setView({
                        destination: Cesium.Cartesian3.fromDegrees(
                            pick.id._longitude,
                            pick.id._latitude,
                            800,
                        ),
                    });
                }
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        } else if (window.toggle == 3) {
            positionData.value.forEach((item) => {
                if (item.type == 15) {
                    let marker = new AMap.Marker({
                        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                        icon: new AMap.Icon({
                            size: new AMap.Size(72, 92), // 图标尺寸
                            image: "/static/poi/specialWork.svg", //绝对路径
                            imageSize: new AMap.Size(72, 92),
                        }),
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-36, -92),
                    });
                    marker.on("click", (e) => {
                        specialId.value = item.name;
                        specialDialogShow.value = true;
                        proxy.$loading.show();
                        //   window.map1.setZoomAndCenter(22, e.target._opts.position)
                    });
                    const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      line-height: 40px;

  `;
                    const sizeColor = `
                   width: 100%;
                    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
      font-size: 14px;
  `;

                    // const box = `<div style="${boxStyle}"></div>`;
                    let infoWindow = new AMap.InfoWindow({
                        isCustom: true,
                        // 设置信息窗口的内容
                        content: `<div style="${boxStyle}">
                              <div style="${sizeColor}">${item.equipmentName}</div>
                         </div>`,
                        // 设置信息窗口的偏移量
                        offset: new AMap.Pixel(0, -85),
                    });
                    // 添加鼠标移入事件监听器
                    marker.on("mouseover", (e) => {
                        infoWindow.open(window.map1, e.target.getPosition());
                    });

                    // 添加鼠标移出事件监听器
                    marker.on("mouseout", (e) => {
                        infoWindow.close();
                    });
                    marker.setMap(window.map1);
                }
            });
            window.map1.setFitView();
        }
    }
});
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let chartData = ref([]);
//cesium图标点击事件
const openCesiumDialog = (val, id) => {
    console.log("jfksdjfhjsdfjdgfjhf", val);
    switch (val) {
        case 1:
            pickWorkshopkId.value = id;
            workshopDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 2:
            warehouseId.value = id;
            warehouseDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 3:
            tankId.value = id;
            tankDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 4:
            pickMajorId.value = id;
            majorHazardDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 9:
            monitoringPointskId.value = id;
            monitoringPointsDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 6:
            highriskProcessesId.value = id;
            highriskProcessesDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 8:
            hazardousChemicalsId.value = id;
            hazardousChemicalsDialogShow.value = true;
            proxy.$loading.show();
            break;
        case 5:
            riskPointsId.value = id;
            riskPointsDialogShow.value = true;
            proxy.$loading.show();

            break;
        case 10:
            videoId.value = id;
            videoDialogShow.value = true;
            proxy.$loading.show();
            break;
        default:
            break;
    }
};
import workshopImg from "/static/poi/workshop-new.svg";
import workshopGreyImg from "/static/poi/workshop-grey.svg";
import warehouseImg from "/static/poi/warehouse-new.svg";
import warehouseGreyImg from "/static/poi/warehouse-grey.svg";
import tankNoGreyImg from "/static/poi/tank-new.svg";
import tankGreyImg from "/static/poi/tank-grey.svg";
import storageGreyTankImg from "/static/poi/storageTank-grey.svg";
import storageNoGreyTankImg from "/static/poi/storageTank-new.svg";
import majorHazardImg1 from "/static/poi/majorHazard1.svg";
import majorHazardImg2 from "/static/poi/majorHazard2.svg";
import majorHazardImg3 from "/static/poi/majorHazard3.svg";
import majorHazardImg4 from "/static/poi/majorHazard4.svg";
import majorHazardImgGrey from "/static/poi/majorHazard-grey.svg";
import riskPointsImg1 from "/static/poi/riskPoints1.svg";
import riskPointsImg2 from "/static/poi/riskPoints2.svg";
import riskPointsImg3 from "/static/poi/riskPoints3.svg";
import riskPointsImg4 from "/static/poi/riskPoints4.svg";
import highRiskProcessesImg from "/static/poi/highRiskProcesses-new.svg";
import hazardousChemicalsImg from "/static/poi/hazardousChemicals-new.svg";
import videoImg from "/static/poi/video-new.svg";
import SpecialAssignmentsDialog from "./subgroup/specialAssignmentsDialog.vue";
//筛选点击的下拉框里的数据,并返回一个新数组
const resetData = (list, values, uid) => {
    //list全部数据
    //values是根据哪个属性
    //uid要查询的id数据
    let selectLsit = [];
    list.forEach((item) => {
        if (item[values] == uid) {
            selectLsit.push(item);
        }
    });
    window.viewer.entities.removeAll(); //删除所有
    window.map1.clearMap();
    // console.log(window.viewer.entities);
    initBoundary();
    return selectLsit;
};

// const aliveImg = ref('../../../assets/images/card/interval.svg')
const totalData = ref([]); //风险点查id对应的name
const positionData = ref([]);
const areaData = ref([]);
const areaDataThree = ref([]);
//cesium调整视野
const adjustField = () => {
    const arrs = []; //经纬度数组
    const lonArr = []; //经纬度数组
    const latArr = []; //经纬度数组

    positionData.value.forEach((item) => {
        //循环数据 push经纬度
        arrs.push(item.longitude);
        lonArr.push(item.longitude);
        arrs.push(item.latitude);
        latArr.push(item.latitude);
    });
    if (areaDataThree.value != []) {
        areaDataThree.value.forEach((item) => {
            for (let i = 0; i < item.path.length; i++) {
                // 根据索引值判断要添加到哪个目标数组
                if (i % 3 === 0) {
                    arrs.push(item.path[i]);
                    lonArr.push(item.path[i]);
                } else if (i % 3 === 1) {
                    arrs.push(item.path[i]);
                    latArr.push(item.path[i]);
                }
            }
        });
    }
    let adr = Cesium.Cartesian3.fromDegreesArray(arrs);
    let polys = Cesium.BoundingSphere.fromPoints(adr).center;
    polys = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polys);
    let ellipso = window.viewer.scene.globe.ellipsoid;
    let cartesian = new Cesium.Cartesian3(polys.x, polys.y, polys.z);
    let cartographic2 = ellipso.cartesianToCartographic(cartesian);

    let obj2 = {};
    obj2.lat = Cesium.Math.toDegrees(cartographic2.latitude);
    obj2.lng = Cesium.Math.toDegrees(cartographic2.longitude);
    obj2.alt = cartographic2.height;
    const maxLon = Math.max.apply(Math, lonArr);
    const minLon = Math.min.apply(Math, lonArr);
    const maxLat = Math.max.apply(Math, latArr);
    const minLat = Math.min.apply(Math, latArr);
    const lat = maxLat - minLat;
    const lon = maxLon - minLon;
    let radius = lat > lon ? lat : lon;
    //   console.log(radius);
    let h = radius * 550000;
    if (radius == 0) {
        radius = 0.002;
    }
    if (radius > 1) {
        // console.log(11111111);
        h = radius * 200000; //高度 自行调整  250000
    }
    if (radius < 0.004) {
        h = radius * 1150000;
    }
    if (radius < 0.002) {
        h = radius * 9550000;
    }

    nextTick(() => {
        window.viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(obj2.lng, obj2.lat, h),
        });
    });
};
/**
 * 视图定位方法，定位到范围
 * 范围数组（最西、最南、最东、最北）
 */
let locationRectEntity = null;
const viewerFlyToRange = (rect) => {
    let maxlat = 0; //维度最大
    let minlat = 90;
    let maxlag = 0;
    let minlag = 120; //经度最小
    positionData.value.forEach((item) => {
        //循环数据 push经纬度
        maxlag = item.longitude > maxlag ? item.longitude : maxlag;
        minlag = item.longitude < minlag ? item.longitude : minlag;
        maxlat = item.latitude > maxlat ? item.latitude : maxlat;

        minlat = item.latitude < minlat ? item.latitude : minlat;
    });
    if (locationRectEntity) window.viewer.entities.remove(locationRectEntity);
    locationRectEntity = window.viewer.entities.add({
        // name: 'locationRectangle',
        // id: 'locationRectangle',
        rectangle: {
            coordinates: Cesium.Rectangle.fromDegrees(
                minlag,
                minlat,
                maxlag,
                maxlat,
            ),
            material: Cesium.Color.GREEN.withAlpha(0),
            height: 10.0,
            outline: false,
        },
    });
    // let flyPromise = window.viewer.flyTo(locationRectEntity, {
    //     duration: 5,
    //     offset: new Cesium.HeadingPitchRange(0.0, Cesium.Math.toRadians(-20.0))
    // });

    //倾斜后记录相机到矩形中心点的距离，然后再根据倾斜角度，计算新的距离，
    // 再viewer.flyto的完成后，再通过中心点离相机的距离，使用zoomTo来拉近一下距离
    flyPromise.then(function () {
        let center = Cesium.Rectangle.center(
            Cesium.Rectangle.fromDegrees(minlag, minlat, maxlag, maxlat),
        );
        let car = Cesium.Cartesian3.fromRadians(
            center.longitude,
            center.latitude,
        );
        let range =
            Cesium.Cartesian3.distance(car, window.viewer.camera.position) *
            Math.cos(20);
        window.viewer.zoomTo(
            locationRectEntity,
            new Cesium.HeadingPitchRange(
                0.0,
                Cesium.Math.toRadians(-20.0),
                range,
            ),
        );
    });
};
const putIcons = (_datas, img, _parent) => {
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        const entity = window.viewer.entities.add({
            name: data.id,
            // 参数顺序：经度、纬度
            chuancan: data.name,
            source_level: data.source_level,
            is_alarm: data.is_alarm,
            type: data.type,
            isGrey: data.isGrey, //是否置灰
            longitude: Number(data.longitude) - 0.0062,
            latitude: Number(data.latitude) - 0.00085,
            position: Cesium.Cartesian3.fromDegrees(
                Number(data.longitude) - 0.0062,
                Number(data.latitude) - 0.00085,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 72,
                height: 92,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                pixelOffset: new Cesium.Cartesian2(0, -46),
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            //   type: "text", // 自定义属性
        });
        // window.viewer.zoomTo(entity);
        // console.log(entity);
        // console.log(999999999999999999999999999999999);
        adjustField();
    }
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
//园区边界
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
const enterpriseNameOptions = ref([]);
var todayTime0 = null;
var todayTime24 = null;
onMounted(() => {
    let currentDate = new Date();
    let year = currentDate.getFullYear();
    let month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
    let day = currentDate.getDate();
    console.log(year + "-" + month + "-" + day);
    let time0 = year + "-" + month + "-" + day + " " + "00:00:00";
    let time24 = year + "-" + month + "-" + day + " " + "23:59:59";
    todayTime0 = new Date(time0).getTime();
    todayTime24 = new Date(time0).getTime(time24);
    console.log(todayTime0);
    enterprise_list().then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            enterpriseNameOptions.value = res.data.data.map((x) => {
                return {
                    value: x.enterprise_name,
                    label: x.enterprise_name,
                };
            });
        }
    });
    initBoundary();
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
    });

    showFlag.value = window.toggle;
    //报警点击跳转事件
    proxy.$bus.on("clicJump1", (val) => {
        if (val.alarmObjectId != null && val.alarmObjectId != undefined) {
            console.log(val);
            if (val.tabFlag == "安全监管") {
                switch (val.tag) {
                    case "重大危险源-罐区":
                        console.log("重大危险源-罐区");
                        data_tank_farm({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                tankNoGrey.value = re1.data.data;
                            }
                            searchTankForm("", 3, val.alarmObjectId);
                        });
                        break;
                    case "重大危险源-储罐":
                        console.log("重大危险源-储罐");
                        data_storage_tank({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                storageTankNoGrey.value = re1.data.data;
                            }
                            searchStorageTank("", 9, val.alarmObjectId);
                        });
                        break;
                    case "重大危险源-仓库":
                        console.log("重大危险源-仓库");
                        data_entrepo({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                entrepoNoGrey.value = re1.data.data;
                            }
                            searchEntrepo("", 2, val.alarmObjectId);
                        });
                        break;
                    case "重大危险源-厂房车间":
                        console.log("重大危险源-厂房车间");
                        data_plant_workshop({
                            enterprise_name: enterpriseName.value,
                        }).then((re1) => {
                            if (
                                re1.data.success &&
                                re1.data.data &&
                                re1.data.data.length
                            ) {
                                plantWorkshopNoGrey.value = re1.data.data;
                            }
                            searchPlantWorkshop("", 1, val.alarmObjectId);
                        });
                        break;

                    default:
                        break;
                }
            }
        }
    });
    console.log(window.toggle, "window.toggle");
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
});
onBeforeUnmount(() => {
    if (window.cluster) window.cluster.setMap(null);
    if (lineChart) {
        lineChart.dispose();
    }
});
onUnmounted(() => {
    proxy.$bus.off("change_toggle");
});
</script>
<style lang="less">
@keyframes blink {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.1;
    }
    100% {
        opacity: 1;
    }
}

.abnormal_marker {
    animation: blink 1.5s infinite;
}
</style>

<style lang="less" scoped>
.sizeColor {
    border-radius: 10px;
    line-height: 50px;
    color: #ffffff;
    font-size: 16px;
}

.input-wrapper {
    width: 320px;
    height: 40px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 496px;

    :deep(.el-input__wrapper) {
        background-color: transparent;
        box-shadow: 0 0 0 0;
        border-radius: 4px;
        border: 1px solid #30abe8;
        height: 22px;
        padding: 7px 12px;
    }

    :deep(.el-input__inner) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-input__inner::placeholder) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}

.input-wrapper1 {
    width: 320px;
    height: 40px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 712px;

    :deep(.el-input__wrapper) {
        background-color: transparent;
        box-shadow: 0 0 0 0;
        border-radius: 4px;
        border: 1px solid #30abe8;
        height: 22px;
        padding: 7px 12px;
    }

    :deep(.el-input__inner) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-input__inner::placeholder) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}

.safety-supervision-navigation {
    width: 158px;
    height: 672px;
    z-index: 1000;
    position: absolute;
    top: 574px;
    left: 496px;

    > div:not(:first-child) {
        margin-top: 24px;
    }

    .noactive {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-noactive-big.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }

    .btn {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-big.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }

    .active-btn {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-active-big.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        // gap: 24px;
    }

    .icon {
        width: 24px;
        height: 24px;
        // margin: auto 0;
        margin-top: 4px;
        margin-left: 12px;
    }

    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        margin: auto 0;
        margin-left: 24px;
    }
}

#sub {
    width: 158px;
    height: 672px;
    z-index: 1000;
    position: absolute;
    top: 570px;
    left: 620px;

    > div:not(:first-child) {
        margin-top: 24px;
    }

    .noactive {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-noactive-big.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }

    .btn {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-big.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }

    .active-btn {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-active-big.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        // gap: 24px;
    }

    .icon {
        width: 24px;
        height: 24px;
        // margin: auto 0;
        margin-top: 4px;
        margin-left: 12px;
    }

    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        margin: auto 0;
        margin-left: 24px;
    }
}

.major-hazard-dialog {
    width: 846px;
    height: 712px;
    border-radius: 4px;
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 20%;
    left: 31%;
    z-index: 1003;

    .header {
        width: 846px;
        height: 40px;
        background: url("../../../assets/images/dialog/major-hazard-dialog.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;

        .title {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 40px; /* 150% */
            margin-left: 18px;
        }

        .close {
            width: 24px;
            height: 24px;
            background: url("../../../assets/images/dialog/close.svg") no-repeat
                center center;
            background-size: cover;
            background-position: center;
            margin: auto 0;
            margin-left: 686px;
        }
    }

    .top {
        width: 814px;
        height: 144px;
        // background-color: antiquewhite;
        margin: 12px auto 36px;
        // table{
        //     border-collapse: collapse;
        // }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }

        .title1 {
            width: 146px;
            height: 36px;
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 157.143% */
        }

        .text1 {
            width: 261px;
            height: 36px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 157.143% */
        }
    }

    .liner {
        width: 814px;
        height: 2px;
        background: url("../../../assets/images/dialog/liner.svg") no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }

    .middle {
        width: 814px;
        height: 40px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;

        .dialog-active-btn {
            width: 144px;
            height: 40px;
            background: url("../../../assets/images/dialog/dialog-active-btn.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
            color: #fff;
            text-align: center;
            text-shadow: 0px 0px 8px rgba(77, 57, 0, 0.6);
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px; /* 150% */
        }

        .dialog-btn {
            width: 144px;
            height: 40px;
            background: url("../../../assets/images/dialog/dialog-btn.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
            color: #fff;
            text-align: center;
            text-shadow: 0px 0px 8px rgba(8, 48, 69, 0.6);
            font-family: Noto Sans SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px; /* 150% */
        }
    }

    .bottom {
        width: 814px;
        height: 360px;
        // background-color: aqua;
        margin: 0 auto;

        .video {
            width: 814px;
            height: 360px;
            // background-color: aqua;
            .img {
                width: 814px;
                height: 360px;
            }
        }

        .linewrap {
            width: 814px;
            height: 360px;
            // background-color:red;
        }

        .details {
            width: 814px;
            height: 360px;
            // background-color:#30ABE8;
            /*最外层透明*/

            /deep/ .el-table,
            /deep/ .el-table__expanded-cell {
                background-color: transparent !important;
                color: #ffffff;
            }

            // 设置表头颜色
            /deep/ .el-table th {
                background-color: transparent !important;
                color: #ffffff;
                // border-color: #30ABE8 !important;
            }

            /* 表格内背景颜色 */

            /deep/ .el-table tr,
            /deep/ .el-table td {
                background-color: transparent !important;
                // border-color: #30ABE8 !important;
            }

            // 去掉最下面的那一条线
            .el-table::before {
                height: 0px;
                // border-color: #30ABE8 !important;
            }
        }
    }
}
</style>
