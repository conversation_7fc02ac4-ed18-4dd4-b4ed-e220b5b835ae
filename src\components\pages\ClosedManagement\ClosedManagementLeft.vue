<template>
    <div class="closed-management-left">
        <left-top></left-top>
        <left-center></left-center>
        <left-bottom></left-bottom>
    </div>
</template>

<script setup>

import WrapSlot from "../../commenNew/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
</script>

<style lang="less" scoped>
.closed-management-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
