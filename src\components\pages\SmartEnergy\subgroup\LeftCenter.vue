<template>
    <Card title="能耗季度分析趋势">
        <template v-slot>
    <div class="energy-consumption-trends">
        <div class="select-btns">
            <div
                :class="active === 1 ? 'active-btn' : 'btn'"
                @click="changeDoor(1)"
            >
                电
            </div>
            <div
                :class="active === 2 ? 'active-btn' : 'btn'"
                @click="changeDoor(2)"
            >
                水
            </div>
            <div
                :class="active === 3 ? 'active-btn' : 'btn'"
                @click="changeDoor(3)"
            >
                天然气
            </div>
            <div
                :class="active === 4 ? 'active-btn' : 'btn'"
                @click="changeDoor(4)"
            >
                蒸汽
            </div>
        </div>
        <div class="linewrap" ref="linewrap"></div>
    </div>
</template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import {
    energy_month_water,
    energy_month_gas,
    energy_month_steam,
    energy_month_electricity,
} from "../../../../assets/js/api/smartEnergy";

const active = ref(1);
const unit = ref("千瓦时");
const changeDoor = (type) => {
    active.value = type;
    xData = [];
    yData = [];
    if (lineChart) {
        lineChart.dispose();
    }
    lineChart = echarts.init(linewrap.value);
    if (type == 1) {
        unit.value = "千瓦时";
        energy_month_electricity({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                res.data.data.forEach((item) => {
                    if (item.quarter == "1") {
                        xData.push("第一季度");
                    } else if (item.quarter == "2") {
                        xData.push("第二季度");
                    } else if (item.quarter == "3") {
                        xData.push("第三季度");
                    } else {
                        xData.push("第四季度");
                    }
                    yData.push(
                        item.total_energy == null
                            ? 0
                            : Number(item.total_energy),
                    );
                });
                console.log(yData);
                initChart();
                lineChart.setOption(option);
                window.addEventListener("resize", () => {
                    lineChart.resize();
                });
            }
        });
    } else if (type == 2) {
        unit.value = "吨";
        energy_month_water({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                res.data.data.forEach((item) => {
                    if (item.quarter == "1") {
                        xData.push("第一季度");
                    } else if (item.quarter == "2") {
                        xData.push("第二季度");
                    } else if (item.quarter == "3") {
                        xData.push("第三季度");
                    } else {
                        xData.push("第四季度");
                    }
                    yData.push(
                        item.total_energy == null
                            ? 0
                            : Number(item.total_energy),
                    );
                });
                console.log(yData);
                initChart();
                lineChart.setOption(option);
                window.addEventListener("resize", () => {
                    lineChart.resize();
                });
            }
        });
    } else if (type == 3) {
        unit.value = "千立方米";
        energy_month_gas({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                res.data.data.forEach((item) => {
                    if (item.quarter == "1") {
                        xData.push("第一季度");
                    } else if (item.quarter == "2") {
                        xData.push("第二季度");
                    } else if (item.quarter == "3") {
                        xData.push("第三季度");
                    } else {
                        xData.push("第四季度");
                    }
                    yData.push(
                        item.total_energy == null
                            ? 0
                            : Number(item.total_energy),
                    );
                });
                console.log(yData);
                initChart();
                lineChart.setOption(option);
                window.addEventListener("resize", () => {
                    lineChart.resize();
                });
            }
        });
    } else {
        unit.value = "吨";
        energy_month_steam({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                res.data.data.forEach((item) => {
                    if (item.quarter == "1") {
                        xData.push("第一季度");
                    } else if (item.quarter == "2") {
                        xData.push("第二季度");
                    } else if (item.quarter == "3") {
                        xData.push("第三季度");
                    } else {
                        xData.push("第四季度");
                    }
                    yData.push(
                        item.total_energy == null
                            ? 0
                            : Number(item.total_energy),
                    );
                });
                console.log(yData);
                initChart();
                lineChart.setOption(option);
                window.addEventListener("resize", () => {
                    lineChart.resize();
                });
            }
        });
    }
};
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let yData = ref([]);
const initChart = () => {
    option = {
        grid: {
            left: 15,
            right: 25,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        tooltip: {
            show: true,
            trigger: "axis",
        },
        legend: {
            show: false,
            x: "right", // 图例水平居中
            y: "top", // 图例垂直居上
            itemStyle: { opacity: 0 }, //去圆点
            textStyle: {
                color: "#fff",
            },
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    color: "#1AB2FF",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#397cbc",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "#195384",
                    },
                },
                data: xData,
            },
        ],
        yAxis: [
            {
                type: "value",
                name: `单位：${unit.value}`,
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#fff",
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#27b4c2",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
            },
        ],
        series: [
            {
                name: "能耗",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: true,
                itemStyle: {
                    normal: {
                        color: "#FFC61A",
                        lineStyle: {
                            color: "#FFC61A",
                            width: 1,
                        },
                        areaStyle: {
                            //color: '#94C9EC'
                            color: new echarts.graphic.LinearGradient(
                                0,
                                1,
                                0,
                                0,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(255, 198, 26, 0.00)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(255, 198, 26, 0.30)",
                                    },
                                ],
                            ),
                        },
                    },
                },
                data: yData,
            },
        ],
    };
};
onMounted(() => {
    lineChart = echarts.init(linewrap.value);
    xData = [];
    yData = [];
    energy_month_electricity({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            res.data.data.forEach((item) => {
                if (item.quarter == "1") {
                    xData.push("第一季度");
                } else if (item.quarter == "2") {
                    xData.push("第二季度");
                } else if (item.quarter == "3") {
                    xData.push("第三季度");
                } else {
                    xData.push("第四季度");
                }
                yData.push(
                    item.total_energy == null ? 0 : Number(item.total_energy),
                );
            });
            console.log(yData);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        }
    });
});
onBeforeUnmount(() => {
    // loopShowTooltip(lineChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.energy-consumption-trends {
    width: 416px;
    height: 224px;
    padding: 16px;
    .select-btns {
        width: 248px;
        height: 24px;
        display: flex;
        gap: 8px;
        margin-left: 152px;
        .active-btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.5px solid #ffc61a;
            background: rgba(255, 198, 26, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
        .btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.622px solid #1ab2ff;
            background: rgba(26, 178, 255, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
    }
    .linewrap {
        width: 400px;
        height: 200px;
        // margin-top: 16px;
        // background-color: aqua;
    }
}
</style>
