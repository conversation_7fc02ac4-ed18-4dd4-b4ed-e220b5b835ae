<template>
    <Card title="企业能耗排行">
        <template v-slot>
        <div class="Enterprise-consumption-rank">
            <div class="select-btns">
                <div
                    :class="active === 1 ? 'active-btn' : 'btn'"
                    @click="changeDoor(1)"
                >
                    电
                </div>
                <div
                    :class="active === 2 ? 'active-btn' : 'btn'"
                    @click="changeDoor(2)"
                >
                    水
                </div>
                <div
                    :class="active === 3 ? 'active-btn' : 'btn'"
                    @click="changeDoor(3)"
                >
                    天然气
                </div>
                <div
                    :class="active === 4 ? 'active-btn' : 'btn'"
                    @click="changeDoor(4)"
                >
                    蒸汽
                </div>
            </div>
            <div class="barwrap" ref="barwrap"></div>
        </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    energy_rank_electricity,
    energy_rank_water,
    energy_rank_gas,
    energy_rank_steam,
} from "../../../../assets/js/api/smartEnergy";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
const unit = ref("千瓦时");
const active = ref(1);
const changeDoor = (type) => {
    active.value = type;
    if (type == 1) {
        unit.value = "千瓦时";
        energy_rank_electricity({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                echartData.value = res.data.data.map((x) => {
                    return {
                        value: Number(x.number),
                        name: x.enterprise_name,
                    };
                });
                barChart = echarts.init(barwrap.value);
                initChart();
                barChart.setOption(option);
                window.addEventListener("resize", () => {
                    barChart.resize();
                });
            } else {
                if (barChart) {
                    barChart.dispose();
                }
            }
        });
    } else if (type == 2) {
        unit.value = "吨";
        energy_rank_water({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                echartData.value = res.data.data.map((x) => {
                    return {
                        value: Number(x.number),
                        name: x.enterprise_name,
                    };
                });
                barChart = echarts.init(barwrap.value);
                initChart();
                barChart.setOption(option);
                window.addEventListener("resize", () => {
                    barChart.resize();
                });
            } else {
                if (barChart) {
                    barChart.dispose();
                }
            }
        });
    } else if (type == 3) {
        unit.value = "千立方米";
        energy_rank_gas({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                echartData.value = res.data.data.map((x) => {
                    return {
                        value: Number(x.number),
                        name: x.enterprise_name,
                    };
                });
                barChart = echarts.init(barwrap.value);
                initChart();
                barChart.setOption(option);
                window.addEventListener("resize", () => {
                    barChart.resize();
                });
            } else {
                if (barChart) {
                    barChart.dispose();
                }
            }
        });
    } else {
        unit.value = "吨";

        energy_rank_steam({ "": "" }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                echartData.value = res.data.data.map((x) => {
                    return {
                        value: Number(x.number),
                        name: x.enterprise_name,
                    };
                });
                barChart = echarts.init(barwrap.value);
                initChart();
                barChart.setOption(option);
                window.addEventListener("resize", () => {
                    barChart.resize();
                });
            } else {
                if (barChart) {
                    barChart.dispose();
                }
            }
        });
    }
};
var echartData = ref([]);
let option = reactive({});
let barwrap = ref(null);
let barChart;
const initChart = () => {
    option = {
        grid: {
            top: "10%",
            left: "0%",
            right: "10%",
            bottom: "0%",
        },
        yAxis: {
            inverse: true,
            axisTick: {
                show: false,
            },
            axisLine: {
                show: false,
            },
            splitLine: {
                show: false,
            },
            axisLabel: {
                show: false,
            },
            data: echartData.value
                .sort((a, b) => b.value - a.value)
                .map((v) => v.name),
        },
        xAxis: {
            show: false,
        },
        series: [
            {
                //内
                type: "bar",
                barWidth: 10,
                legendHoverLink: false,
                symbolRepeat: true,
                silent: true,
                label: {
                    show: true,
                    position: ["0%", "-240%"],
                    color: "#FFFFFF",
                    fontSize: 14,
                    formatter: (a) => {
                        const _index = a.dataIndex + 1;
                        return `{num|0${_index}} ${a.name}`;
                    },
                    rich: {
                        num: {
                            color: "#FFFFFF",
                            fontSize: 14,
                            backgroundColor: "#199FFF",
                            borderRadius: 2,
                            padding: [4, 2, 0, 2],
                        },
                    },
                },
                itemStyle: {
                    color: {
                        type: "linear",
                        x: 1,
                        y: 0,
                        x2: 0,
                        y2: 0,
                        colorStops: [
                            {
                                offset: 0,
                                color: "#01FFFF", // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: "#199FFF", // 100% 处的颜色
                            },
                        ],
                        global: false, // 缺省为 false
                    },
                },
                data: echartData.value.sort((a, b) => b.value - a.value),
                z: 1,
            },
            {
                // 背景
                type: "pictorialBar",
                symbolRepeat: "fixed",
                symbolMargin: "20%",
                symbol: "rect",
                symbolSize: [10, 10],
                label: {
                    show: true,
                    position: ["82%", "-15%"],
                    color: "#FFFFFF",
                    fontSize: 12,
                    formatter: function (val) {
                        console.log(val);
                        return `${val.value}${unit.value}`;
                    },
                },
                itemStyle: {
                    color: "rgba(1, 255, 255, 0.15)",
                },
                data: echartData.value
                    .sort((a, b) => b.value - a.value)
                    .map((v) => v.value),
                z: 0,
            },
            {
                //分隔
                type: "pictorialBar",
                itemStyle: {
                    color: "rgba(13, 47, 115, 0.65)",
                },
                symbolRepeat: "fixed",
                symbolMargin: 5, //间隔间的宽度，就是颜色条的宽度
                symbol: "rect",
                symbolClip: true,
                symbolSize: [3, 10],
                symbolPosition: "start",
                // symbolOffset: [0, 0],
                data: echartData.value
                    .sort((a, b) => b.value - a.value)
                    .map((v) => v.value),
                z: 2,
            },
        ],
    };
};
onMounted(() => {
    barChart = echarts.init(barwrap.value);
    energy_rank_electricity({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            echartData.value = res.data.data.map((x) => {
                return {
                    value: Number(x.number),
                    name: x.enterprise_name,
                };
            });
            initChart();
            barChart.setOption(option);
            window.addEventListener("resize", () => {
                barChart.resize();
            });
        }
    });
});
onBeforeUnmount(() => {
    // loopShowTooltip(barChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (barChart) {
        // setTimeout(() => {
        barChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.Enterprise-consumption-rank {
     width: 416px;
    height: 224px;
    padding: 16px;
    .select-btns {
        width: 248px;
        height: 24px;
        display: flex;
        gap: 8px;
        margin-left: 152px;
        .active-btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.5px solid #ffc61a;
            background: rgba(255, 198, 26, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
        .btn {
            width: 56px;
            height: 24px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.622px solid #1ab2ff;
            background: rgba(26, 178, 255, 0.3);
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 133.333% */
        }
    }
    .barwrap {
        width: 416px;
        height: 200px;
        // margin-top: 16px;
        // background-color: aqua;
    }
}
</style>
