<template>
    <Card title="出入园实时动态">
        <template v-slot>
            <div class="real-time-dynamic">
                <div class="top">
                    <div class="inner-in">
                        <div class="inner-in-image"></div>
                        <div class="detail">
                            <div class="title">累计入园/今日</div>
                            <div class="num-in">
                                {{ todayIn?todayIn:0 }}<span class="unit">次</span>
                            </div>
                        </div>
                    </div>
                    <div class="inner-out">
                        <div class="inner-out-image"></div>
                        <div class="detail">
                            <div class="title">累计出园/今日</div>
                            <div class="num-out">
                                {{ todayOut?todayOut:0 }}<span class="unit">次</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="noDataFlag" class="all-wrapper">
                    <div class="img-no">暂无数据</div>
                </div>
                <div v-else>
                    <autoScroll
                        class="card-container"
                        :step="2"
                        :deltaTime="100"
                        v-if="alarmList && alarmList.length > 4"
                    >
                        <table class="alarm-table" cellpadding="0">
                            <tr
                                class="row"
                                v-for="(item, index) in alarmList"
                                :key="index"
                            >
                                <div>
                                    <div
                                        class="item-left-in"
                                        v-if="item.passType == '进'"
                                    ></div>
                                    <div class="item-left-out" v-else></div>
                                </div>
                                <div class="bottom-right-all">
                                    <div class="plate">
                                        {{ item.carNumber }}
                                    </div>
                                    <div class="area">{{ item.area }}</div>
                                    <div class="type">{{ item.carInSource }}</div>
                                    <div class="time">{{ item.inOutTime }}</div>
                                </div>
                            </tr>
                        </table>
                    </autoScroll>
                    <div v-else class="all-wrapper">
                        <table class="alarm-table" cellpadding="0">
                            <tr
                                class="row"
                                v-for="(item, index) in alarmList"
                                :key="index"
                            >
                                <div>
                                    <div
                                        class="item-left-in"
                                        v-if="item.passType == '进'"
                                    ></div>
                                    <div class="item-left-out" v-else></div>
                                </div>
                                <div class="bottom-right-all">
                                    <div class="plate">
                                        {{ item.carNumber }}
                                    </div>
                                    <div class="area">{{ item.area }}</div>
                                    <div class="type">{{ item.carInSource }}</div>
                                    <div class="time">{{ item.inOutTime }}</div>
                                </div>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    onUnmounted,
} from "vue";
import Card from "@/components/commenNew/Card.vue";
import autoScroll from "@/components/commenNew/autoScroll.vue";
import {
    inOutSituation
} from "@/assets/js/api/closedManagement";
const { proxy } = getCurrentInstance();

let todayIn = ref(0);
let todayOut = ref(0);
let noDataFlag = ref(true);
const alarmList = ref([]);
let typeDict=ref([

])
const getData = () => {
    inOutSituation({}).then(res => {
        console.log(res);
        if(res.data&&res.data.data){
            todayIn.value = res.data.data.todayIn;
            todayOut.value = res.data.data.todayOut;
            alarmList.value = res.data.data.list;
            if(alarmList.value&&alarmList.value.length>0){
                noDataFlag.value = false;
            }
            
        }else{
            noDataFlag.value = true;

        }
    })
}
onMounted(() => {
    getData()
});
onUnmounted(() => {});
</script>

<style lang="less" scoped>
.real-time-dynamic {
    width: 416px;
    height: 224px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .img-no {
        margin: auto 0;
        color: #fff;
        text-align: center;
        font-family: "Noto Sans SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 152px; /* 150% */
    }
    .top {
        width: 416px;
        height: 56px;
        display: flex;
        gap: 16px;
        .inner-in {
            width: 159px;
            padding: 0 20.5px;
            height: 56px;
            display: flex;
            gap: 4px;
            .inner-in-image {
                width: 56px;
                height: 48px;
                background: url("@/assets/newImages/ClosedManagement/inner-in.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
        .inner-out {
            width: 159px;
            height: 56px;
            padding: 0 20.5px;
            display: flex;
            gap: 4px;
            .inner-out-image {
                width: 56px;
                height: 48px;
                background: url("@/assets/newImages/ClosedManagement/inner-out.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
        }
        .detail {
            width: 99px;
            height: 56px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 171.429% */
            .num-in {
                font-family: Noto Sans SC;
                font-weight: 700;
                font-size: 24px;
                line-height: 32px;
                letter-spacing: 0%;
                text-align: left;
                color: rgba(255, 198, 26, 1);
            }
            .num-out {
                font-family: Noto Sans SC;
                font-weight: 700;
                font-size: 24px;
                line-height: 32px;
                letter-spacing: 0%;
                text-align: left;
                color: rgba(128, 234, 255, 1);
            }
            .unit {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 14px;
                line-height: 22px;
                letter-spacing: 0%;
                text-align: left;
            }
        }
    }
    .card-container {
        width: 416px;
        height: 152px;
    }
    .all-wrapper {
        width: 416px;
        height: 152px;
    }
    table {
        /*设置行间距*/
        //   border-collapse: separate;
        //   border-spacing: 12px 16px;
        width: 416px;
        height: 152px;
    }
    // table tr:not(:first-child){
    //     margin-top: 16px;
    // }
    .alarm-table {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        display: flex;
        flex-direction: column;
        gap: 16px;
        .item-left-in {
            width: 24px;
            height: 24px;
            background-color: aqua;
            background: url("../../../../assets/images/card/in.svg") no-repeat
                center center;
            background-size: cover;
            background-position: center;
            margin-right: -4px;
        }
        .item-left-out {
            width: 24px;
            height: 24px;
            background: url("../../../../assets/images/card/out.svg") no-repeat
                center center;
            background-size: cover;
            background-position: center;
        }
        .row {
            width: 416px;
            display: flex;
            gap: 8px;
            .bottom-right-all {
                display: flex;
                gap: 26px;
                .plate {
                    width: 61px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .area {
                    width: 28px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .type {
                    width: 86px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .time {
                    width: 131px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    color: #47ebeb;
                }
            }
        }
    }
    .bottom {
        width: 416px;
        height: 152px;
        overflow: hidden;
    }
    .first-marquee {
        animation: 12s first-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes first-marquee {
        0% {
            transform: translate3d(0, 0, 0);
        }
        /* 向上移动 */
        100% {
            transform: translate3d(0, -100%, 0);
        }
    }
    .second-marquee {
        /* 因为要在第一个span播完之前就得出现第二个span，所以就延迟12s才播放 */
        animation: 12s second-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes second-marquee {
        0% {
            transform: translateY(0);
        }
        /* 向上移动 */
        100% {
            transform: translateY(-100%);
        }
    }

    //   .bottom::-webkit-scrollbar {
    //     width: 3px;
    //     height: 10px;
    //     background-color: transparent;
    //   }
    .all-wrapper::-webkit-scrollbar {
        display: none;
    }
}
</style>
