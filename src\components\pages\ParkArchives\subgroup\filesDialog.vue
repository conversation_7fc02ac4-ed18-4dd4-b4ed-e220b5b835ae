<template>
    <div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">企业详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog('enterprise')"
                    />
                </div>
                <div class="dialog-content">
                    <div class="container">
                        <div class="top">
                            <div>{{ enterpriseInfoCountData.enterprise_name }}</div>
                            <div>
                                <span class="title">法定代表人：</span
                                >{{ enterpriseInfoCountData.legal_person_name }}
                            </div>
                            <div>
                                <span class="title">联系方式：</span
                                >{{ enterpriseInfoCountData.contact_way }}
                            </div>
                        </div>
                        <div class="liner"></div>
                        <div class="center">
                            <div class="one">
                                <div class="details">
                                    <div class="icon1"></div>
                                    <div class="value">
                                        <div>
                                            <span class="num">{{
                                                enterpriseInfoCountData.mds_count
                                            }}</span
                                            >个
                                        </div>
                                        <div>重大危险源</div>
                                    </div>
                                </div>
                                <div class="details">
                                    <div class="icon2"></div>
                                    <div class="value">
                                        <div>
                                            <span class="num">{{
                                                enterpriseInfoCountData.dcp_count
                                            }}</span
                                            >个
                                        </div>
                                        <div>重点工艺</div>
                                    </div>
                                </div>
                                <div class="details">
                                    <div class="icon3"></div>
                                    <div class="value">
                                        <div>
                                            <span class="num">{{
                                                enterpriseInfoCountData.dc_count
                                            }}</span
                                            >个
                                        </div>
                                        <div>重点监管化学品</div>
                                    </div>
                                </div>
                            </div>
                            <div class="two">
                                <div class="details">
                                    <div class="icon1"></div>
                                    <div class="value">
                                        <div>
                                            <span class="num">{{
                                                enterpriseInfoCountData.rr_count
                                            }}</span
                                            >个
                                        </div>
                                        <div>风险点</div>
                                    </div>
                                </div>
                                <div class="details">
                                    <div class="icon2"></div>
                                    <div class="value">
                                        <div>
                                            <span class="num">{{
                                                enterpriseInfoCountData.dr_count
                                            }}</span
                                            >个
                                        </div>
                                        <div>当月隐患</div>
                                    </div>
                                </div>
                                <div class="details">
                                    <div class="icon3"></div>
                                    <div class="value">
                                        <div>
                                            <span class="num">{{
                                                enterpriseInfoCountData.sws_count
                                            }}</span
                                            >个
                                        </div>
                                        <div>当月特殊作业</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="liner"></div> -->
                        <div class="bottom">
                            <div class="top-selects">
                                <div class="slect-tabs" ref="tabsRef">
                                    <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="selectType(item.value)">
                                        <div class="tab-item-text" :class="{'active-tab-item':currentTab === item.value}" >{{ item.label }}</div>
                                        <div class="tab-item-line" v-if="currentTab === item.value"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="change">
                            <component
                                :pickId="pickId"
                                :is="changeType"
                                :key="Math.random() * 1000"
                                @sizeChange="sizeChange"
                            ></component>
                        </div>
                    </div>
                   
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    nextTick,
    toRefs,
} from "vue";
import * as Cesium from "cesium";
import BasicArchives from "./BasicArchives.vue";
import TwoKeynote from "./TwoKeynote.vue";
import SecurityArchives from "./SecurityArchives.vue";
import EmergencyArchives from "./EmergencyArchives.vue";
import infrastructureDialog from "./infrastructureDialog.vue";
import SurroundingEnvironmentDialog from "./SurroundingEnvironmentDialog.vue";
import {
    enterprise_industry,
    enterprise_list,
    enterprise_info_count,
    enterprise_info,
    enterprise_staff_files,
    construct_meanwhile,
    product_certificate,
    safety_evaluation_management,
    emergency_management,
    emergency_plan,
    emergency_rehearsal,
    nj_equipments,
    sensitive_target_list,
    sensitive_target_info,
} from "../../../../assets/js/api/parkArchives";
const emit = defineEmits(["closeDialog"]);

const props = defineProps({
    pickId: {
        type: String,
    },
    pickFilesId: {
        type: [String, Number],
    },
});
const { pickId } = toRefs(props);
const { pickFilesId } = toRefs(props);
watch(
    pickId,
    (a, b) => {
        console.log(a, b);
        // console.log(pickFilesId.value);
        // clickName.value = a;
        if (a != undefined && a != b) {
            enterprise_info_count({ enterprise_name: a }).then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    console.log(res);
                    enterpriseInfoCountData.value = res.data.data[0];
                    console.log(enterpriseInfoCountData.value);
                }
            });
        }
    },
    {
        immediate: true,
    },
);
const clickName = ref(null);
const enterpriseInfoCountData = ref({
    enterprise_name: "",
    legal_person_name: "",
    mds_count: "",
    dcp_count: "",
    dc_count: "",
    rr_count: "",
    dr_count: "",
    sws_count: "",
    contact_way: "",
});
let changeType = ref(BasicArchives);
let obj = reactive({
    1: BasicArchives, // 综合监管
    2: TwoKeynote, //两重点一重大
    3: SecurityArchives, //安全类监管档案
    4: EmergencyArchives, //应急类监管档案
});
let size = ref(1);
const enterpriseInfoCount = (name) => {
    enterprise_info_count({ enterprise_name: name }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res);
            enterpriseInfoCountData.value = res.data.data[0];
            console.log(enterpriseInfoCountData.value);
        }
    });
};
//顶部tab
let topTabs = ref([
    {
        value:1,
        label:'基础档案'
    },
    {
        value:2,
        label:'两重点一重大'
    },
    {
        value:3,
        label:'安全类监管档案'
    },
    {
        value:4,
        label:'应急类监管档案'
    },
]);
let currentTab = ref(1);
const tabsRef = ref(null);




const selectType = (type) => {
    currentTab.value = type;
    changeType.value = obj[type];

};

const closeDialog = () => {
    emit("closeDialog", "enterprise");
};
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 788px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 788px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 692px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}

.container {
    // width: 800px;
    // height: 644px;
    // padding: 16px 24px;
    .top {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        text-align: left;
        .title {
            color: #47ebeb;
        }
    }
    .liner {
        width: 800px;
        height: 2px;
        background: url("../../../../assets/images/dialog/liner.svg") no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
    .center {
        // height: 48px;
        .one {
            width: 800px;
            height: 48px;
            display: flex;
            justify-content: space-between;
        }
        .two {
            width: 800px;
            height: 48px;
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
        }
        .details {
            width: 150px;
            height: 48px;
            display: flex;
            gap: 4px;
            .icon1 {
                width: 56px;
                height: 48px;
                background: url("../../../../assets/images/dialog/icon1.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .icon2 {
                width: 56px;
                height: 48px;
                background: url("../../../../assets/images/dialog/icon2.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .icon3 {
                width: 56px;
                height: 48px;
                background: url("../../../../assets/images/dialog/icon3.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .value {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 10px;
                font-style: normal;
                font-weight: 400;
                line-height: 14px; /* 140% */
                .num {
                    color: #fff;
                    text-align: center;
                    font-family: Digital Numbers;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 28px; /* 140% */
                }
            }
        }
    }
    .top-selects{
            width: 8000px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 800px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
    
}
.two-icon-infrastructure {
    width: 48px;
    height: 48px;
    background: url("../../../../../assets/images/dialog/type-active-btn.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    z-index: 99999;
}
</style>
