<template>
    <Card title="数字化设备">
        <template v-slot>
    <div class="digital-devices" @click="clickDevice" style="cursor: pointer">
        <div class="title">设备总数</div>
        <div class="boxAll">
            <div
                :class="item == ',' ? 'box_number1' : 'box_number'"
                v-for="(item, index) in numberList"
                :key="index"
            >
                {{ item }}
            </div>
        </div>
        <div class="content">
            <div class="inner">
                <div class="num on">{{ device.on }}</div>
                <div class="text on-text">在线</div>
            </div>
            <div class="inner">
                <div class="num off">{{ device.off }}</div>
                <div class="text off-text">离线</div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import { njdevice } from "../../../../assets/js/api/comprehensiveSupervision";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    onUnmounted,
    defineEmits,
} from "vue";
const emit = defineEmits(["device"]);

const { proxy } = getCurrentInstance();

const numberAll = ref(null);
const numberList = ref([]);
const device = ref({
    on: 0,
    off: 0,
});

const obtainData = () => {
    numberList.value = [];
    njdevice({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            // numberAll.value = res.data.data[0].equipment_total;
            // device.value.on = res.data.data[0].equipment_total;
            // device.value.off = 0;
            numberAll.value = '55';
            device.value.on = 55;
            device.value.off = 0;
            // device.value.on = res.data.data[0].online_count;
            // device.value.off = res.data.data[0].offline_count;
            let number = numberAll.value.split("");
            let n = Math.ceil(number.length / 3);
            for (let i = number.length - 1; i >= 0; i--) {
                for (let j = 1; j < n; j++) {
                    if (i == number.length - 3 * j - 1) {
                        numberList.value.unshift(",");
                    }
                }
                numberList.value.unshift(number[i]);
            }
        }
    });
};
const clickDevice = () => {
    emit("device");
};
proxy.$bus.on("deviceNumber", () => {
    obtainData();
});
onMounted(() => {
    obtainData();
});
onUnmounted(() => {
    proxy.$bus.off("deviceNumber");
});
</script>

<style lang="less" scoped>
.digital-devices {
    width: 416px;
    height: 224px;
    padding: 16px;
    // background-color: aquamarine;
    .title {
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
    }
    .boxAll {
        margin: 0 auto;
        margin-top: 8px;
        text-align: center;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        gap: 8px;
        .box_number1 {
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
        }
        .box_number {
            width: 40px;
            height: 48px;
            background: url("../../../../assets/images/card/numberCard.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            color: #fff;
            text-align: center;
            font-family: Digital Numbers;
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 48px; /* 128.571% */
        }
    }
    .content {
        margin-top: 4px;
        display: flex;
        justify-content: space-evenly;
        .inner {
            text-align: center;
            width: 122px;
        }
        .num {
            text-align: center;
            width: 122px;
            height: 122px;
            // background-color: #47EBEB;
            color: #fff;
            font-family: DINPro;
            font-size: 30px;
            font-style: normal;
            font-weight: 700;
            line-height: 122px; /* 126.667% */
        }
        .on {
            background: url("../../../../assets/images/card/on.svg") no-repeat
                center center;
            background-size: cover;
            background-position: center;
        }
        .off {
            background: url("../../../../assets/images/card/off.svg") no-repeat
                center center;
            background-size: cover;
            background-position: center;
        }
        .text {
            width: 96px;
            height: 24px;
            color: #fff;
            text-align: center;
            margin: 0 auto;
            margin-top: 6px;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 157.143% */
        }
        .on-text {
            background: url("../../../../assets/images/card/on-text.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .off-text {
            background: url("../../../../assets/images/card/off-text.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
    }
}
</style>
