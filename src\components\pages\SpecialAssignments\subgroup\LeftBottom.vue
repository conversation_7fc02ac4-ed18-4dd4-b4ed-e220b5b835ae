<template>
    <Card title="特殊作业报备趋势">
        <template v-slot>
            <div class="report-trends-wrapper">
                <div class="linewrap" ref="linewrap"></div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import * as echarts from "echarts";
import Card from "@/components/commenNew/Card.vue";
import {setSize} from "@/assets/js/echartsSetSizeNew.js";
import {
    ref,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
} from "vue";
import { report_trend } from "@/assets/js/api/specialAssignments.js";
import LeftBottomJson from "./json/LeftBottom.json"
const { proxy } = getCurrentInstance();
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let yData = ref([]);
let detailData = ref({});

const processData = (data) => {
    console.log(data,'llalalala');
    
    const months = Object.keys(data).sort();
    xData.value = months;
    
    // Calculate total count for each month
    yData.value = months.map(month => {
        const monthData = data[month];
        return monthData[0]?.sumCount || 0;
    });

    // Store detailed data for tooltip
    detailData.value = data;
};

const initChart = () => {
    option = {
        grid: {
            left: setSize(10),
            right: setSize(25),
            top: setSize(40),
            bottom: setSize(10),
            containLabel: true,
        },
        tooltip: {
            show: true,
            trigger: "axis",
            formatter: function (params) {
                const month = params[0].name;
                const monthData = detailData.value[month];
                if (!monthData) return '';
                
                let result = `${month}&nbsp;&nbsp;&nbsp;&nbsp;报备总数 ${monthData[0]?.sumCount || 0}次<br/>`;
                
                // Filter and sort by recordCount to only show non-zero records
                const validRecords = monthData.sort((a, b) => b.recordCount - a.recordCount);
                
                validRecords.forEach((item,index) => {
                    result += `${item.workType} ${item.recordCount}次<br/>`;
                });
                
                return result;
            },
            backgroundColor: 'rgba(19, 67, 134, 0.6)',
            borderColor: 'rgba(25, 159, 255, 1)',
            extraCssText: `
                backdrop-filter: blur(4px);
                -webkit-backdrop-filter: blur(4px);
                border-radius: 4px;
                padding: 8px 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            `,
            textStyle: {
                color: '#fff',
                fontSize: setSize(12),
                lineHeight: setSize(16),
            }
        },
        legend: {
            show: false,
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    textStyle: {
                        color: "#FFFFFF",
                        fontSize:setSize(12),
                    lineHeight:setSize(16)
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(25, 159, 255, 0.6)",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                },
                data: xData.value,
            },
        ],
        yAxis: [
            {
                type: "value",
                name: "单位：次",
                nameTextStyle: {
                    color: "#fff",
                    fontSize: setSize(12),
                    lineHeight: setSize(16),
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                        fontSize: setSize(12),
                        lineHeight: setSize(16),
                    },
                },
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(26, 159, 255, 0.3)",
                        type: "dashed",
                        width: setSize(1),
                    },
                },
            },
        ],
        series: [
            {
                type: "line",
                showSymbol: false,
                symbolSize: 8,
                smooth: true,
                itemStyle: {
                    normal: {
                        color: "rgba(255, 198, 26, 1)",
                        lineStyle: {
                            color: "rgba(255, 198, 26, 1)",
                            width: 1,
                        },
                    },
                },
                areaStyle: {
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0.5,
                                color: "rgba(255, 198, 26, 0.3)",
                            },
                            {
                                offset: 1,
                                color: "rgba(255, 198, 26, 0)",
                            },
                        ],
                        global: false,
                    },
                },
                data: yData.value,
            },
        ]
    };
};

const getData = () => {
    report_trend().then(res => {
        if (res.data) {
            console.log(LeftBottomJson.data);
            
            // processData(LeftBottomJson.data);
            processData(res.data.data);
            initChart();
            lineChart.setOption(option);
        }
    });
};

onMounted(() => {
    lineChart = echarts.init(linewrap.value);
    getData();
    window.addEventListener("resize", () => {
        lineChart.resize();
    });
});

onBeforeUnmount(() => {
    window.removeEventListener("resize", () => {
        lineChart.resize();
    });
});
</script>

<style lang="less" scoped>
.report-trends-wrapper {
    width: 416px;
    height: 224px;
    padding: 16px;
    .linewrap {
        width: 416px;
        height: 224px;
    }
}
</style>
