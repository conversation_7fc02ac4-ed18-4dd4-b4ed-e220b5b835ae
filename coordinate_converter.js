// WGS84坐标转GCJ-02坐标
function wgs84ToGcj02(lng, lat) {
    if (outOfChina(lng, lat)) {
        return [lng, lat];
    }
    
    let dlat = transformLat(lng - 105.0, lat - 35.0);
    let dlng = transformLng(lng - 105.0, lat - 35.0);
    
    let radlat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radlat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    let sqrtmagic = Math.sqrt(magic);
    
    dlat = (dlat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtmagic) * Math.PI);
    dlng = (dlng * 180.0) / (6378245.0 / sqrtmagic * Math.cos(radlat) * Math.PI);
    
    return [lng + dlng, lat + dlat];
}

// 判断坐标是否在中国范围内
function outOfChina(lng, lat) {
    return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
}

// 转换纬度
function transformLat(x, y) {
    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
    ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin(y / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(y / 12.0 * Math.PI) + 320 * Math.sin(y * Math.PI / 30.0)) * 2.0 / 3.0;
    return ret;
}

// 转换经度
function transformLng(x, y) {
    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
    ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin(x / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(x / 12.0 * Math.PI) + 300.0 * Math.sin(x / 30.0 * Math.PI)) * 2.0 / 3.0;
    return ret;
}

// 修改处理坐标串的函数
function convertCoordinates(coordString) {
    return coordString.split(' ').map(coord => {
        const [lng, lat] = coord.split(',');
        const [gcjLng, gcjLat] = wgs84ToGcj02(parseFloat(lng), parseFloat(lat));
        return [gcjLng, gcjLat];
    });
}

// 您的坐标串
const wgs84Coords = "115.100752936272,37.965991818172,0 115.102708979269,37.9662188360386,0 115.108306581565,37.9668142855822,0 115.108600280011,37.9632683391622,0 115.114485136899,37.9640103731496,0 115.11648201594,37.9633482729116,0 115.119879859165,37.9624256858432,0 115.121296826096,37.9574531228352,0 115.129114329905,37.9579143226411,0 115.134376389092,37.9543029761836,0 115.134280098448,37.9496581184362,0 115.134410704278,37.9484053347636,0 115.121373718914,37.9450510415608,0 115.121260324488,37.9454910742636,0 115.110396319093,37.9441262479968,0 115.109460159494,37.9488686514597,0 115.103952052785,37.9490359110372,0 115.100488756905,37.949039830824,0 115.10075265868,37.9659732262932,0 115.100752936272,37.965991818172,0";

// 转换并输出结果
console.log(JSON.stringify(convertCoordinates(wgs84Coords))); 