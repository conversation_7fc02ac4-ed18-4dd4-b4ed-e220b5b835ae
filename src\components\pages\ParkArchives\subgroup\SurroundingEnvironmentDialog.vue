<template>
<div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">周边环境详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog('enterprise')"
                    />
                </div>
                <div class="dialog-content">
                    
                   <div class="container">
            <div class="table">
                <table>
                    <tr>
                        <td class="title1">
                            <span class="title">企业名称</span>
                        </td>
                        <td class="text1">
                            <div
                                class="text"
                                :title="dialogData.enterprise_name"
                            >
                                {{ dialogData.enterprise_name }}
                            </div>
                        </td>
                        <td class="title1">
                            <span class="title">敏感目标名称</span>
                        </td>
                        <td class="text1">
                            <div
                                class="text"
                                :title="dialogData.sensitive_name"
                            >
                                {{ dialogData.sensitive_name }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="title1">
                            <span class="title">敏感目标描述</span>
                        </td>
                        <td class="text1">
                            <div
                                class="text"
                                :title="dialogData.sensitive_description"
                            >
                                {{ dialogData.sensitive_description }}
                            </div>
                        </td>
                        <td class="title1">
                            <span class="title">负责人</span>
                        </td>
                        <td class="text1">
                            <div class="text" :title="dialogData.principal">
                                {{ dialogData.principal }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="title1">
                            <span class="title">联系电话</span>
                        </td>
                        <td class="text1">
                            <div class="text" :title="dialogData.contactNumber">
                                {{ dialogData.contactNumber }}
                            </div>
                        </td>
                        <td class="title1"><span class="title"></span></td>
                        <td class="text1">
                            <div class="text"></div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
    
</template>

<script setup>
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
    computed,
} from "vue";
import { sensitive_target_info } from "../../../../assets/js/api/parkArchives";
var jessibuca = null;
const img = ref(null);
const clickId = ref();
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const dialogData = ref({
    enterprise_name: "",
    sensitive_name: "",
    sensitive_description: "",
    principal: "",
    contactNumber: "",
});
watch(
    pickId,
    (a, b) => {
        console.log(a, b);

        clickId.value = a;
        if (a != undefined) {
            sensitive_target_info({ id: a }).then((res1) => {
                // console.log(res1);
                if (
                    res1.data.success &&
                    res1.data.data &&
                    res1.data.data.length
                ) {
                    dialogData.value = res1.data.data[0];
                }
            });
        }
    },
    {
        immediate: true,
    },
);
const emit = defineEmits(["closeDialog"]);

const closeDialog = () => {
    emit("closeDialog", "surrounding");
};
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 460px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 460px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 344px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}

.container {
    // width: 792px;
    // height: 108px;
    // padding: 16px 24px;
    // background-color: aquamarine;
    .table {
        width: 792px;
        height: 108px;
        // background-color: antiquewhite;
        table {
            border-collapse: collapse;
        }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }

        .title1 {
            width: 220px;
            height: 36px;
            padding: 0 12px;
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 157.143% */
            background-color: rgba(48, 171, 232, 0.1);
        }

        .text1 {
            width: 236px;
            height: 36px;
            padding: 0 12px;
            color: #fff;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px; /* 157.143% */
            .text {
                width: 236px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .separate {
        width: 792px;
        height: 2px;
        background: url("../../../../assets/images/dialog/separate.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
    .btns {
        width: 792px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        color: #fff;
        text-align: center;
        text-shadow: 0px 0px 8px rgba(77, 57, 0, 0.6);
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px; /* 150% */
        .type-active-btn {
            width: 144px;
            height: 40px;
            background: url("../../../../assets/images/dialog/type-active-btn.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .type-btn {
            width: 144px;
            height: 40px;
            background: url("../../../../assets/images/dialog/type-btn.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
    }
    .video {
        width: 792px;
        height: 444px;
        background: rgba(48, 171, 232, 0.15);
        margin-top: 16px;
        .img {
            width: 792px;
            height: 444px;
        }
    }
    .monitor-data {
        width: 792px;
        height: 354px;
        // background: rgba(48, 171, 232, 0.15);
        margin-top: 16px;
        .monitor-btns {
            width: 792px;
            height: 28px;
            //   background: url("../../../../assets/images/dialog/emergency-archives-liner.svg")
            //     no-repeat center center;
            //   background-size: cover;
            //   background-position: center;
            .center {
                width: 615px;
                height: 22px;
                margin: 16px auto 12px;
                // background-color: antiquewhite;
                display: flex;
                justify-content: space-between;
                .left-arrow {
                    width: 16px;
                    height: 16px;
                    margin: auto 0;
                    background: url("../../../../assets/images/left-arrow1.svg")
                        no-repeat;
                    background-size: cover;
                    background-position: center;
                }
                .center-content {
                    width: 551px;
                    height: 22px;
                    //   background-color: aqua;
                    .event-btn-wrap {
                        width: 87px;
                        color: #fff;
                        text-align: center;
                        font-family: Noto Sans SC;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                    }
                    .selectActive {
                        color: #47ebeb;
                        text-align: center;
                        font-family: Noto Sans SC;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                    }
                }
                .right-arrow {
                    width: 16px;
                    height: 16px;
                    margin: auto 0;
                    background: url("../../../../assets/images/right-arrow1.svg")
                        no-repeat;
                    background-size: cover;
                    background-position: center;
                }
            }
            .btns {
                width: 320px;
                height: 28px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                color: #fff;
                text-align: center;
                text-shadow: 0px 0px 8px rgba(8, 48, 69, 0.6);
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 133.333% */
                .monitor-active-btn {
                    width: 96px;
                    height: 28px;
                    background: url("../../../../assets/images/dialog/device-active-btn.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
                .monitor-btn {
                    width: 96px;
                    height: 28px;
                    background: url("../../../../assets/images/dialog/device-btn.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
            }
        }
        .linewrap {
            width: 792px;
            height: 310px;
            margin-top: 16px;
        }
    }
    .alarm {
        width: 792px;
        height: 274px;
        // background: rgba(48, 171, 232, 0.15);
        margin-top: 16px;

        .tablebox {
            //表格四个边框的颜色
            border: 1px solid #30abe8 !important;
            th.el-table__cell {
                border: none !important;
            }
        }
        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            border-bottom: 1px solid #30abe8 !important;
            height: 36px;
            background-color: rgba(48, 171, 232, 0.1) !important;
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            // border-color: #30ABE8 !important;
        }
        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: 1px solid #30abe8 !important;
                border-right: 1px solid #30abe8 !important;
            }
        }
        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
            // border-color: #30ABE8 !important;
        }
        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
        :deep(.el-table__cell) {
            border-right: 1px solid #30abe8 !important ;
            border-bottom: 1px solid #30abe8 !important  ;
        }
        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }
        .pagination {
            margin-top: 16px;
        }
        :deep(.el-pagination .el-pager li:not(.disabled)) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        :deep(.el-pagination .el-pager li:not(.disabled).active) {
            // background-color:green;/*进行修改选中项背景和字体*/
            // color:#fff;
        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 2px;
            border: 1px solid #47ebeb;
            background: linear-gradient(
                180deg,
                rgba(71, 235, 235, 0) 50%,
                rgba(71, 235, 235, 0.45) 100%
            );
            color: #47ebeb;
            text-align: center;

            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination .btn-next) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination .btn-prev) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination__total) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
    .hide {
        visibility: hidden;
    }
}
</style>
