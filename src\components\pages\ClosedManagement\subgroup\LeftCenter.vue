<template>
    <Card title="危化品运输车与普通车出入园统计">
        <template v-slot>
            <div class="chart-wrapper">
                <div class="linewrap" ref="linewrap" v-show="!nonFlag"></div>
                <div class="all-wrapper" v-show="nonFlag">
                    <div class="img-no">暂无数据</div>
                </div>
            </div>
</template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import * as echarts from "echarts";
import { setSize } from "@/assets/js/echartsSetSizeNew";
import { carStatistics } from "@/assets/js/api/closedManagement";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
//暂无数据标志
const nonFlag = ref(false);
const timer = ref(null);
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let chemicalData = ref([]);
let carData = ref([]);
let employee = ref([]);
let heavyTruck = ref([]);
let management = ref([]);
let visitorCar = ref([]);
const initChart = () => {
    option = {
        tooltip: {
            trigger: "axis",
            confine: true, // 解决悬浮提示被遮住的问题
            formatter: function (params) {
                var res =
                    "<div style='padding:0 12px;height:24px;line-height:24px;'><p>" +
                    params[0].name +
                    " </p></div>";
                for (var i = 0; i < params.length; i++) {
                    //因为是个数组，所以要遍历拿到里面的数据，并加入到tooltip的数据内容部分里面去
                    res += `<div style="padding:0 12px;">
                  <span style="display:inline-block;margin-right:4px;border-radius:2px;width:10px;height:10px;background-color:${[
                      params[i].color, // 默认是小圆点，我们将其修改成有圆角的正方形，这里用的是模板字符串。并拿到对应颜色、名字、数据
                  ]};"></span>
                  ${params[i].seriesName}
                  ${params[i].data} ${i == 0 ? "辆" : "辆"}
                </div>`;
                }
                return res; // 经过这么一加工，最终返回出去并渲染，最终就出现了我们所看的效果
            },
            backgroundColor: 'rgba(19, 67, 134, 0.6)',
            borderColor: 'rgba(25, 159, 255, 1)',
            extraCssText: `
                backdrop-filter: blur(4px);
                -webkit-backdrop-filter: blur(4px);
                border-radius: 4px;
                padding: 8px 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            `,
            textStyle: {
                color: '#fff',
                fontSize: setSize(12),
                lineHeight: setSize(16),
            }
        },
        xAxis: {
            type: "category",
            data: xData.value,
            axisLabel: {
                textStyle: {
                    color: "rgba(255, 255, 255, 1)",
                    fontSize:setSize(12),
                    lineHeight:setSize(16)
                },
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: "rgba(25, 159, 255, 0.6)",
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: "#195384",
                },
            },
        },
        yAxis: [
            {
                type: "value",
                // splitNumber: 6, //设置坐标轴的分割段数
                splitLine: {
                    //去除网格线
                    show: true,
                    lineStyle: {
                        color: "rgba(26, 159, 255, 0.3)",
                        type: "dashed",
                        width: setSize(1),
                    },
                },
                axisLine: {
                    //y轴线的颜色以及宽度
                    show: false,
                    lineStyle: {
                        color: "#ffffff",
                        width: 1,
                        type: "solid",
                    },
                },
                axisLabel: {
                    // 设置y轴的文字的样式
                    textStyle: {
                        color: "#FFFFFF",
                        fontSize: setSize(12),
                        lineHeight: setSize(16),
                    },
                },
            },
        ],
        legend: {
            data: ["企业员工", "三方单位", "访客", "危化品"],
    show: true,
    x: "center", // 水平居中
    y: "top",   // 垂直居上
    itemWidth: setSize(12), // 颜色方块的宽度
    itemHeight: setSize(8), // 颜色方块的高度
    itemGap: setSize(16), // 图例项之间的间距
    textStyle: {
        color: "#fff",
        fontSize: 12,
        padding: [0, 0, 0, 6], // 调整文字与方块的距离（左间距）
    },
    icon: "rect", // 使用矩形（长方形）作为图例图标
        },

        series: [
            {
                name: "企业员工",
                showSymbol: false,
                data: employee.value,
                type: "line",
            },
            {
                name: "三方单位",
                showSymbol: false,
                data: management.value,
                type: "line",
            },
            {
                name: "访客",
                showSymbol: false,
                data: visitorCar.value,
                type: "line",
            },
            {
                name: "危化品",
                showSymbol: false,
                data: heavyTruck.value,
                type: "line",
            },
        ],
        grid: {
            left: setSize(10),
            right: setSize(10),
            top: setSize(40),
            bottom: setSize(0),
            containLabel: true,
        },
        title: [
            // 因为是两个y轴，所以title写成数组的形式，进行配置
            {
                // title为标题部分，有一级标题text，二级标题subtext。这里我们使用二级标题，再修改一下这个二级标题的位置即可出现我们想要的效果了，当然样式也可以通过title.subtextStyle去配置
                subtext: "辆",
                left: 8, // 距离左边位置
                top: 0, // 距离上面位置
                subtextStyle: {
                    // 设置二级标题的样式
                    color: "#ffffff",
                },
            },
            //   {
            //     // title为标题部分，有一级标题text，二级标题subtext。这里我们使用二级标题，再修改一下这个二级标题的位置即可出现我们想要的效果了，当然样式也可以通过title.subtextStyle去配置
            //     subtext: "辆",
            //     right: 8, // 距离左边位置
            //     top: 0, // 距离上面位置
            //     subtextStyle: {
            //       // 设置二级标题的样式
            //       color: "#ffffff",
            //     },
            //   },
        ],
        color: ["rgba(128, 234, 255, 1)", "rgba(11, 218, 46, 1)","rgba(255, 198, 26, 1)","rgba(255, 51, 51, 1)"], // 控制折线图的颜色
    };
};
const getData = () => {
    carStatistics({}).then(res => {
        console.log(res);
        if(res.data&&res.data.data){
            xData.value = res.data.data.employee.map(item => item.date);
            employee.value = res.data.data.employee.map(item => item.num);
            management.value = res.data.data.management.map(item => item.num);
            visitorCar.value = res.data.data.visitorCar.map(item => item.num);
            heavyTruck.value = res.data.data.heavyTruck.map(item => item.num);   
            console.log(xData.value,employee.value,management.value,visitorCar.value,heavyTruck.value,'lalal');

            initChart();
                lineChart.setOption(option);
                window.addEventListener("resize", () => {
                    lineChart.resize();
                });
        }
    })
}
onMounted(() => {
    // xData = [];
    // chemicalData = [];
    // carData = [];
    // lineChart = echarts.init(linewrap.value);
    // security_car_statistics({ "": "" }).then((res) => {
    //     if (res.data.success && res.data.data && res.data.data.length) {
    //         nonFlag.value = false;
    //         res.data.data.forEach((item) => {
    //             xData.push(item.date);
    //             chemicalData.push(item.heavy_car);
    //             carData.push(item.car);
    //         });
    //     } else {
    //         nonFlag.value = true;
    //     }
    //     initChart();
    //     lineChart.setOption(option);
    //     window.addEventListener("resize", () => {
    //         lineChart.resize();
    //     });
    // });
    lineChart = echarts.init(linewrap.value);

    getData()
   
});
onBeforeUnmount(() => {
    if (lineChart) {
        lineChart.dispose();
    }
});
</script>

<style lang="less" scoped>
.chart-wrapper{
    width: 416px;
    height: 224px;
    padding: 16px;
}
.linewrap {
    width: 416px;
        height: 224px;
}
.all-wrapper {
    width: 416px;
        height: 224px;
    display: flex;
    align-items: center;
    justify-content: center;
    .img-no {
        margin: auto 0;
        color: #fff;
        text-align: center;
        font-family: "Noto Sans SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
    }
}
</style>
