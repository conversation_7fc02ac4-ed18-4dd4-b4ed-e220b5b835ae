import { fetch } from "../request";
//风险分析单元
export const unit = (params) =>fetch("/api-v2/safey/unit", { ...params }, "POST");
//企业重大危险源分析
export const danger = (params) =>fetch("/api-v2/safey/danger", { ...params }, "POST");
//隐患治理情况-一般
export const report = (params) =>fetch("/api-v2/safety/report", { ...params }, "POST");
//隐患治理情况-严重
export const dangerReport = (params) =>fetch("/api-v2/safety/danger-report", { ...params }, "POST");
//风险管控措施
export const control = (params) => fetch("/api-v2/safety/control", { ...params }, "POST");
//重大危险源企业分页查询
export const getMajorDanger = (params) => fetch("/safety-v2/major-danger-source/select-page", { ...params }, "GET");
//重大危险源企业详情
export const majorDangerDetail = (params) => fetch(`/safety-v2/major-danger-source/select-detail/${params}`, {}, "GET");
//风险分析单元分页查询
export const getUnit = (params) => fetch("/safety-v2/risk-unit/select-page", { ...params }, "GET");
//风险分析单元详情
export const unitDetail = (params) => fetch(`/safety-v2/risk-unit/select-detail/${params}`, {}, "GET");
//风险管控措施分页查询
export const getControl = (params) => fetch("/safety-v2/control-measure/select-page", { ...params }, "GET");

// 风险分析统计
export const riskAnalysisOverview = (params) =>fetch("/api-v2/safety/risk-analysis-overview", { ...params }, "POST");
// 风险等级
export const riskLevelCount = (params) =>fetch("/api-v2/safety/risk-level-count", { ...params }, "POST");
// 隐患排查情况统计
export const dangerInvestigationStatistics = (params) =>fetch("/api-v2/safety/danger-investigation-statistics", { ...params }, "POST");
// 运行效果检查
export const onlineChackStatistics = (params) =>fetch("/api-v2/safety/online-chack-statistics", { ...params }, "POST");
// 企业风险数量排名
export const corporateRiskRanking = (params) =>fetch("/api-v2/safety/corporate-risk-ranking", { ...params }, "POST");
// 隐患治理情况统计
export const dangerReformStatistics = (params) =>fetch("/api-v2/safety/danger-reform-statistics", { ...params }, "POST");
