<template>
    <Card title="园区季度总产值">
        <template v-slot>
            <div class="barwrap" ref="barwrap"></div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { energy_output_month } from "../../../../assets/js/api/smartEnergy";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
let option;
let barwrap = ref(null);
let barChart;
let echartData = ref([]);
let yList = ref([]);
let xData = ref([]);
const initChart = () => {
    let color = [
        "#CC1CAA",
        "#8D67FF",
        "#00FFFF",
        "#48DE13",
        "#FFC516",
        "#DC3E14",
        "#8E16F8",
    ];

    let dom = 400;
    let barWidth = dom / xData.length / 2;
    let colors = [];
    for (let i = 0; i < xData.length; i++) {
        colors.push({
            type: "linear",
            x: 0,
            x2: 1,
            y: 0,
            y2: 0,
            colorStops: [
                {
                    offset: 0,
                    color: "rgba(48, 171, 232, 0.15)", // 最左边
                },
                {
                    offset: 0.5,
                    color: "#30ABE8", // 左边的右边 颜色
                },
                {
                    offset: 0.5,
                    color: "#30ABE8", // 右边的左边 颜色
                },
                {
                    offset: 1,
                    color: "rgba(48, 171, 232, 0.15)",
                },
            ],
        });
    }
    console.log(colors);
    option = {
        //提示框
        tooltip: {
            trigger: "axis",
            formatter: "{b} : {c}",
            axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
        },
        /**区域位置*/
        grid: {
            left: 10,
            right: 20,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        //X轴
        xAxis: {
            data: xData,
            type: "category",
            axisLine: {
                show: true,
                lineStyle: {
                    color: " rgba(48, 171, 232, 0.60)",
                },
                // symbol: ['none', 'arrow'],
                // symbolOffset: [0, 25]
            },
            splitLine: {
                show: false,
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                textStyle: {
                    color: "#FFFFFF",
                    fontSize: 12,
                    lineHeight: 18,
                },
            },
        },
        yAxis: {
            type: "value",
            name: "单位：万元",
            nameTextStyle: {
                //y轴上方单位的颜色
                color: "#fff",
            },
            show: true,
            // splitNumber: 4,
            axisLine: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: "rgba(255, 255, 255, 0.15)",
                },
            },
            axisLabel: {
                textStyle: {
                    color: "#FFFFFF",
                    fontSize: 12,
                    lineHeight: 18,
                },
            },
        },
        series: [
            {
                type: "bar",
                barWidth: barWidth,
                itemStyle: {
                    normal: {
                        color: function (params) {
                            return colors[params.dataIndex % 7];
                        },
                    },
                },
                label: {
                    show: false,
                    position: [barWidth / 2, -(barWidth + 20)],
                    color: "#ffffff",
                    fontSize: 14,
                    fontStyle: "bold",
                    align: "center",
                },
                data: yList,
            },
            {
                z: 2,
                type: "pictorialBar",
                data: yList,
                symbol: "diamond",
                symbolOffset: [0, "50%"],
                symbolSize: [barWidth, barWidth * 0.5],
                itemStyle: {
                    normal: {
                        color: function (params) {
                            return colors[params.dataIndex % 7];
                        },
                    },
                },
            },
            {
                z: 3,
                type: "pictorialBar",
                symbolPosition: "end",
                data: yList,
                symbol: "diamond",
                symbolOffset: [0, "-50%"],
                symbolSize: [barWidth, barWidth * 0.5],
                itemStyle: {
                    normal: {
                        borderWidth: 0,
                        color: function (params) {
                            console.log(colors[params.dataIndex % 7]);
                            if (colors[params.dataIndex % 7] !== undefined)
                                return colors[params.dataIndex % 7]
                                    .colorStops[0].color;
                        },
                    },
                },
            },
        ],
    };
};
onMounted(() => {
    yList = [];
    xData = [];
    barChart = echarts.init(barwrap.value);
    energy_output_month({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            res.data.data.forEach((item) => {
                if (item.quarter == "1") {
                    xData.push("第一季度");
                } else if (item.quarter == "2") {
                    xData.push("第二季度");
                } else if (item.quarter == "3") {
                    xData.push("第三季度");
                } else {
                    xData.push("第四季度");
                }
                yList.push(
                    item.total_energy == null ? 0 : Number(item.total_energy),
                );
            });
            initChart();
            barChart.setOption(option);
            window.addEventListener("resize", () => {
                barChart.resize();
            });
        }
    });
});
onBeforeUnmount(() => {
    // loopShowTooltip(barChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (barChart) {
        // setTimeout(() => {
        barChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.barwrap {
    width: 416px;
    height: 224px;
    padding: 16px;
}
</style>
