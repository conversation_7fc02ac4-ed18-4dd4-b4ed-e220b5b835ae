kind: Deployment
apiVersion: apps/v1
metadata:
  name: ioc-v1
  namespace: jinzhou-park-dev
  labels:
    app: ioc
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ioc
      version: v1
  template:
    metadata:
      labels:
        app: ioc
        version: v1
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: volume-n37m9x
          configMap:
            name: ioc
            defaultMode: 420
        - name: volume-ow1ykd
          persistentVolumeClaim:
          claimName: ioc-data
      containers:
        - name: container-y5ug1e
          image: 'local.harbor.com/park-project/$IMAGE_NAME:$IMAGE_TAG'
          ports:
            - name: http-80
              containerPort: 80
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: volume-n37m9x
              readOnly: true
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf
              - name: volume-ow1ykd
              mountPath: /usr/share/nginx/html/static/
              subPath: static
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
