<template>
    <Card title="抽查检查统计">
        <template v-slot>
            <div class="spot-check-wrapper"  style="cursor: pointer;" @click="openDialog('specialWorkCheckDialogShow',3)">
                <div class="spot-check-inner">
                    <div class="top-num top-num-month">{{monthNum}}</div>
                    <div class="top-name top-name-month">本月检查次数</div>
            </div>
            <div class="spot-check-inner" style="cursor: pointer;" @click="openDialog('specialWorkCheckDialogShow',4)">
                <div class="top-num top-num-year">{{yearNum}}</div>
                    <div class="top-name top-name-year">本年检查次数</div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits
} from "vue";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val,val2) => {
    proxy.$emit('openDialog',val,val2);
}
import { check } from "@/assets/js/api/specialAssignments.js";
let monthNum = ref(0);
let yearNum = ref(0);
const getData = () => {
    check().then(res => {
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            monthNum.value = res.data.data[0].monthly_count?res.data.data[0].monthly_count:0;
            yearNum.value = res.data.data[0].yearly_count?res.data.data[0].yearly_count:0;
        }
    });
};
onMounted(() => {
    getData();
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>
.spot-check-wrapper {
    width: 320px;
    height: 176px;
    padding: 34px 64px;
    display: flex;
    justify-content: space-between;
    .spot-check-inner{
        
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .top-num{
        width: 140px;
        height: 140px;
        font-family: DIN Alternate;
font-weight: 700;
font-size: 32px;
line-height: 140px;
letter-spacing: 0%;
text-align: center;
color: #FFFFFF;

    }
    .top-num-month{
        background: url("@/assets/newImages/SpecialAssignments/yellow-round.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }
    .top-num-year{
        background: url("@/assets/newImages/SpecialAssignments/green-round.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }
    .top-name{
        width: 120px;
        height: 28px;
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 28px;
color: #FFFFFF;
margin:auto;
text-align: center;

    }
    .top-name-month{
        background: url("@/assets/newImages/SpecialAssignments/yellow-card.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }
    .top-name-year{
        background: url("@/assets/newImages/SpecialAssignments/green-card.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }
}

</style>
