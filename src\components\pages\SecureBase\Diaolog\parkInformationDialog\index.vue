<template>
    <div class="dialog-all-wrapper">
        <div class="park-information-dialog-wrapper">
            <div class="park-information-dialog border">
                <div class="dialog-header">
                    <div class="header-title">园区信息</div>
                    <img src="@/assets/newImages/Dialog/dialog-close.svg" class="header-close" @click="closeDialog">
                </div>
                <div class="dialog-content">
                    <div class="slect-tabs">
                        <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="clickTab(item)">
                            <div class="tab-item-text" :class="{'active-tab-item':currentTab === item}" >{{ item }}</div>
                            <div class="tab-item-line" v-if="currentTab === item"></div>
                        </div>
                    </div>
                    <div class="tab-content">
                        <parkBasic v-if="currentTab === '基本信息'"></parkBasic>
                        <establishInfo v-if="currentTab === '设立信息'"></establishInfo>
                        <industryLocation v-if="currentTab === '产业定位'"></industryLocation>
                        <planApproval v-if="currentTab === '规划审批'"></planApproval>
                        <awardFile v-if="currentTab === '获奖档案'"></awardFile>
                        <patentFile v-if="currentTab === '专利档案'"></patentFile>
                    </div>
                </div>
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits } from "vue";
const emit = defineEmits(["closeDialog"]);

import parkBasic from './parkBasic.vue'
//设立信息
import establishInfo from './establishInfo.vue'
//产业定位
import industryLocation from './industryLocation.vue'
//规划审批
import planApproval from "./planApproval.vue";
//获奖档案
import awardFile from "./awardFile.vue";
//专利档案
import patentFile from "./patentFile.vue"
//顶部tab
let topTabs = ref(['基本信息','设立信息','产业定位','规划审批','获奖档案','专利档案']);
let currentTab = ref('基本信息');
const clickTab = (item) => {
    currentTab.value = item;
}
onMounted(() => {});
const closeDialog = ()=>{
    emit('closeDialog','parkInformationDialogShow');

}
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .park-information-dialog-wrapper {
        width: 1160px;
        height: 720px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .park-information-dialog {
        width: 1160px;
        height: 720px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,/* 左上角开始 */ 
            calc(100% - 10px) 0, /* 右上角左侧 */ 
            100% 10px,/* 右上角下侧 */
            100% calc(100% - 10px),/* 右下角上侧 */ 
            calc(100% - 10px) 100%, /* 右下角左侧 */ 
            10px 100%,/* 左下角右侧 */ 
            0 calc(100% - 10px),/* 左下角上侧 */ 
            0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 1128px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title{
            font-family: MStiffHei PRC;
font-weight: 400;
font-size: 18px;
line-height: 26px;
letter-spacing: 0%;
background: 
        linear-gradient(180deg, rgba(26, 159, 255, 0.45) 20%, rgba(26, 159, 255, 0) 80%),
        linear-gradient(180deg, #FFFFFF 15.63%, #2AD9FF 87.5%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
        }
        .header-close{
            width: 16px;
height: 16px;

        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content{
        width: 1120px;
        height: 624px;
        padding: 20px;
        .slect-tabs{
            width: 1120px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 20px;
            .tab-item{
               
height: 36px;
padding-top: 12px;
font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
text-align: center;
color:rgba(128, 234, 255, 1);

            }
            .tab-item-line{
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin:auto;
                margin-top: 10px;
            }
            .active-tab-item{
                color:rgba(255, 198, 26, 1);
            }
        }
        .tab-content{
            padding:16px 0;
        }

    }
    
}
</style>
