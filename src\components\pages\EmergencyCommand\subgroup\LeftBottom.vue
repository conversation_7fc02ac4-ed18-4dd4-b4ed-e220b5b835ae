<template>
    <Card title="应急预案">
        <template v-slot>
    <div class="linewrap">
        <div class="topNumAll">
            <div class="topNum">
                <div class="fontTop">预案总数</div>
                <div class="top_numberCard">
                    <div
                        v-for="item in numberString"
                        :key="item"
                        class="top_numberCard_little"
                    >
                        {{ item }}
                    </div>
                </div>
                <div class="fontTop1">项</div>
            </div>
        </div>
        <!--    <div  ref="linewrap"></div>-->
        <div class="piewrap" ref="piewrap"></div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";

import { ref, reactive, onMounted, onBeforeUnmount } from "vue";

let numberString = ref(null);
import { getplan } from "../../../../assets/js/api/api";

const num = reactive({
    total: 0,
    normal: 0,
    abnormal: 0,
});
let option;
let piewrap = ref(null);
let pieChart;
let echartData = ref([]);
const getinfo = () => {
    pieChart = echarts.init(piewrap.value);
    getplan({}).then((res) => {
        console.log(res, "测试接口");
        numberString.value = res.data.data[0].total;
        res.data.data.forEach((item) => {
            item.name = item.plan_name;
            item.value = item.count;
        });
        echartData.value = res.data.data;
        console.log(res.data.data, "返回的数据");
        // echartData.value = [
        //   {
        //     name: '自然灾害',
        //     value: 5,
        //     percent: '10%'
        //   },
        //   {
        //     name: '事故灾害',
        //     value: 10,
        //     percent: '20%'
        //   },
        //   {
        //     name: '公共卫生',
        //     value: 20,
        //     percent: '40'
        //   },
        //   {
        //     name: '社会安全',
        //     value: 5,
        //     percent: '10%'
        //   },
        //   {
        //     name: '其他突发',
        //     value: 10,
        //     percent: '20%'
        //   },
        //
        // ];
        initChart();
    });
};
const initChart = () => {
    let color = [
        "#30ABE8",
        "#27F85D",
        "#FFB54C",
        "#ff4c4c",
        "#c0ffb2",
        "#EB4747",
        "#47EBEB",
        "#C0FFB3",
        "#FF791A",
        "#31ABE8",
    ];

    let formatNumber = function (num) {
        let reg = /(?=(\B)(\d{3})+$)/g;
        return num.toString().replace(reg, ",");
    };
    // let total = echartData.value.reduce((a, b) => {
    //   return a + b.count * 1
    // }, 0);

    option = {
        color: color,
        tooltip: {
            trigger: "item",
        },
        grid: {
            top: "1px",
            right: "1px",
            bottom: "1px",
            left: "2px",
        },
        legend: {
            type: "scroll",
            orient: "vertical",
            icon: "rect",
            x: "72%",
            y: "bottom",
            itemWidth: 12,
            itemHeight: 12,
            align: "left",
            height: 150,
            textStyle: {
                rich: {
                    name: {
                        fontSize: 14,
                        // padding: [0, 10, 0, 4],
                        fontWeight: 400,
                        color: "#ffffff",
                        lineHeight: 22,
                    },
                },
            },
            formatter: function (name) {
                let res = echartData.value.filter((v) => v.name === name);
                res = res[0] || {};
                return "{name|" + name + "}";
            },
            // data: legendName
        },
        series: [
            {
                type: "pie",
                radius: ["35%", "57%"],
                center: ["45%", "50%"],
                data: echartData.value,
                hoverAnimation: false,
                itemStyle: {
                    // normal: {
                    //     borderColor: '#fff',
                    //     borderWidth: 2
                    // }
                },
                labelLine: {
                    normal: {
                        length: 20,
                        length2: 30,
                        // lineStyle: {
                        //     color: '#e6e6e6'
                        // }
                    },
                },
                label: {
                    normal: {
                        formatter: (params) => {
                            console.log(params, "每一项");
                            return (
                                // '{value|' + params.percent + '%\n}{name|' +
                                "{value|" +
                                (
                                    (params.data.count / params.data.total) *
                                    100
                                ).toFixed(2) +
                                "%\n}{name|" +
                                params.data.count +
                                "项}"
                            );
                        },
                        // padding: [0 , -100, 25, -100],
                        rich: {
                            name: {
                                fontSize: 12,
                                // padding: [0, 10, 0, 4],
                                fontWeight: 400,
                                color: "#ffffff",
                                lineHeight: 18,
                            },
                            value: {
                                fontSize: 20,
                                fontWeight: 700,
                                color: "#ffffff",
                                lineHeight: 28,
                            },
                        },
                    },
                },
            },
        ],
    };
    pieChart.setOption(option);
    window.addEventListener("resize", () => {
        pieChart.resize();
    });
};

onMounted(() => {
    // echartData.value = [
    //   {
    //     name: '自然灾害',
    //     value: 5,
    //     percent: '10%'
    //   },
    //   {
    //     name: '事故灾害',
    //     value: 10,
    //     percent: '20%'
    //   },
    //   {
    //     name: '公共卫生',
    //     value: 20,
    //     percent: '40'
    //   },
    //   {
    //     name: '社会安全',
    //     value: 5,
    //     percent: '10%'
    //   },
    //   {
    //     name: '其他突发',
    //     value: 10,
    //     percent: '20%'
    //   },
    //
    // ];
    getinfo();
});
onBeforeUnmount(() => {
    if (pieChart) {
        // setTimeout(() => {
        pieChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.topNumAll {
    display: flex;
    justify-content: center;
    text-align: center;
}
.topNum {
    color: #fff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    display: flex;
}
.fontTop {
    width: 80px;
    /*line-height: 66px;*/
    /*margin-right:8px ;*/
    color: #fff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 40px; /* 150% */
}
.fontTop1 {
    margin-right: 8px;

    margin-left: 6px;
    color: #fff;
    text-align: center;
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 40px; /* 150% */
}
.top_numberCard {
    /* width: 416px; */
    height: 40px;
    display: flex;
    align-items: flex-start;
    gap: 6px;
    /*margin-top: 16px;*/
}
.top_numberCard_little {
    background: url("../../../../assets/images/command/left_bottom_1.svg")
        no-repeat;
    width: 28px;
    height: 34px;
    background-size: cover; /* 将背景图等比例缩放并完全覆盖盒子 */
    background-position: center; /* 将背景图居中显示 */
    color: #fff;
    text-align: center;
    font-family: Digital Numbers;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 34px; /* 140% */
}
.linewrap {
    width: 416px;
    height: 224px;
    padding: 16px;
}
.piewrap {
    width: 416px;
    height: 188px;
}
</style>
