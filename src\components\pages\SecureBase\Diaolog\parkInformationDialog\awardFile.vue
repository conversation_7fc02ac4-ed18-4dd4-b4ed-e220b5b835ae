<template>
    <div class="bottom">
        <el-table class="tablebox" :data="tableData" style="width: 100%">
            <el-table-column
                prop="enterpriseName"
                label="企业名称"
                show-overflow-tooltip
            />
            <el-table-column
                prop="awardName"
                label="获奖名称"
                show-overflow-tooltip
            />
            <el-table-column
                prop="issueInstitution"
                label="颁布机构"
                show-overflow-tooltip
            />
            <el-table-column
                prop="issueDate"
                label="颁发日期"
                show-overflow-tooltip
            />
            <el-table-column
                prop="awardDescription"
                label="奖项说明"
                show-overflow-tooltip
            />
            <el-table-column
                prop="awardFile"
                label="奖项附件"
                show-overflow-tooltip
            >
            <template #default="scope">
                <span
                    v-if="scope.row.awardFileObj && scope.row.awardFileObj.fileName"
                    class="file-download-link"
                    @click="downloadFile(scope.row.awardFileObj)"
                    :title="scope.row.awardFileObj.fileName"
                >
                    {{ scope.row.awardFileObj.fileName }}
                </span>
                <span v-else>-</span>
            </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            background
            v-model:currentPage="pageNum"
            :page-size="pageSize"
            @current-change="handleCurrentChange"
            layout="->,total, prev, pager, next"
            :total="total"
        />
    </div>
</template>

<script setup>
import {
    award_file_page,
    dict_allList,
} from "@/assets/js/api/dialog/secureBase";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    getCurrentInstance,
} from "vue";
const { proxy } = getCurrentInstance();
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
//字典
const dictList = ref([]);

const getDict = () => {
    dict_allList({
        pid: 115,
    }).then((res) => {
        if (res.data && res.data.data) {
            dictList.value = res.data.data;
        }
    });
};
const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData();
};
const getData = () => {
    award_file_page({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    }).then((res) => {
        console.log(res);
        if (res.data && res.data.data) {
            // tableData.value=res.data.data.list;
            total.value = res.data.data.total;
            tableData.value = res.data.data.list.map((i) => {
                let awardFileObj = "";
                if (i.awardFile) {
                    try {
                        // 尝试第一次解析
                        let firstParse = JSON.parse(i.awardFile);
                        // 如果第一次解析的结果是字符串，说明需要再次解析
                        if (typeof firstParse === 'string') {
                            awardFileObj = JSON.parse(firstParse);
                        } else {
                            awardFileObj = firstParse;
                        }
                    } catch (error) {
                        console.warn('解析awardFile失败:', error, i.awardFile);
                        awardFileObj = "";
                    }
                }
                return {
                    ...i,
                    awardFileObj: awardFileObj,
                };
            });
        }
    });
};

const downloadFile = (fileObj) => {
    if (!fileObj || !fileObj.fileName) {
        console.warn('文件信息不完整');
        return;
    }
    
    let fileName = fileObj.fileName;
    let fileUrl;
    
    // 构建文件下载URL
    if (fileObj.viewPath || fileObj.filePath || fileObj.url) {
        let filePath = fileObj.viewPath || fileObj.filePath || fileObj.url;
        let font = window.location.protocol;
        let host = window.location.hostname;
        
        if (filePath.indexOf("city189.cn") != -1) {
            fileUrl = filePath;
        } else {
            // 根据项目配置构建文件URL
            fileUrl = font + "//" + host + ":10000/" + 
                     filePath.split("/").splice(3, filePath.split("/").length).join("/");
        }
    }
    
    // 创建下载链接并触发下载
    try {
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = fileUrl;
        link.setAttribute("download", fileName);
        link.setAttribute("target", "_blank");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('文件下载失败:', error);
        window.open(fileUrl, '_blank');
    }
};
onMounted(() => {
    getData();
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.bottom {
    width: 1120px;
    height: 504px;
    .tablebox {
        //表格四个边框的颜色
        // border: 1px solid #30abe8 !important;
        border: none !important;
        border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

        th.el-table__cell {
            border: none !important;
        }
    }

    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        // border-bottom: 1px solid #30abe8 !important;
        height: 40px;
        background-color: rgba(25, 159, 255, 0.1) !important;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        text-align: center;
        color: rgba(128, 234, 255, 1);
    }

    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: none !important;
        }
    }

    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
    }

    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    // 表头样式（去掉底部边框）
    :deep(.el-table__header) {
        .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
        }
    }

    // 表格内容样式（保留底部边框）
    :deep(.el-table__body) {
        .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(255, 255, 255, 1);
        }
    }

    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }

    .pagination {
        margin-top: 16px;
    }

    // 文件下载链接样式
    .file-download-link {
        color: #1A9FFF;
        cursor: pointer;
        text-decoration: underline;
        transition: color 0.3s ease;

        &:hover {
            color: #FFC61A;
            text-decoration: underline;
        }

        &:active {
            color: #0080CC;
        }
    }

    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .btn-next) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .btn-prev) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination__total) {
        background-color: transparent;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: right;
        vertical-align: middle;
        color: rgba(26, 159, 255, 1);
    }
}
</style>
