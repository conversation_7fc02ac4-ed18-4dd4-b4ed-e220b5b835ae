import { createApp } from "vue";
import App from "../App.vue";

const app = createApp(App);
app.config.globalProperties.$changeUrl = function (url) {
    console.log("changeUrl");
    let font = window.location.protocol;
    let host = window.location.hostname;
    console.log(font,'llslsllsls');
    
    if (url.indexOf("city189.cn") != -1) {
        return url;
        // return url
    } else if (
        window.location.hostname == "***********" &&
        url.indexOf(".live.flv") != -1
    ) {
        let newUrl =
            font +
            "//***********:1506/" +
            url.split("/").splice(3, url.split("/").length).join("/");
        return newUrl;
    } else if (
        window.location.hostname == "**************" &&
        url.indexOf(".live.flv") != -1
    ) {
        let newUrl =
            font +
            "//" +
            host +
            ":11004/" +
            url.split("/").splice(3, url.split("/").length).join("/");
        return newUrl;
    } else if (
        window.location.hostname == "*************" &&
        url.indexOf(".live.flv") != -1
    ) {
        let newUrl =
            font +
            "//" +
            host +
            ":11004/" +
            url.split("/").splice(3, url.split("/").length).join("/");
        return newUrl;
    } else {
        let newUrl =
            font +
            "//" +
            host +
            ":10000/" +
            url.split("/").splice(3, url.split("/").length).join("/");
        return newUrl;
    }
};

app.mount("#app");
