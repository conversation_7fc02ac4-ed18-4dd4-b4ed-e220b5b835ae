<template>
    <TwoCard title="园区基础信息">
        
        <template v-slot>
            <div class="basic-information-wrapper">
              <div class="top">
                <div class="top-item" style="cursor: pointer;" @click="openDialog('parkInformationDialogShow')">
                    <div class="top-item-name">{{ topObj.establish_date }}</div>
                    <div class="top-item-value">成立日期</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('parkInformationDialogShow')">
                    <div class="top-item-name">{{ topObj.plan_area }}</div>
                    <div class="top-item-value">实际面积</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('parkInformationDialogShow')">
                    <div class="top-item-name">{{ topObj.rank }}</div>
                    <div class="top-item-value">安全风险等级</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('securityManagementDialogShow')">
                    <div class="top-item-name">{{ topObj.institution_system }}</div>
                    <div class="top-item-value">安全管理机构</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('informationDialogShow')">
                    <div class="top-item-name">{{ topObj.enterprise_num }}</div>
                    <div class="top-item-value">企业数量</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('personnelFileDialogShow')">
                    <div class="top-item-name">{{ topObj.staff }}</div>
                    <div class="top-item-value">从业人员</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('thirdPartyDialogShow')">
                    <div class="top-item-name">{{ topObj.third_enterprise }}</div>
                    <div class="top-item-value">第三方单位</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('dutyDialogShow')">
                    <div class="top-item-name">{{ topObj.duty_count }}</div>    
                    <div class="top-item-value">今日值班</div>
                </div>
                <div class="top-item" style="cursor: pointer;" @click="openDialog('accidentEventsDialogShow')">
                    <div class="top-item-name">{{ topObj.accident_case }}</div>    
                    <div class="top-item-value">事故事件</div>
                </div>
              </div>
              <div class="split-line">产品定位</div>
              <div class="center">{{ topObj.industry_position }}</div>
              <div class="split-line">禁限控目录</div>
              <div class="bottom">
                <div class="bottom-item"  style="cursor: pointer;" @click="openDialog('hazardousChemicalsControlDialogShow')">
                    <div class="bottom-item-num blue">{{ bottomData1 }}<span class="bottom-item-num-unit">项</span></div>
                    <img class="bottom-item-img" src="@/assets/newImages/SecureBase/basic-information-bottom-blue.svg" alt=""/>
                    <div class="bottom-item-name">危险化学品禁限控</div>
                </div>
                <div class="bottom-item" style="cursor: pointer;" @click="openDialog('workmanshipControlDialogShow')">
                    <div class="bottom-item-num yellow">{{ bottomData2 }}<span class="bottom-item-num-unit">项</span></div>
                    <img class="bottom-item-img" src="@/assets/newImages/SecureBase/basic-information-bottom-yellow.svg" alt=""/>
                    <div class="bottom-item-name">工艺禁限控</div>
                </div>
                <div class="bottom-item" style="cursor: pointer;" @click="openDialog('deviceControlDialogShow')">
                    <div class="bottom-item-num blue"> {{ bottomData3 }}<span class="bottom-item-num-unit">项</span></div>
                    <img class="bottom-item-img" src="@/assets/newImages/SecureBase/basic-information-bottom-blue.svg" alt=""/>
                    <div class="bottom-item-name">装置禁限控</div>
                </div>
              </div>
            </div>
        </template>
    </TwoCard>
</template>

<script setup>
import TwoCard from "@/components/commenNew/TwoCard.vue";
import {park_basicInfo_new, forbidden_control } from "@/assets/js/api/secureBase";
import {
    ref,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    computed,
    defineEmits
} from "vue";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

let topObj=ref({
    establish_date: '',
            plan_area: 0,
            rank: '-',
            institution_system: 0,
            enterprise_num: 0,
            staff: 0,
            third_enterprise: 0,
            duty_count: 1,
            accident_case: 0,
            industry_position: ''
})
let topList = ref([
    {
        name:'成立日期',
        value:'2020-01-01'
    },
    {
        name:'实际面积',
        value:'2934'
    },
    {
        name:'安全风险等级',
        value:'C'
    },
    {
        name:'安全管理机构',
        value:'12'
    },
    {
        name:'企业数量',
        value:'12'
    },
    {
        name:'从业人员',
        value:'12'
    },
    {
        name:'第三方单位',
        value:'12'
    },
    {
        name:'今日值班',
        value:'12'
    },
    {
        name:'事故事件',
        value:'12'
    }
]);
let bottomData1 = ref(0);
let bottomData2 = ref(0);
let bottomData3 = ref(0);
const getDataTop = () =>{
    park_basicInfo_new({}).then(res =>{
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            topObj.value = res.data.data[0];
        }
    })
}
const getDataBottom = () =>{
    forbidden_control({}).then(res =>{
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            let result = res.data.data;
            bottomData1.value = result.find(item => item.table_name === 'chemical-substance').record_count;
            bottomData2.value = result.find(item => item.table_name === 'craft_forbidden_control').record_count;
            bottomData3.value = result.find(item => item.table_name === 'equipment_control_type').record_count;
        }
    })  
}
onMounted(() => {
    getDataTop();
    getDataBottom();
});

onBeforeUnmount(() => {
});
//打开下钻弹窗
const openDialog = (val) => {
    proxy.$emit('openDialog',val);
}
</script>

<style lang="less" scoped>

.basic-information-wrapper{
    width: 416px;
height: 504px;
padding: 16px;
display: flex;
flex-direction: column;
.top{
    width: 416px;
height: 264px;
display: flex;
gap: 16px;
flex-wrap: wrap;
.top-item{
    width: 128px;
height: 80px;
background: url("@/assets/newImages/SecureBase/basic-information-top.svg") no-repeat;
            background-size: cover;
            background-position: center;
.top-item-name{
    font-family: Noto Sans SC;
font-weight: 700;
font-size: 20px;
line-height: 28px;
text-align: center;
color: rgba(255, 255, 255, 1);

}
.top-item-value{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);
margin-top: 24px;
}
}
}
.split-line{
    width: 416px;
height: 24px;
background: url("@/assets/newImages/SecureBase/basic-information-line.svg") no-repeat;
            background-size: cover;
            background-position: center;
            font-family: Noto Sans SC;
font-weight: 700;
font-size: 16px;
line-height: 24px;
text-align: center;
color:rgba(255, 255, 255, 1);
margin-top: 16px;
}
.center{
    width: 416px;
height: 52px;
margin-top: 8px;
background: url("@/assets/newImages/SecureBase/basic-information-center.svg") no-repeat;
            background-size: cover;
            background-position: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
display: flex;
align-items: center;
justify-content: center;
text-align: center;
color: rgba(255, 255, 255, 1);

}
.bottom{
    width: 416px;
height: 92px;
margin-top: 8px;
display: flex;
justify-content: space-between;
.bottom-item{
    width: 128px;
height: 92px;
color: rgba(255, 255, 255, 1);
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
.blue{
color:rgba(26, 159, 255, 1);
}
.yellow{
color:rgba(255, 198, 26, 1)
}
.bottom-item-num{
    font-family: Noto Sans SC;
font-weight: 700;
font-size: 24px;
line-height: 32px;
text-align: center;

.bottom-item-num-unit{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);
}
}
.bottom-item-img{
    width: 80px;
    height: 40px;
}
.bottom-item-name{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);

}

}
}

}
</style>
