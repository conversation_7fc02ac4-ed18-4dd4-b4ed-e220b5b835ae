<template>
    <Card title="车辆统计">
        <template v-slot>
    <div class="people-car">
        <div class="select-btn">
            <!-- <div :class="active === 1 ? 'active-btn' : 'btn'" @click="changeBtn(1)">人</div> -->
            <!-- <div :class="active === 2 ? 'active-btn' : 'btn'" @click="changeBtn(2)">
          车
        </div> -->
        </div>
        <div class="boxAll">
            <div
                :class="item == ',' ? 'box_number1' : 'box_number'"
                v-for="(item, index) in numberList"
                :key="index"
            >
                {{ item }}
            </div>
        </div>
        <div class="card">
            <div class="first">
                <div class="title">{{ text.oneTitle }}</div>
                <div class="num">{{ num.staff }}</div>
                <div class="unit">{{ text.oneUnit }}</div>
            </div>
            <div class="second">
                <div class="title">{{ text.twoTitle }}</div>
                <div class="num">{{ num.visitor }}</div>
                <div class="unit">{{ text.twoUnit }}</div>
            </div>
            <div class="third">
                <div class="title">{{ text.threeTitle }}</div>
                <div class="num">{{ num.other }}</div>
                <div class="unit">{{ text.threeUnit }}</div>
            </div>
            <div class="fourth">
                <div class="title">{{ text.fourTitle }}</div>
                <div class="num">{{ num.abnormal }}</div>
                <div class="unit">{{ text.fourUnit }}</div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    onUnmounted,
} from "vue";
import { cars_today } from "../../../../assets/js/api/comprehensiveSupervision";
const numberAll = ref(null);
const numberList = ref([]);
const number = ref([]);
const active = ref(2);
const { proxy } = getCurrentInstance();

const num = ref({
    staff: 0,
    visitor: 0,
    abnormal: 0,
    other: 0,
});
const text = ref({
    oneTitle: "员工",
    oneUnit: "员工车辆",
    twoTitle: "访客",
    twoUnit: "访客车辆",
    threeTitle: "陌生人",
    threeUnit: "陌生车辆",
    fourTitle: "危化品运输",
    fourUnit: "危化品车辆",
});
const obtainData = () => {
    cars_today({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            numberAll.value = Number(res.data.data[0].all_num);
            numberList.value = [];
            let number = numberAll.value.toString().split("");
            let n = Math.ceil(number.length / 3);
            for (let i = number.length - 1; i >= 0; i--) {
                for (let j = 1; j < n; j++) {
                    if (i == number.length - 3 * j - 1) {
                        numberList.value.unshift(",");
                    }
                }
                numberList.value.unshift(number[i]);
            }
            num.value.staff = res.data.data[0].car_num;
            num.value.visitor = res.data.data[0].visit_num;
            num.value.abnormal = res.data.data[0].heavy_num;
            num.value.other = res.data.data[0].other;
        }
    });
};
proxy.$bus.on("cars_today", () => {
    obtainData();
});
onMounted(() => {
    obtainData();
});
onUnmounted(() => {
    proxy.$bus.off("cars_today");
});
</script>

<style lang="less" scoped>
.people-car {
    width: 416px;
    height: 224px;
    padding: 16px;
    .select-btn {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px; /* 133.333% */
        .active-btn {
            width: 24px;
            padding: 4px 16px;
            justify-content: center;
            align-items: center;
            text-align: center;
            border-radius: 4px;
            border: 0.5px solid #ffc61a;
            background: rgba(255, 198, 26, 0.3);
        }
        .btn {
            width: 24px;
            padding: 4px 16px;
            text-align: center;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 0.622px solid #1ab2ff;
            background: rgba(26, 178, 255, 0.3);
        }
    }
    .boxAll {
        margin: 0 auto;
        margin-top: 8px;
        text-align: center;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        gap: 8px;
        .box_number1 {
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
        }
        .box_number {
            width: 40px;
            height: 48px;
            background: url("../../../../assets/images/card/numberCard.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            color: #fff;
            text-align: center;
            font-family: Digital Numbers;
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 48px; /* 128.571% */
        }
    }
    .card {
        margin-top: 40px;
        width: 416px;
        height: 112px;
        display: flex;
        justify-content: space-between;
        .first {
            width: 80px;
            height: 112px;
            background: url("../../../../assets/images/change/blue.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .second {
            width: 80px;
            height: 112px;
            background: url("../../../../assets/images/change/yellow.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .third {
            width: 80px;
            height: 112px;
            background: url("../../../../assets/images/change/orange.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .fourth {
            width: 80px;
            height: 112px;
            background: url("../../../../assets/images/change/red.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .title {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 32px; /* 157.143% */
        }
        .num {
            color: #fff;
            text-align: center;
            font-family: DINPro;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 28px; /* 140% */
            margin-top: 9px;
        }
        .unit {
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px; /* 133.333% */
        }
    }
}
</style>
