<template>
    <div class="command_1">
        <div class="command_title">
            <div>
                <img
                    class="title_image"
                    src="../../../../assets/images/card/title_left.svg"
                />
            </div>
            事故报告
            <img
                src="../../../../assets/images/card/index2_7.png"
                alt=""
                srcset=""
                class="closeImg"
                @click="handleClose"
            />
        </div>
        <div class="command_content">
            <div class="content1_title">
                <div class="line_box"></div>
                <div>基本信息</div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">事件名称:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.eventName }}
                    </div>
                </div>
                <div class="command_hang1">
                    <div class="command_hang1_title">上报人:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.eventLabel }}
                    </div>
                </div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">联系电话:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.contactNumber }}
                    </div>
                </div>
            </div>

            <div class="content1_title">
                <div class="line_box"></div>
                <div>事件详情</div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">事件描述:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.eventDescription }}
                    </div>
                </div>
                <div class="command_hang1">
                    <div class="command_hang1_title">发生时间:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.occurrenceTime }}
                    </div>
                </div>
            </div>
            <div class="command_hang">
                <!--        <div class="command_hang1">-->
                <!--          <div class="command_hang1_title">事件附件:</div>-->
                <!--          <div class="command_hang1_contet">-->
                <!--            {{ fireEvent.longitude }},{{ fireEvent.latitude }}-->
                <!--          </div>-->
                <!--        </div>-->
            </div>
            <div class="content1_title">
                <div class="line_box"></div>
                事件报告
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">事件类型:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.eventTypeName }}
                    </div>
                </div>
                <div class="command_hang1">
                    <div class="command_hang1_title">事件等级:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.eventLevel }}
                    </div>
                </div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">经济损失:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.economicLoss }}
                    </div>
                </div>
                <div class="command_hang1">
                    <div class="command_hang1_title">受灾面积:</div>
                    <div class="command_hang1_contet">
                        {{
                            reportData.disasterArea
                                ? reportData.disasterArea + "公顷"
                                : ""
                        }}
                    </div>
                </div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">死亡人数:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.deathNumber }}
                    </div>
                </div>
                <div class="command_hang1">
                    <div class="command_hang1_title">受伤人数:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.injuredNumber }}
                    </div>
                </div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">失踪人数:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.missingNumber }}
                    </div>
                </div>
                <div class="command_hang1">
                    <div class="command_hang1_title">受困人数:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.trappedNumber }}
                    </div>
                </div>
            </div>
            <div class="command_hang">
                <div class="command_hang1">
                    <div class="command_hang1_title">事件标签:</div>
                    <div class="command_hang1_contet">
                        {{ reportData.eventLabel }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted, getCurrentInstance } from 'vue'
import { reportDetails } from '../../../../assets/js/api/fire.js'

const { proxy } = getCurrentInstance()

// 定义props
const props = defineProps({
    propsList: {
        type: Object,
        required: true
    },
    dicts: {
        type: Object,
        default: () => ({})
    }
})

// 定义emits
const emit = defineEmits(['close'])

// 响应式数据
const reportData = ref({})

// 获取事故报告数据
const fetchReportData = () => {
    reportDetails({ id: props.propsList.itemList.id }).then((res) => {
        console.log(res, "事故报告", props.dicts.event_level)
        
        // 处理事件等级显示
        if (props.dicts.event_level) {
            props.dicts.event_level.forEach((items) => {
                console.log(items, "每一项")
                if (res.data.data.eventLevel == items.code) {
                    res.data.data.eventLevel = items.name
                    console.log(
                        res.data.data.eventLevel,
                        "res.data.data.eventLevel",
                    )
                }
            })
        }
        
        reportData.value = res.data.data
        proxy.$loading.show()
    })
}

// 关闭弹窗
const handleClose = () => {
    proxy.$loading.hide()
    emit('close')
}

// 组件挂载时获取数据
onMounted(() => {
    fetchReportData()
})
</script>

<style scoped>
.title_image {
    width: 12px;
    height: 12px;
    margin: auto 4px auto 16px;
}

.command_1 {
    background: url("../../../../assets/images/card/fire_2_1.png") no-repeat;
    background-size: cover;
    width: 891px;
    height: 808px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    z-index: 1005;
}

.command_title {
    margin-left: 17px;
    margin-top: 12px;
    font-style: normal;
    width: 100%;
    display: flex;
    margin-bottom: 30px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
}

.closeImg {
    width: 14px;
    height: 14px;
    margin-right: 40px;
    margin-top: 10px;
    margin-left: auto;
}

.command_content {
    height: 695px;
    margin-left: 24px;
    width: 845px;
    color: #fff;
    text-align: right;
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 5px;
    box-sizing: border-box;
}

.content1_title {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-wrap: nowrap;
    width: 408px;
    height: 36px;
    background-size: cover;
    padding-left: 30px;
    box-sizing: border-box;
    color: #47ebeb;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 16px;
}

.line_box {
    width: 2px;
    height: 8px;
    background: #47ebeb;
    margin-right: 4px;
}

.command_hang {
    display: flex;
    width: 814px;
    margin-bottom: 16px;
}

.command_hang1 {
    width: 50%;
    display: flex;
}

.command_hang1_title {
    width: 134px;
    text-align: right;
    color: #47ebeb;
    text-align: right;
    font-family: Noto Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.command_hang1_contet {
    margin-left: 16px;
}

::-webkit-scrollbar {
    width: 5px;
    float: right;
}

::-webkit-scrollbar-track {
    border-radius: 4px;
    background: #004f4f;
    width: 5px;
}

::-webkit-scrollbar-thumb {
    background: #fff;
    border-radius: 8px;
    width: 5px;
}
</style>
