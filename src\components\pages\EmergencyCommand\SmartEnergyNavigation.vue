<template>
    <div>
        <div class="smart-energy-navigation" v-if="showFlag == 3">
            <!-- <div :class="active === 5 ? 'active-btn' : 'btn'" @click.stop="seticon(5)">
          指挥调度
        </div> -->
        </div>

        <div>
            <fireIndex
                ref="testshow"
                :itemList="itemList"
                @clickListButton1="clickListButton1"
                @removeList="removeList"
            />
        </div>

        <!-- <div v-if="fire">
      <fireIndex ref="testshow" :itemList="itemList" @clickListButton1="clickListButton1" @removeList="removeList"/>
    </div> -->
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    nextTick,
} from "vue";

const { proxy } = getCurrentInstance();
import EnergyConsumptionDialog from "./subgroup/EnergyConsumptionDialog.vue";
import fireIndex from "./fireIndex.vue";
import { newLogin } from "../../../assets/js/api/lifeService.js";
// import { emergencyEvent } from "../../../assets/js/api/fire.js";
import { emergencyEvent } from "../../../assets/js/api/fire";

const energyConsumptionDialogShow = ref(false);
const fire = ref(false);
const energyConsumptionId = ref(null);
const timetype = ref(null);
const itemList = ref(null);
const circlenew = ref(null);
const showFlag = ref(1);
const flagList = ref({});
// const testshow = ref(null)
const active = ref(0);
var points = [];
var max = 0;
const clickListButton1 = (val, list) => {
    fire.value = val;
    //子组件将资源点位发过来 清除点位
    flagList.value = list;
    if (window.circle) {
        // window.map1.remove(circle);

        window.circle.setMap(null);
    }
    clearFlags();
    // console.log(flagList.value["_rawValue"]["BNCS"],"flagList.value[]")

    // if (!val){
    //     active.value = 0;
    // }
};
const removeList = (e) => {
    console.log(e, "e");
    circlenew.value = e;
    console.log(circlenew.value, "circlenew.value");
};
const clearFlags = () => {
    console.log(flagList.value, "返回来的数据");
    if (flagList.value["_rawValue"]) {
        if (flagList.value["_rawValue"]["BNCS"]) {
            flagList.value["_rawValue"]["BNCS"].forEach((item) => {
                item.setMap(null);
            });
        }
        if (flagList.value["_rawValue"]["ZBJK"]) {
            flagList.value["_rawValue"]["ZBJK"].forEach((item) => {
                item.setMap(null);
            });
        }
        if (flagList.value["_rawValue"]["FHMB"]) {
            flagList.value["_rawValue"]["FHMB"].forEach((item) => {
                item.setMap(null);
            });
        }
        if (flagList.value["_rawValue"]["YLJG"]) {
            flagList.value["_rawValue"]["YLJG"].forEach((item) => {
                item.setMap(null);
            });
        }
        if (flagList.value["_rawValue"]["FXYH"]) {
            flagList.value["_rawValue"]["FXYH"].forEach((item) => {
                item.setMap(null);
            });
        }
        if (flagList.value["_rawValue"]["TXBZ"]) {
            flagList.value["_rawValue"]["TXBZ"].forEach((item) => {
                item.setMap(null);
            });
        }
    }
};
const seticon = (type) => {
    console.log(type, "测试点击索引");
    if (active.value == type) {
        active.value = 0;
        console.log(type, "二次点击");
        fire.value = false;
        window.map1.clearMap();

        // if (window.heatmap != null) {
        //     window.heatmap.setMap(null)
        // }
        // points = []
        // console.log('----------------');
        // energyConsumptionId.value = null
        initBoundary();
    } else {
        active.value = type;
        getmapinfo();
        startTimer();
    }
    console.log(
        circlenew.value,
        "circlenew.value=============================",
    );

    // fire.value = true;
    // console.log(fire.value,"fire.value")
};
const getmapinfo = () => {
    window.viewer.entities.removeAll(); //删除所有
    window.map1.clearMap();
    initBoundary();
    // newLogin("chennan", "WmRzYyExMjM=").then((res) => {
    //     localStorage.setItem("token", res.data.access_token);
    //     console.log(res,"res.access_token")

    //     // fire.value = true;
    // });
    getEmergencyEvent();
};
const startTimer = () => {
    window.clearInterval(timetype.value);
    timetype.value = window.setInterval(() => {
        getmapinfo(); // 调用接口方法
    }, 540000); // 每隔 10 分钟调用一次接口
    //}, 600000); // 每隔 10 分钟调用一次接口
};
const isWithin10Minutes = (dateStr) => {
    const now = new Date(); // 获取当前时间

    const targetDate = new Date(dateStr); // 将要比较的时间转换为 Date 对象

    const diff = targetDate.getTime() - now.getTime(); // 计算时间差

    if (diff <= 10 * 60 * 1000 && diff >= 0) {
        return true;
    } else {
        return false;
    }
};
const clearTimer = () => {
    console.log("cghjkdjffhgk");
    window.clearInterval(timetype.value);
    timetype.value = null;
    window.map1.clearMap();
};
const getEmergencyEvent = () => {
    emergencyEvent({
        current: 1,
        size: 1,
    }).then((res) => {
        console.log(res, "接口返回数据");

        // fireEvent.value = res.data.data[0];
        // console.log(fireEvent.value.eventNo," fireEvent.value")
        res.data.data.forEach((item) => {
            let icons;
            if (isWithin10Minutes(item.createTime)) {
                icons = new AMap.Icon({
                    size: new AMap.Size(72, 92), // 图标尺寸
                    image: "/static/poi/dangerous-red.svg", //绝对路径
                    imageSize: new AMap.Size(72, 92),
                });
            } else {
                icons = new AMap.Icon({
                    size: new AMap.Size(72, 92), // 图标尺寸
                    image: "/static/poi/dangerous.svg", //绝对路径
                    imageSize: new AMap.Size(72, 92),
                });
            }
            let marker = new AMap.Marker({
                // content: '<div class="two-icon-infrastructure"></div>',
                icon: icons,
                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                position: [item.longitude, item.latitude],
                offset: new AMap.Pixel(-36, -92),
            });
            marker.on("click", (e) => {
                console.log(item, "点击的标记点");
                clearFlags();
                // infrastructureId.value = item.name;
                // infrastructureDialogShow.value = true;
                proxy.$bus.emit("showEvent", true);
                itemList.value = item;
                nextTick(() => {
                    proxy.$bus.emit("fireEvent", itemList.value);
                });

                fire.value = true;
                if (window.overlays) {
                    console.log(window.overlays, "进入清除================1");
                    window.map1.remove(window.overlays[0]);
                    window.map1.remove(window.overlays[1]);
                    window.map1.remove(window.overlays[2]);
                    // window.overlays.forEach(item=>{
                    //     window.map1.remove(item);
                    // })
                }
                if (window.circleone) {
                    console.log(window.circleone, "进入清除================2");
                    // window.infoWindow.destroy();
                    window.map1.remove(window.circleone);
                    window.circleone.forEach((item) => {
                        window.map1.remove(item);
                    });
                }
                if (window.circletwo || window.circletwoone) {
                    console.log("进入清除================3");
                    // window.map1.remove(window.circletwo);
                    // window.infoWindowone.close();
                    // console.log(window.infoWindowone,"window.infoWindowone")
                    // window.infoWindowone.forEach(item=>{
                    //     item.close()
                    // })
                    window.circletwo.forEach((item) => {
                        window.map1.remove(item);
                    });
                    window.circletwoone.forEach((item) => {
                        window.map1.remove(item);
                    });
                }
                // this.$refs.testshow.toUeInit(item)
                // this.$refs.testshow.getDictionary(item)
                //
                // console.log(infrastructureId.value);
            });
            marker.setMap(window.map1);
        });
        window.map1.setFitView();
    });
    window.map1.setFitView();
};

var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    console.log(window.map1, "地图实例", window.toggle, "测试");
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
onMounted(() => {
    //   showFlag.value = window.toggle;
    initStart();
    proxy.$bus.off("initStart");
    proxy.$bus.on("initStart", () => {
        initStart();
    });
});
const initStart = () => {
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
    getmapinfo();
    startTimer();
};
onBeforeUnmount(() => {
    clearTimer();
});
const closeDialog = (value) => {
    console.log(value);
    if (value == "energyConsumption") {
        energyConsumptionDialogShow.value = false;
    }
};
</script>

<style lang="less" scoped>
.smart-energy-navigation {
    width: 130px;
    height: 92px;
    /*z-index: 1000;*/
    z-index: 1;
    position: absolute;
    top: 812px;
    left: 496px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 157.143% */
    text-align: center;

    > div:not(:first-child) {
        margin-top: 24px;
    }

    .btn {
        width: 130px;
        height: 32px;
        background: url("../../../assets/images/jump-btn/self-nav.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }

    .active-btn {
        width: 130px;
        height: 32px;
        background: url("../../../assets/images/jump-btn/self-nav-active.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
    }
}
</style>
