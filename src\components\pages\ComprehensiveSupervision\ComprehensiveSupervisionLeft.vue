<template>
    <div>
        <div class="comprehensive-supervision-left">
            <!-- <wrap-slot
                title1="数字化设备"
                title2="在线设备数量"
                title3="企业概况"
            >
                <template #content1>
                </template>
                <template #content2>
                </template>
                <template #content3>
                </template>
            </wrap-slot> -->
            <left-top @device="device"></left-top>
            <left-center></left-center>
            <left-bottom></left-bottom>


        </div>
        <device-query-dialog
            v-if="deviceDialogShow"
            @closeDialog="closeDialog"
        ></device-query-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";
import WrapSlot from "../../commenNew/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import deviceQueryDialog from "./subgroup/deviceQueryDialog.vue";
const { proxy } = getCurrentInstance();

const deviceDialogShow = ref(false);
const device = () => {
    deviceDialogShow.value = true;
    proxy.$loading.show();
};
const closeDialog = () => {
    proxy.$loading.hide();
    deviceDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.comprehensive-supervision-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
