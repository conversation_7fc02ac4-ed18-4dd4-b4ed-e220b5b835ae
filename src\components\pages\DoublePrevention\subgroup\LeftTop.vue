<template>
    <Card title="风险分析">
        <template v-slot:title>
                <div class="title-bar-right" style="cursor: pointer;" @click="openDialog('riskControlCheckDialogShow')">
                    <div>风险管控清单</div>
                    <img class="right-arrow" src="@/assets/newImages/right-arrow.svg" />
                </div>
        </template>
        <template v-slot>
            <div class="risk-analysis-wrapper">
                <div class="left">
                    <div class="left-num">{{percent}}%</div>
                    <div class="left-text">风险分析完成率</div>
                </div>
                <div class="right">
                    <div class="right-item" v-for="(item,index) in rightList" :key="index" @click="openDialog(item.flag)">
                        <div class="right-item-name">{{item.name}}</div>
                        <div class="right-item-value">{{item.value}}</div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue"
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance } from "vue";
import {riskAnalysisOverview} from "@/assets/js/api/doublePrevention.js";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val) => {
    proxy.$emit('openDialog',val);
}
let percent = ref(0);
let rightList = ref([
    {
        name: "风险分析对象",
        value: 0,
        flag:'riskObjectDialogShow'
    },
    {
        name: "风险分析单元",
        value: 0,
        flag:'riskAnalysisUnitDialogShow'
    },
    {
        name: "风险事件",
        value: 0,
        flag:'riskEventInfoDialogShow'
    },
    {
        name: "风险管控措施",
        value: 0,
        flag:'riskControlMeasureDialogShow'
    },
    
])
const getData = () => {
    riskAnalysisOverview({}).then(res => {
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            console.log(res.data.data[0],'res.data.data[0]');
            let result = res.data.data[0];
            rightList.value[0].value = result.riskAnalysisObjectCount;
            rightList.value[1].value = result.riskAnalysisUnitCount;
            rightList.value[2].value = result.riskEventCount;
            rightList.value[3].value = result.riskControlMeasureCount;
            percent.value = Number(result.riskAnalysisCompletionRate).toFixed(0);
            // console.log(rightList.value,'rightList.value');
        }
    })
}
onMounted(() => {
    getData();
})
</script>

<style lang="less" scoped>
.title-bar-right{
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: center;
color: #80EAFF;

        display: flex;
        gap: 4px;
        .right-arrow{
            width: 16px;
            height: 16px;
            margin: auto;
        }
    }
    .risk-analysis-wrapper{
        width: 416px;
    height: 224px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    .left{
        width: 160px;
        height: 190px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;
        margin: 17px 0px 17px 12px;
        .left-num{
            width:160px;
            height: 160px;
            background: url("@/assets/newImages/DoublePrevention/risk-num.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: DIN Alternate;
            font-weight: 700;
            font-size: 28px;
            line-height: 36px;
            text-align: center;
            color: rgba(255, 255, 255, 1);
        }
        .left-text{
            font-family: Noto Sans SC;
font-weight: 700;
font-size: 16px;
line-height: 22px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);

        }
    }
    .right{
        width: 216px;
height: 184px;
margin-top: 20px;
display: flex;
flex-direction: column;
gap: 24px;
.right-item{
    width: 193px;
height: 28px;
padding-left: 23px;

    display: flex;
    // align-items: center;
    justify-content: space-between;
    background: url("@/assets/newImages/DoublePrevention/risk-bar.svg") no-repeat;
            background-size: cover;
            background-position: center;
    .right-item-name{
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
color:rgba(255, 255, 255, 1);
    }
    .right-item-value{
        font-family: MStiffHei PRC;
font-weight: 400;
font-size: 20px;
line-height: 28px;
background: linear-gradient(180deg, #FFFFFF 0%, #80EAFF 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
    }
    
}

    }
    }
</style>
