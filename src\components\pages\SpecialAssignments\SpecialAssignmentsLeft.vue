<template>
    <div>
        <div class="secure-base-left">
            <left-top @openDialog="openDialog"></left-top>
            <left-bottom></left-bottom>
        </div>
        <special-homework-report-dialog v-if="reportDialogShow" @closeDialog="closeDialog"></special-homework-report-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,

} from "vue";
import WrapSlot from "../../commenNew/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import specialHomeworkReportDialog from "./subgroup/SpecialHomeworkReportDialog.vue";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

const openDialog = (val,btnSelect,nowState) => {
    console.log(val,btnSelect,nowState, '打开弹窗');
    emit('openDialog',val,btnSelect,nowState);
}
const reportDialogShow = ref(false);
const report = () => {
    reportDialogShow.value = true;
    proxy.$loading.show();
};
const closeDialog = () => {
    proxy.$loading.hide();
    reportDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.secure-base-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
