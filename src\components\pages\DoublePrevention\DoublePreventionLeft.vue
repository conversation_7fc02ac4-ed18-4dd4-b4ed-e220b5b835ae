<template>
    <div>
        <div class="secure-base-left">
           <left-top @openDialog="openDialog"></left-top>
           <left-center @openDialog="openDialog"></left-center>
           <left-bottom></left-bottom>
        </div>
        <major-hazard-enterprises-dialog  v-if="majorDialogShow" @closeDialog="closeMajorDialog" :title="title" :sourceLevel="sourceLevel"></major-hazard-enterprises-dialog>
        <risk-analysis-unit-dialog  v-if="riskDialogShow" @closeDialog="closeRiskDialog"></risk-analysis-unit-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";
import WrapSlot from "../../commen/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import MajorHazardEnterprisesDialog from './subgroup/MajorHazardEnterprisesDialog.vue'
import RiskAnalysisUnitDialog from './subgroup/RiskAnalysisUnitDialog.vue'
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

const openDialog = (val,btnSelect) => {
    console.log(val,btnSelect, '打开弹窗');
    emit('openDialog',val,btnSelect);
}

const title = ref('重大危险源企业');
const sourceLevel = ref('')
const majorDialogShow = ref(false);
const major = (val) => {
    if (val) {
        title.value = val + '重大危险源';
    sourceLevel.value = val;
    } else {
        title.value = '重大危险源企业';
    sourceLevel.value = '';
    }

    console.log(val, 'major');
    majorDialogShow.value = true;
    proxy.$loading.show();
    
};

const closeMajorDialog = () => {
    proxy.$loading.hide();
    majorDialogShow.value = false;
};
const riskDialogShow = ref(false);

const risk = () => {
    riskDialogShow.value = true;
    proxy.$loading.show();
};
const closeRiskDialog = () => {
    proxy.$loading.hide();
    riskDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.secure-base-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
