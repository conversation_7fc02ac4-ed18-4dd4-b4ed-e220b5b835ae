<template>
    <div>
        <div class="secure-base-left">
            <left-top @openDialog="openDialog"></left-top>
            <left-center @openDialog="openDialog"></left-center>
        </div>
        <enterprise-basic-information-dialog  v-if="basicDialogShow" @closeDialog="closeDialog"></enterprise-basic-information-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";
import WrapSlot from "../../commen/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
import EnterpriseBasicInformationDialog from './subgroup/EnterpriseBasicInformationDialog.vue'
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

const basicDialogShow = ref(false);
const basic = () => {
    basicDialogShow.value = true;
    proxy.$loading.show();
};
const closeDialog = () => {
    proxy.$loading.hide();
    basicDialogShow.value = false;
};
const openDialog = (val) => {
    console.log(val, '打开弹窗');
    emit('openDialog',val);
}
</script>

<style lang="less" scoped>
.secure-base-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
