<svg width="200" height="75" viewBox="0 0 200 75" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="8.5" y="9.5" width="55" height="55" rx="27.5" stroke="url(#paint0_linear_53_94)"/>
<circle cx="36" cy="37" r="21.5" fill="url(#paint1_linear_53_94)" stroke="url(#paint2_linear_53_94)"/>
<g clip-path="url(#clip0_53_94)">
<g clip-path="url(#clip1_53_94)">
<path d="M31.5149 31.0477C31.6738 31.0396 31.8319 31.075 31.9721 31.1501L34.7354 32.6267C34.7729 32.6442 34.8066 32.6692 34.8353 32.6979C35.7285 33.5823 36.949 39.0926 37.2225 40.7028C37.2459 40.8281 37.2484 40.9564 37.23 41.0825L38.0845 41.897L39.6997 39.6172C40.3043 38.5604 41.5648 37.8121 42.1694 38.869L45.897 45.9819C45.9559 46.0834 45.9871 46.1987 45.9874 46.316C45.9878 46.4334 45.9572 46.5488 45.8989 46.6507C45.8406 46.7526 45.7565 46.8373 45.6551 46.8964C45.5537 46.9555 45.4385 46.9869 45.3211 46.9875H35.8184C35.7405 46.9865 35.6643 46.9652 35.5972 46.9256C35.53 46.8861 35.4744 46.8297 35.4358 46.7621C35.3972 46.6944 35.3769 46.6178 35.377 46.5399C35.377 46.4621 35.3974 46.3855 35.4362 46.3179L37.3737 42.9901L32.5143 39.2387L32.6017 39.3549C32.5168 39.5673 32.5543 39.7921 32.3344 39.7297C31.5549 39.506 29.5312 38.4317 28.7742 38.1532C28.6706 38.1149 28.5849 38.0395 28.5336 37.9417C28.4824 37.8438 28.4693 37.7305 28.4969 37.6235C28.5518 37.4049 28.4219 37.7447 28.5668 37.2775C28.5938 37.1904 28.5937 37.0972 28.5664 37.0102C28.5392 36.9232 28.4862 36.8465 28.4144 36.7903L26.7655 35.5074L26.287 35.1201C26.1612 34.994 26.0759 34.8331 26.042 34.6582C26.0082 34.4832 26.0274 34.3022 26.0972 34.1382C26.4532 33.29 27.0965 32.202 27.3239 31.7597C27.3901 31.6348 27.5113 31.5474 27.6512 31.5261C28.3557 31.4187 30.5506 31.0952 31.5137 31.0477H31.5149ZM28.6205 38.8852C29.0203 38.9789 29.6449 39.2225 30.2707 39.4898L30.6855 39.6709C31.5724 40.0644 32.3569 40.4504 32.3569 40.4504C33.0315 40.7652 34.2507 42.744 34.2419 44.0032C34.2394 44.5103 33.9721 45.3548 33.1651 46.7214C33.1262 46.7879 33.0705 46.843 33.0037 46.8814C32.9369 46.9197 32.8612 46.94 32.7841 46.94H31.8922C31.8151 46.9388 31.7396 46.9178 31.6731 46.8789C31.6065 46.84 31.5511 46.7846 31.5121 46.7181C31.4732 46.6515 31.4521 46.576 31.4508 46.499C31.4496 46.4219 31.4682 46.3458 31.5049 46.278C31.8235 45.6946 32.2307 44.8289 32.2919 44.1081C32.3419 43.526 31.0852 41.581 29.5937 41.5897C29.4862 41.5897 29.059 42.7352 28.618 43.9657L28.4407 44.4591C28.1171 45.3648 27.8073 46.2392 27.6299 46.6665C27.5972 46.7477 27.541 46.8173 27.4684 46.8663C27.3958 46.9153 27.3102 46.9414 27.2227 46.9413H26.4432C26.3746 46.9409 26.307 46.9248 26.2457 46.894C26.1843 46.8633 26.1309 46.8188 26.0896 46.764C26.0482 46.7093 26.0201 46.6457 26.0073 46.5783C25.9946 46.5109 25.9975 46.4415 26.016 46.3754C26.4232 44.8951 27.7648 40.4167 28.0934 39.2012C28.1234 39.0907 28.1951 38.996 28.2933 38.9371C28.3916 38.8782 28.5089 38.8596 28.6205 38.8852ZM34.483 36.4318C34.4564 36.3439 34.403 36.2666 34.3304 36.2105C34.2578 36.1543 34.1695 36.1222 34.0778 36.1185C33.9861 36.1148 33.8955 36.1397 33.8186 36.1898C33.7417 36.2399 33.6823 36.3127 33.6486 36.398L33.0552 37.8733L35.7297 40.1057C35.2513 39.0488 34.8028 37.4736 34.483 36.4318ZM28.703 32.8715C28.6206 32.8689 28.5392 32.8894 28.4678 32.9307C28.3965 32.9719 28.3382 33.0324 28.2995 33.1051C28.0696 33.5361 27.6949 34.3518 27.7386 34.3893L28.623 35.2263C28.8504 35.4461 29.2277 35.3637 29.3401 35.0664C29.5924 34.4093 29.7873 33.9496 30.0009 33.5549C30.0375 33.4883 30.0564 33.4136 30.0559 33.3377C30.0553 33.2617 30.0352 33.1873 29.9976 33.1213C29.96 33.0554 29.9061 33.0002 29.8411 32.9611C29.776 32.9219 29.702 32.9001 29.6261 32.8978L28.703 32.8715ZM34.9465 27.0003C35.5237 27.0138 36.0733 27.2503 36.48 27.6602C36.8866 28.0701 37.1187 28.6216 37.1276 29.1989C37.135 29.4994 37.0817 29.7984 36.9707 30.0778C36.8597 30.3572 36.6934 30.6113 36.4817 30.8248C36.2701 31.0383 36.0175 31.2069 35.739 31.3203C35.4606 31.4337 35.1622 31.4897 34.8616 31.4849C34.2839 31.4714 33.7339 31.2346 33.3272 30.8241C32.9205 30.4137 32.6887 29.8616 32.6804 29.2838C32.6733 28.9835 32.727 28.6848 32.8381 28.4057C32.9493 28.1266 33.1156 27.8728 33.3272 27.6596C33.5389 27.4463 33.7914 27.278 34.0696 27.1647C34.3478 27.0514 34.6461 26.9955 34.9465 27.0003Z" fill="white"/>
</g>
</g>
<path d="M64 31L71.5777 46.1554C74.288 51.576 79.8282 55 85.8885 55H192" stroke="url(#paint3_linear_53_94)"/>
<rect x="184" y="54" width="16" height="2" fill="#80EAFF"/>
<defs>
<linearGradient id="paint0_linear_53_94" x1="36" y1="9" x2="36" y2="65" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AC6FF"/>
<stop offset="1" stop-color="#1AC6FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_53_94" x1="36" y1="59" x2="36" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint2_linear_53_94" x1="58" y1="37" x2="14" y2="37" gradientUnits="userSpaceOnUse">
<stop stop-color="#9CECFC"/>
<stop offset="0.5" stop-color="#1AA0FF" stop-opacity="0.45"/>
<stop offset="1" stop-color="#9CECFC"/>
</linearGradient>
<linearGradient id="paint3_linear_53_94" x1="125.86" y1="31" x2="125.86" y2="55" gradientUnits="userSpaceOnUse">
<stop stop-color="#6699CC" stop-opacity="0"/>
<stop offset="1" stop-color="#6699CC" stop-opacity="0.45"/>
</linearGradient>
<clipPath id="clip0_53_94">
<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 48 25)"/>
</clipPath>
<clipPath id="clip1_53_94">
<rect width="20" height="20" fill="white" transform="translate(26 27)"/>
</clipPath>
</defs>
</svg>
