<template>
    <Card title="能源监测总数">
        <template v-slot>
            <div class="energy-consumption-monitoring">
                <div class="top">
                    <div class="inner" @click="clickElectricity">
                        <div class="title">用电量（千瓦时）</div>
                        <div class="num">{{ data.electricity_total }}</div>
                    </div>
                    <div class="inner" @click="clickWater">
                        <div class="title">用水量（吨）</div>
                        <div class="num">{{ data.water_total }}</div>
                    </div>
                </div>
                <div class="bottom">
                    <div class="inner" @click="clickGas">
                        <div class="title">天然气（千立方米）</div>
                        <div class="num">{{ data.gas_total }}</div>
                    </div>
                    <div class="inner" @click="clickSteam">
                        <div class="title">蒸汽量（吨）</div>
                        <div class="num">{{ data.steam_total }}</div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>
<script setup>
import Card from "@/components/commenNew/Card.vue";

import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { energy_total } from "../../../../assets/js/api/smartEnergy";
const emit = defineEmits(["energy"]);

const clickElectricity = () => {
    emit("energy", "Electricity");
};
const clickWater = () => {
    emit("energy", "Water");
};
const clickGas = () => {
    emit("energy", "Gas");
};
const clickSteam = () => {
    emit("energy", "Steam");
};
const data = ref({
    electricity_total: "",
    gas_total: "",
    steam_total: "",
    water_total: "",
});
onMounted(() => {
    energy_total({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            data.value = res.data.data[0];
        }
    });
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.energy-consumption-monitoring {
    width: 416px;
    height: 224px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .top {
        width: 416px;
        height: 120px;
        display: flex;
        justify-content: space-between;
    }
    .bottom {
        width: 416px;
        height: 120px;
        display: flex;
        justify-content: space-between;
    }
    .inner {
        width: 192px;
        height: 120px;
        background: url("../../../../assets/images/card/overview.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        .title {
            // width: 136px;
            // height: 20px;
            margin-top: 10px;
            align-items: center;
            gap: 10px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 150% */
            text-align: center;
        }
        .num {
            margin-top: 8px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
        }
    }
}
</style>
