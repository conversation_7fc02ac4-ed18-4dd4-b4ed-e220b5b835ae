<template>
    <div>
        <div class="nav-all-wrapper">
            <!-- 企业选择器 -->
            <div class="input-wrapper">
                <!-- <el-select
                    v-model="enterpriseName"
                    class="m-2"
                    placeholder="请选择企业"
                    clearable
                    @change="changeEnterpriseName"
                    filterable
                >
                    <el-option
                        v-for="item in enterpriseNameOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select> -->
            </div>

            <!-- 导航按钮 -->
            <div v-if="showFlag == 2 || showFlag == 3" class="nav-all-btns">
                <div
                    :class="active === 1 ? 'active-btn' : 'btn'"
                    @click="setPage(1)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/atmospheric-monitoring.svg"
                    />
                    <div class="text">大气监测</div>
                </div>
                <div
                    :class="active === 2 ? 'active-btn' : 'btn'"
                    @click="setPage(2)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/waste-gas-outlet.svg"
                    />
                    <div class="text">废气排放口</div>
                </div>
                <div
                    :class="active === 3 ? 'active-btn' : 'btn'"
                    @click="setPage(3)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/wastewater-outlet.svg"
                    />
                    <div class="text">雨水排口</div>
                </div>
                <div
                    :class="active === 6 ? 'active-btn' : 'btn'"
                    @click="setPage(6)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/wastewater-outlet.svg"
                    />
                    <div class="text">废水排口</div>
                </div>
                <div
                    :class="active === 4 ? 'active-btn' : 'btn'"
                    @click="setPage(4)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/hazardous-waste-warehouse.svg"
                    />
                    <div class="text">危废仓库</div>
                </div>
                <div
                    v-if="false"
                    :class="active === 5 ? 'active-btn' : 'btn'"
                    @click="setPage(5)"
                >
                    <img
                        class="icon"
                        src="../../../assets/images/icon/dynamic-thermodynamics.svg"
                    />
                    <div class="text">动态热力</div>
                </div>
            </div>
        </div>
        <div
            class="dynamic-thermodynamics-btns"
            v-if="dynamicThermodynamicsBtnsShow"
        >
            <div
                :class="
                    dynamicActive === 106001
                        ? 'active-dynamic-btn'
                        : 'dynamic-btn'
                "
                @click="selectDynamic(106001)"
            >
                大气
            </div>
            <div
                :class="
                    dynamicActive === 106002
                        ? 'active-dynamic-btn'
                        : 'dynamic-btn'
                "
                @click="selectDynamic(106002)"
            >
                废水
            </div>
            <div
                :class="
                    dynamicActive === 106006
                        ? 'active-dynamic-btn'
                        : 'dynamic-btn'
                "
                @click="selectDynamic(106006)"
            >
                废气
            </div>
            <div
                :class="
                    dynamicActive === 106007
                        ? 'active-dynamic-btn'
                        : 'dynamic-btn'
                "
                @click="selectDynamic(106007)"
            >
                雨水
            </div>
        </div>
        <div class="kedu" v-if="keduShow">
            <!-- <el-slider v-model="value2" :step="10" show-stops :show-tooltip="false" /> -->
            <div
                class="kedu-item"
                v-for="(item, index) in time"
                :key="index"
                @click="clickTime(item)"
            >
                <div
                    :class="item == selectTime ? 'kedu-text' : 'kedu-text-no'"
                    v-if="item == selectTime || index == 0 || index == 24"
                >
                    {{ item }}
                </div>
                <div v-else class="kedu-text-none"></div>
                <div
                    :class="item == selectTime ? 'kedu-liner-no' : 'kedu-liner'"
                ></div>
                <div class="kedu-icon" v-if="item == selectTime"></div>
            </div>
        </div>
        <hazardous-waste-warehouse-dialog
            :pickId="warehouseId"
            v-if="warehouseDialogShow"
            @closeDialog="closeDialog"
        ></hazardous-waste-warehouse-dialog>
        <atmospheric-monitoring-dialog
            :pickId="atmosphericId"
            v-if="atmosphericDialogShow"
            @closeDialog="closeDialog"
        ></atmospheric-monitoring-dialog>
        <waste-gas-outlet-dialog
            :pickId="wasteGasId"
            v-if="wasteGasDialogShow"
            @closeDialog="closeDialog"
        ></waste-gas-outlet-dialog>
        <rainwater-outlet-dialog
            :pickId="rainwaterOutletId"
            v-if="rainwaterOutletDialogShow"
            @closeDialog="closeDialog"
        ></rainwater-outlet-dialog>
        <wastewater-outlet-dialog
            :pickId="wastewaterOutletId"
            v-if="wastewaterOutletDialogShow"
            @closeDialog="closeDialog"
        ></wastewater-outlet-dialog>
        <atmospheric-monitoring-dialog-new
            :pickId="atmosphericMonitoringId"
            v-if="atmosphericMonitoringDialogShow"
            @closeDialog="closeDialog"
        ></atmospheric-monitoring-dialog-new>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    onUnmounted,
    getCurrentInstance,
    watch,
    nextTick,
} from "vue";

import HazardousWasteWarehouseDialog from "./subgroup/HazardousWasteWarehouseDialog.vue";
import AtmosphericMonitoringDialog from "./subgroup/AtmosphericMonitoringDialog.vue";
import AtmosphericMonitoringDialogNew from "./subgroup/AtmosphericMonitoringDialogNew.vue";
import WasteGasOutletDialog from "./subgroup/WasteGasOutletDialog.vue";
import RainwaterOutletDialog from "./subgroup/RainwaterOutletDialog.vue";
import WastewaterOutletDialog from "./subgroup/WastewaterOutletDialog.vue";
import {
    waste_store_position,
    edevice_position,
    monitoring_stations,
    thermal_map,
    getthermalMap,
} from "../../../assets/js/api/environmentalManagement";
import { enterprise_list } from "../../../assets/js/api/parkArchives";
import * as echarts from "echarts";
import { setSize } from "../../../assets/js/echartsSetSize";
import * as Cesium from "cesium";

// POI图片路径
import atmosphericMonitoringImg from "/static/poi/atmospheric-monitoring-new.svg";
import atmosphericMonitoringGreyImg from "/static/poi/atmospheric-monitoring-grey.svg";
import wasteGasOutletImg from "/static/poi/waste-gas-outlet-new.svg";
import wasteGasOutletGreyImg from "/static/poi/waste-gas-outlet-grey.svg";
import wastewaterOutletImg from "/static/poi/wastewater-outlet-new.svg";
import wastewaterOutletGreyImg from "/static/poi/wastewater-outlet-grey.svg";

// 组件实例和状态
const { proxy } = getCurrentInstance();
const showFlag = ref(1);
const active = ref(0);

// 企业选择器相关
const enterpriseName = ref("");
const enterpriseNameOptions = ref([]);

// 弹窗相关
const warehouseDialogShow = ref(false);
const wasteGasDialogShow = ref(false);
const rainwaterOutletDialogShow = ref(false);
const wastewaterOutletDialogShow = ref(false);
const atmosphericMonitoringDialogShow = ref(false);

// ID相关
const warehouseId = ref(null);
const wasteGasId = ref(null);
const rainwaterOutletId = ref(null);
const wastewaterOutletId = ref(null);
const atmosphericMonitoringId = ref(null);

// 数据相关
const areaData = ref([]);
const positionData = ref([]);
const areaDataThree = ref([]);

// 其他状态
const value2 = ref(0);
const keduShow = ref(false);
const dynamicThermodynamicsBtnsShow = ref(false);
const dynamicActive = ref(106001);
const dynamicValue = ref(106001);

// 时间相关
const day = ref("");
const selectTime = ref("00:00");
const time = ref([
    "00:00", "01:00", "02:00", "03:00", "04:00", "05:00",
    "06:00", "07:00", "08:00", "09:00", "10:00", "11:00",
    "12:00", "13:00", "14:00", "15:00", "16:00", "17:00",
    "18:00", "19:00", "20:00", "21:00", "22:00", "23:00", "24:00"
]);

// 热力图相关
let points = [];
let pointsCesium = [];
let pointsCesium_1 = [];
let max = 0;
// 获取企业列表数据
const fetchEnterpriseData = async () => {
    try {
        const res = await enterprise_list({ enterprise_name: "" });
        if (res.data.success && res.data.data?.length) {
            enterpriseNameOptions.value = res.data.data.map(x => ({
                value: x.enterprise_name,
                label: x.enterprise_name,
            }));
        }
    } catch (error) {
        console.error("获取企业列表失败:", error);
    }
};

// 切换企业
const changeEnterpriseName = (val) => {
    if (val) {
        const searchItem = enterpriseNameOptions.value.find(i => i.value === val);
        enterpriseName.value = searchItem?.label || '';
    } else {
        enterpriseName.value = '';
    }

    clearMapEntities();

    // 如果有选中的导航按钮，重新获取数据
    if (active.value) {
        getData(active.value);
    }
};

// 清除地图实体
const clearMapEntities = () => {
    window.viewer.entities.removeAll();
    window.map1.clearMap();
    if (window.cluster) window.cluster.setMap(null);
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    if (window.Locast != null) window.Locast.setMap(null);
};

//点击报警,并返回一个新数组
const resetData = (list, values, uid) => {
    console.log("laizhele");
    //list全部数据
    //values是根据哪个属性
    //uid要查询的id数据
    let selectLsit = [];
    console.log(list);
    list.forEach((item) => {
        console.log(item);
        console.log(uid);
        if (item[values] == uid) {
            selectLsit.push(item);
        }
    });
    console.log(selectLsit);
    clearMapEntities();
    initBoundary();
    return selectLsit;
};
const selectDynamic = (val) => {
    dynamicActive.value = val;
    dynamicValue.value = val;
    keduShow.value = true;
    // var today = new Date();
    // var hour = ("0" + today.getHours()).slice(-2);
    // selectTime.value = hour + ':00'
    var time = day.value + " " + selectTime.value + ":00";

    thermal_map({
        equipmentType: dynamicValue.value,
        end: time,
    }).then((res) => {
        if (res.data.code == 200) {
            console.log("in");
            points = res.data.data.map((x) => {
                return {
                    lat: x.latitude,
                    lng: x.longitude,
                    polluteLevelMax: x.polluteLevelMax,
                    count: x.monitor_value,
                };
            });
            max = points[0].count;

            initHeatMap();
        } else {
            console.log(1111);
            console.log(window.heatmap);
            if (window.heatmap != null) {
                window.heatmap.setMap(null);
            }
        }
    });
};
const closeDialog = (value) => {
    console.log(value);
    if (value == "warehouse") {
        warehouseDialogShow.value = false;
    } else if (value == "wastegas") {
        wasteGasDialogShow.value = false;
    } else if (value == "rainwater") {
        rainwaterOutletDialogShow.value = false;
    } else if (value == "wastewater") {
        wastewaterOutletDialogShow.value = false;
    } else if (value == "atmospheric") {
        atmosphericMonitoringDialogShow.value = false;
    }
    //proxy.$loading.hide();
};
var zIndex = 30;
// 切换导航按钮
const setPage = (val) => {
    if (val === active.value) {
        // 再次点击同一按钮，取消选择
        active.value = 0;
        clearMapEntities();
        dynamicThermodynamicsBtnsShow.value = false;
        keduShow.value = false;
        initBoundary();
    } else {
        clearMapEntities();
        active.value = val;
        dynamicThermodynamicsBtnsShow.value = false;
        keduShow.value = false;
        initBoundary();

        // 根据选择获取不同状态的数据
        getData(val);
    }
};

// 统一的数据获取方法
const getData = async (val) => {
    positionData.value = [];

    if (val === 1) {
        // 大气监测
        searchAtmosphericMonitoring();
    } else if (val === 2) {
        // 废气排放口
        searchWasteGas();
    } else if (val === 3) {
        // 雨水排口
        searchRainwater();
    } else if (val === 6) {
        // 废水排口
        searchWastewater();
    } else if (val === 4) {
        // 危废仓库
        searchWasteWarehouse();
    } else if (val === 5) {
        // 动态热力
        dynamicThermodynamicsBtnsShow.value = true;
        keduShow.value = true;
        dynamicActive.value = 106001;
        var today = new Date();
        var hour = ("0" + today.getHours()).slice(-2);
        selectTime.value = hour + ":00";
        var time = day.value + " " + selectTime.value + ":00";

        try {
            const res = await thermal_map({
                equipmentType: dynamicValue.value,
                end: time,
            });

            if (res.data.code == 200) {
                points = res.data.data.map((x) => {
                    return {
                        lat: x.latitude,
                        lng: x.longitude,
                        polluteLevelMax: x.polluteLevelMax,
                        count: x.sumValue,
                    };
                });
                pointsCesium = res.data.data.map((x) => {
                    return {
                        lnglat: [x.longitude, x.latitude],
                        value: x.sumValue,
                    };
                });
                pointsCesium_1 = res.data.data.map((x) => {
                    return {
                        ...x,
                        name: x.factorId,
                        id: x.factorId,
                        longitude: Number(x.longitude),
                        latitude: Number(x.latitude),
                    };
                });
                max = points[0].count;
                initHeatMap();
            } else {
                if (window.heatmap != null) {
                    window.heatmap.setMap(null);
                }
            }
        } catch (error) {
            console.error("获取热力图数据失败:", error);
        }
    }
};
//大气监测
const searchAtmosphericMonitoring = (uid) => {
    positionData.value = [];
    edevice_position({ device: 106001 }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                        run_status: item.run_status,
                    });
                }
            });
            if (window.toggle == 2) {
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.run_status == "105002") {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    selectLsit.forEach((item) => {
                        if (item.run_status == "105002") {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                }
                putIcons(positionNoGreyData, atmosphericMonitoringImg);
                putIcons(positionGreyData, atmosphericMonitoringGreyImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id.id);

                        atmosphericMonitoringId.value = pick.id.id;
                        atmosphericMonitoringDialogShow.value = true;
                        //proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                if (!uid) {
                    console.log(window.map1, positionData.value);
                    positionData.value.forEach((item) => {
                        var imgUrl1 =
                            "/static/poi/atmospheric-monitoring-new.svg";
                        console.log("ceshi", item.run_status);
                        if (item.run_status == "105002") {
                            imgUrl1 =
                                "/static/poi/atmospheric-monitoring-grey.svg";
                        }
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgUrl1, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            atmosphericMonitoringId.value = item.name;
                            atmosphericMonitoringDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        var imgUrl1 =
                            "/static/poi/atmospheric-monitoring-new.svg";
                        console.log("ceshi", item.run_status);
                        if (item.run_status == "105002") {
                            imgUrl1 =
                                "/static/poi/atmospheric-monitoring-grey.svg";
                        }
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgUrl1, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            atmosphericMonitoringId.value = item.name;
                            atmosphericMonitoringDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                }

                window.map1.setFitView();
            }
        }
    });
};
//废气
const searchWasteGas = (uid) => {
    positionData.value = [];
    //   wasteGasDialogShow.value = true;
    edevice_position({ device: 106006 }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                        run_status: item.run_status,
                    });
                }
            });
            if (window.toggle == 2) {
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.run_status == "105002") {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    selectLsit.forEach((item) => {
                        if (item.run_status == "105002") {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                }

                putIcons(positionNoGreyData, wasteGasOutletImg);
                putIcons(positionGreyData, wasteGasOutletGreyImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id.id);

                        wasteGasId.value = pick.id.id;
                        wasteGasDialogShow.value = true;
                        //proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                if (!uid) {
                    console.log(window.map1, positionData.value);
                    positionData.value.forEach((item) => {
                        var imgUrl1 = "/static/poi/waste-gas-outlet-new.svg";
                        console.log("ceshi", item.run_status);
                        if (item.run_status == "105002") {
                            imgUrl1 = "/static/poi/waste-gas-outlet-grey.svg";
                        }
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgUrl1, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            wasteGasId.value = item.name;
                            wasteGasDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        var imgUrl1 = "/static/poi/waste-gas-outlet-new.svg";
                        console.log("ceshi", item.run_status);
                        if (item.run_status == "105002") {
                            imgUrl1 = "/static/poi/waste-gas-outlet-grey.svg";
                        }
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgUrl1, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            wasteGasId.value = item.name;
                            wasteGasDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                }
                window.map1.setFitView();
            }
        }
    });
};
//雨水监测
const searchRainwater = (uid) => {
    positionData.value = [];
    edevice_position({ device: 106007 }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                        run_status: item.run_status,
                    });
                }

                console.log(positionData.value);
            });
            if (window.toggle == 2) {
                //不置灰数组
                let positionNoGreyData = [];
                let positionGreyData = [];
                if (!uid) {
                    positionData.value.forEach((item) => {
                        if (item.run_status == "105002") {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    selectLsit.forEach((item) => {
                        if (item.run_status == "105002") {
                            positionGreyData.push(item);
                        } else {
                            positionNoGreyData.push(item);
                        }
                    });
                }

                putIcons(positionNoGreyData, wastewaterOutletImg);
                putIcons(positionGreyData, wastewaterOutletGreyImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id.id);

                        rainwaterOutletId.value = pick.id.id;
                        rainwaterOutletDialogShow.value = true;
                        //proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                if (!uid) {
                    console.log(window.map1, positionData.value);
                    positionData.value.forEach((item) => {
                        var imgUrl1 = "/static/poi/wastewater-outlet-new.svg";
                        console.log("ceshi", item.run_status);
                        if (item.run_status == "105002") {
                            imgUrl1 = "/static/poi/wastewater-outlet-grey.svg";
                        }
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgUrl1, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            console.log(item.name);
                            rainwaterOutletId.value = item.name;
                            rainwaterOutletDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        var imgUrl1 = "/static/poi/wastewater-outlet-new.svg";
                        console.log("ceshi", item.run_status);
                        if (item.run_status == "105002") {
                            imgUrl1 = "/static/poi/wastewater-outlet-grey.svg";
                        }
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: imgUrl1, //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            console.log(item.name);
                            rainwaterOutletId.value = item.name;
                            rainwaterOutletDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                }
                window.map1.setFitView();
            }
        }
    });
};
//废水监测
const searchWastewater = (uid) => {
    positionData.value = [];
    edevice_position({ device: 106002 }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            if (window.toggle == 2) {
                if (uid) {
                    let selectLsit = resetData(positionData.value, "name", uid);
                    positionData.value = selectLsit;
                }
                putIcons(positionData.value, wastewaterOutletImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id.id);

                        wastewaterOutletId.value = pick.id.id;
                        wastewaterOutletDialogShow.value = true;
                        //proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                if (!uid) {
                    console.log(window.map1, positionData.value);

                    positionData.value.forEach((item) => {
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: "/static/poi/wastewater-outlet-new.svg", //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            wastewaterOutletId.value = item.name;
                            wastewaterOutletDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                } else {
                    let selectLsit = resetData(positionData.value, "id", uid);
                    selectLsit.forEach((item) => {
                        let marker = new AMap.Marker({
                            // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                            icon: new AMap.Icon({
                                size: new AMap.Size(72, 92), // 图标尺寸
                                image: "/static/poi/wastewater-outlet-new.svg", //绝对路径
                                imageSize: new AMap.Size(72, 92),
                            }),
                            position: [item.longitude, item.latitude],
                            offset: new AMap.Pixel(-36, -92),
                        });
                        marker.on("click", (e) => {
                            wastewaterOutletId.value = item.name;
                            wastewaterOutletDialogShow.value = true;
                            //proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                        });
                        marker.setMap(window.map1);
                    });
                }
                window.map1.setFitView();
            }
        }
    });
};
//危废仓库
const searchWasteWarehouse = () => {
    areaData.value = [];
    areaDataThree.value = [];
    //   warehouseDialogShow.value = true;
    waste_store_position({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            res.data.data.forEach((item) => {
                if (item.longitude != null && item.latitude != null) {
                    positionData.value.push({
                        name: item.id,
                        id: item.id,
                        longitude: Number(item.longitude),
                        latitude: Number(item.latitude),
                    });
                }
            });
            if (window.toggle == 2) {
                putIcons(positionData.value, wasteGasOutletImg);
                console.log(window.viewer.entities);
                // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                window.viewer.screenSpaceEventHandler.setInputAction(function (
                    e,
                ) {
                    var pick = viewer.scene.pick(e.position);
                    if (pick && pick.id) {
                        console.log(pick.id.id);

                        warehouseId.value = pick.id.id;
                        warehouseDialogShow.value = true;
                        //proxy.$loading.show();
                        window.viewer.camera.setView({
                            destination: Cesium.Cartesian3.fromDegrees(
                                pick.id._longitude,
                                pick.id._latitude,
                                800,
                            ),
                        });
                    }
                },
                Cesium.ScreenSpaceEventType.LEFT_CLICK);
            } else if (window.toggle == 3) {
                console.log(window.map1, positionData.value);
                positionData.value.forEach((item) => {
                    let marker = new AMap.Marker({
                        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                        icon: new AMap.Icon({
                            size: new AMap.Size(72, 92), // 图标尺寸
                            image: "/static/poi/waste-gas-outlet-new.svg", //绝对路径
                            imageSize: new AMap.Size(72, 92),
                        }),
                        position: [item.longitude, item.latitude],
                        offset: new AMap.Pixel(-36, -92),
                    });
                    marker.on("click", (e) => {
                        warehouseId.value = item.name;
                        warehouseDialogShow.value = true;
                        //proxy.$loading.show();
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                    });
                    marker.setMap(window.map1);
                });
                window.map1.setFitView();
            }
        }
    });
};
var heatmap;
const initHeatMap = () => {
    console.log(window.toggle, "地图模式");
    if (window.toggle == 1) {
        window.map1.clearMap();
        if (window.Locast != null) window.Locast.setMap(null);
        if (window.heatmap != null) {
            window.heatmap.setMap(null);
        }
        initBoundary();
        window.heatmap = new AMap.HeatMap(window.map1, {
            radius: 55, //给定半径
            opacity: [0, 0.8],
        });
        window.heatmap.setDataSet({
            data: points,
            max: max,
        });
        // marker.setMap(window.map1);
        for (let i = 0; i < points.length; i++) {
            let center = [];
            center.push(points[i].lng);
            center.push(points[i].lat);
            console.log(center);
            let circleMarker = new AMap.CircleMarker({
                center: center,
                radius: 35, //3D视图下，CircleMarker半径不要超过64px
                strokeColor: "",
                strokeWeight: 0,
                strokeOpacity: 0.5,
                // fillColor:circleColr,
                fillColor: "rgba(0,0,0, 0.1)",
                fillOpacity: 0.4,
                zIndex: 10,
                bubble: true,
                cursor: "pointer",
                clickable: true,
                exData: points[i],
            });
            circleMarker.setMap(window.map1);
        }
        window.map1.setFitView();
    } else if (window.toggle == 3) {
        console.log(points, "points");
        //先把数据按照污染程度进行区分
        // 严重---#450808
        // 重度---#452108
        // 中度----#453608
        // 轻度----#083045
        // 剩余的-----#4DCB62

        let serious = []; // 严重-
        let severe = []; // 重度
        let moderate = []; // 中度
        let mild = []; // 轻度
        let other = []; // 剩余的
        points.forEach((item) => {
            if (item.polluteLevelMax == "严重污染") {
                serious.push(item);
            } else if (item.polluteLevelMax == "重度污染") {
                severe.push(item);
            } else if (item.polluteLevelMax == "中度污染") {
                moderate.push(item);
            } else if (item.polluteLevelMax == "轻度污染") {
                mild.push(item);
            } else {
                other.push(item);
            }
        });
        window.Locast = new Loca.Container({
            map: window.map1,
        });
        console.log(window.Locast);
        // let loca = new Loca.Container({
        //   map:window.map1,
        // });
        //严重数据
        if (serious.length) {
            let seriousList = serious.map(function (location) {
                return {
                    type: "Feature",
                    properties: {
                        // name: location.name
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [location.lng, location.lat],
                    },
                };
            });
            let seriousgeo = new Loca.GeoJSONSource({
                data: {
                    type: "FeatureCollection",
                    features: seriousList,
                },
            });
            window.seriousst = new Loca.ScatterLayer({
                loca: window.Locast,
                zIndex: 111,
                opacity: 1,
                visible: true,
                zooms: [2, 22],
            });
            window.seriousst.setSource(seriousgeo);
            window.seriousst.setStyle({
                // color: 'rgba(244,244,244,1)',
                // unit: 'meter',
                // size: [150, 150],
                // borderWidth: 0,
                unit: "meter",
                size: [260, 260],
                borderWidth: 0,
                texture:
                    "https://a.amap.com/Loca/static/loca-v2/demos/images/breath_red.png",
                // texture: '#FFFF00',
                duration: 500,
                animate: true,
            });
            window.Locast.add(window.seriousst);
        }

        //重度
        if (severe.length) {
            let severeList = severe.map(function (location) {
                return {
                    type: "Feature",
                    properties: {
                        // name: location.name
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [location.lng, location.lat],
                    },
                };
            });
            let severegeo = new Loca.GeoJSONSource({
                data: {
                    type: "FeatureCollection",
                    features: severeList,
                },
            });
            window.severest = new Loca.ScatterLayer({
                loca: window.Locast,
                zIndex: 111,
                opacity: 1,
                visible: true,
                zooms: [2, 22],
            });
            window.severest.setSource(severegeo);
            window.severest.setStyle({
                // color: 'rgba(244,244,244,1)',
                // unit: 'meter',
                // size: [150, 150],
                // borderWidth: 0,
                unit: "meter",
                size: [260, 260],
                borderWidth: 0,
                texture:
                    "https://a.amap.com/Loca/static/loca-v2/demos/images/breath_red.png",
                // texture: '#FFFF00',
                duration: 500,
                animate: true,
            });
            window.Locast.add(window.severest);
        }

        //中度
        if (moderate.length) {
            let moderateList = moderate.map(function (location) {
                return {
                    type: "Feature",
                    properties: {
                        // name: location.name
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [location.lng, location.lat],
                    },
                };
            });
            let moderategeo = new Loca.GeoJSONSource({
                data: {
                    type: "FeatureCollection",
                    features: moderateList,
                },
            });
            window.moderatest = new Loca.ScatterLayer({
                loca: window.Locast,
                zIndex: 111,
                opacity: 1,
                visible: true,
                zooms: [2, 22],
            });
            window.moderatest.setSource(moderategeo);
            window.moderatest.setStyle({
                // color: 'rgba(244,244,244,1)',
                // unit: 'meter',
                // size: [150, 150],
                // borderWidth: 0,
                unit: "meter",
                size: [260, 260],
                borderWidth: 0,
                texture:
                    "https://a.amap.com/Loca/static/loca-v2/demos/images/breath_yellow.png",
                // texture: '#FFFF00',
                duration: 500,
                animate: true,
            });
            window.Locast.add(window.moderatest);
        }

        //轻度
        if (mild.length) {
            let mildList = mild.map(function (location) {
                return {
                    type: "Feature",
                    properties: {
                        // name: location.name
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [location.lng, location.lat],
                    },
                };
            });
            let mildgeo = new Loca.GeoJSONSource({
                data: {
                    type: "FeatureCollection",
                    features: mildList,
                },
            });
            window.mildst = new Loca.ScatterLayer({
                loca: window.Locast,
                zIndex: 111,
                opacity: 1,
                visible: true,
                zooms: [2, 22],
            });
            window.mildst.setSource(mildgeo);
            window.mildst.setStyle({
                // color: 'rgba(244,244,244,1)',
                // unit: 'meter',
                // size: [150, 150],
                // borderWidth: 0,
                unit: "meter",
                size: [260, 260],
                borderWidth: 0,
                texture:
                    "https://a.amap.com/Loca/static/loca-v2/demos/images/breath_yellow.png",
                // texture: '#FFFF00',
                duration: 500,
                animate: true,
            });
            window.Locast.add(window.mildst);
        }

        //其他
        if (other.length) {
            let otherList = other.map(function (location) {
                return {
                    type: "Feature",
                    properties: {
                        // name: location.name
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [location.lng, location.lat],
                    },
                };
            });
            let othergeo = new Loca.GeoJSONSource({
                data: {
                    type: "FeatureCollection",
                    features: otherList,
                },
            });
            window.othergeost = new Loca.ScatterLayer({
                loca: window.Locast,
                zIndex: 111,
                opacity: 1,
                visible: true,
                zooms: [2, 22],
            });
            window.othergeost.setSource(othergeo);
            window.othergeost.setStyle({
                color: "rgba(43,156,75,1)",
                unit: "meter",
                size: [150, 150],
                borderWidth: 0,
                // unit: 'meter',
                // size: [260, 260],
                // borderWidth: 0,
                // texture: 'https://a.amap.com/Loca/static/loca-v2/demos/images/breath_red.png',
                //
                // duration: 500,
                // animate: true
            });
            window.Locast.add(window.othergeost);
        }

        //     let features = points.map(function(location) {
        //       return {
        //         type: 'Feature',
        //         properties: {
        //           // name: location.name
        //         },
        //         geometry: {
        //           type: 'Point',
        //           coordinates: [location.lng, location.lat]
        //         }
        //       };
        //     });
        // // 创建 GeoJSON 数据源，并设置数据
        //     let geo = new Loca.GeoJSONSource({
        //       data: {
        //         type: 'FeatureCollection',
        //         features: features
        //       }
        //     });
        //     // let scatter = new Loca.ScatterLayer({
        //     window.scatterst = new Loca.ScatterLayer({
        //       loca:window.Locast,
        //       zIndex: 111,
        //       opacity: 1,
        //       visible: true,
        //       zooms: [2, 22],
        //     });
        //     window.scatterst.setSource(geo);
        //     let colors ='https://a.amap.com/Loca/static/loca-v2/demos/images/breath_red.png'
        //     let colors1 ='https://a.amap.com/Loca/static/loca-v2/demos/images/breath_green.png'
        //     let colors2 ='https://a.amap.com/Loca/static/loca-v2/demos/images/breath_yellow.png'
        //
        //     window.scatterst.setStyle({
        //       // color: 'rgba(244,244,244,1)',
        //       // unit: 'meter',
        //       // size: [150, 150],
        //       // borderWidth: 0,
        //       unit: 'meter',
        //       size: [260, 260],
        //       borderWidth: 0,
        //       texture: colors1,
        //       // texture: '#FFFF00',
        //       duration: 500,
        //       animate: true
        //     });
        //     // console.log(scatter.value,"scatter")
        //     window.scatterst.on('click', (event)=> {
        //       console.log(event,"点击事件");
        //     });

        // 启动渲染动画
        window.Locast.animate.start();

        window.map1.add(window.scatterst);
    } else {
        // let list = [];
        // for (let i = 0; i < 100; i++) {
        //   list.push({
        //     "lnglat": [
        //       117.28 + Math.random() * (Math.random() > 0.5 ? 1 : -1),
        //       31.923 + Math.random() * (Math.random() > 0.5 ? 1 : -1)
        //     ],
        //     "value": 100 * Math.random()
        //   })
        // }
        // new Heatmap(viewer, {
        //   list: list
        // })
        // new Heatmap(viewer, {
        //   list: pointsCesium
        // })
        // console.log(pointsCesium_1)
        // putIcons(pointsCesium_1,atmosphericMonitoringImg)
    }
};
// 这些变量已经在上面声明了，删除重复声明
const clickTime = (val) => {
    console.log(val);
    selectTime.value = val;
    var today = new Date();
    var month = ("0" + (today.getMonth() + 1)).slice(-2);
    var day1 = ("0" + today.getDate()).slice(-2);
    var day = today.getFullYear() + "-" + month + "-" + day1;
    var time = day + " " + selectTime.value + ":00";
    console.log(time);
    getthermalMap({
        equipmentType: dynamicValue.value,
        end: time,
    }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            points = res.data.data.map((x) => {
                return {
                    lat: x.latitude,
                    lng: x.longitude,
                    polluteLevelMax: x.polluteLevelMax,
                    count: x.monitor_value,
                };
            });
            max = points[0].count;

            initHeatMap();
        } else {
            console.log(1111);
            console.log(window.heatmapmap);
            if (window.heatmap != null) {
                window.heatmap.setMap(null);
            }
        }
    });
};
// import aliveImg from "../../../assets/images/card/icon1.png";
// import atmosphericMonitoringImg from "/static/poi/atmospheric-monitoring-new.svg";
// import atmosphericMonitoringGreyImg from "/static/poi/atmospheric-monitoring-grey.svg";
// import wasteGasOutletImg from "/static/poi/waste-gas-outlet-new.svg";
// import wasteGasOutletGreyImg from "/static/poi/waste-gas-outlet-grey.svg";
// import wastewaterOutletImg from "/static/poi/wastewater-outlet-new.svg";
// import wastewaterOutletGreyImg from "/static/poi/wastewater-outlet-grey.svg";
// import hazardouswasteWarehouseImg from "../../../assets/images/poi/hazardous-waste-warehouse.svg";
// import Heatmap from "../../../heatmap/heatmap";

const putIcons = (_datas, img, _parent) => {
    console.log("添加视频icon");
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        console.log(data);
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        const entity = window.viewer.entities.add({
            name: "cameraPoint",
            // 参数顺序：经度、纬度
            id: data.name,
            longitude: Number(data.longitude) - 0.0062,
            latitude: Number(data.latitude) - 0.00085,
            position: Cesium.Cartesian3.fromDegrees(
                Number(data.longitude) - 0.0062,
                Number(data.latitude) - 0.00085,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 72,
                height: 92,
                pixelOffset: new Cesium.Cartesian2(0, -46),
                //   eyeOffset: new Cesium.Cartesian3(0, -44, 0),
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            type: "text", // 自定义属性
        });
        window.viewer.zoomTo(entity);
        console.log(999999999999999999999999999999999);
    }
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
onUnmounted(() => {
    proxy.$bus.off("change_toggle");
    proxy.$bus.off("clicJump1");
});

onMounted(() => {
    // 监听地图切换事件
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
        clearMapEntities();
        initBoundary();
        dynamicActive.value = 0;
    });

    // 初始化
    initBoundary();
    fetchEnterpriseData();
    showFlag.value = window.toggle;
    var today = new Date();
    var month = ("0" + (today.getMonth() + 1)).slice(-2);
    var day1 = ("0" + today.getDate()).slice(-2);
    day.value = today.getFullYear() + "-" + month + "-" + day1;
    var hour = ("0" + today.getHours()).slice(-2);
    console.log(hour);
    selectTime.value = hour + ":00";
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
    proxy.$bus.on("clicJump1", (val) => {
        console.log(val);
        if (val.tabFlag == "环保管理") {
            switch (val.tag) {
                case "环保大气质量告警":
                    searchAtmosphericMonitoring(val.equipmentId);
                    break;
                case "环保废气质量告警":
                    searchWasteGas(val.equipmentId);
                    break;
                case "环保雨水质量告警":
                    searchRainwater(val.equipmentId);
                    break;
                case "环保污水质量告警":
                    searchWastewater(val.equipmentId);
                    break;
                default:
                    break;
            }
        }
    });
    // var time = day +' '+selectTime.value+':00'
    // console.log(time);
    // thermal_map({ type: 106007, acquisition_time: time }).then((res) => {
    //     if (res.data.success && res.data.data && res.data.data.length) {
    //         points= res.data.data.map((x) => {
    //     return {
    //         lat: x.latitude,
    //         lng: x.longitude,
    //         count: x.monitor_value
    //     };
    //   });
    //     }
    // })
});
</script>

<style lang="less" scoped>
.nav-all-wrapper{
    // width: 928px;
    height: 862px;
    position: absolute;
    z-index: 1000;
    top: 96px;
    left: 496px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: none;
    .nav-all-btns{
        display: flex;
        flex-direction: column;
        gap:24px;
        pointer-events: auto;
        .btn {
            width: 144px;
            height: 34px;
            background: url("@/assets/newImages/Nav/nav-btn-blue.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
        }

        .active-btn {
            width: 144px;
            height: 34px;
            background: url("@/assets/newImages/Nav/nav-active-btn-yellow.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
        }
        .icon {
            width: 24px;
            height: 24px;
            // margin: auto 0;
            // margin-top: 4px;
            margin-left: 12px;
        }

        .text {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            vertical-align: middle;
            color: #FFFFFF;

            margin: auto 0;
            margin-left: 24px;
        }
    }
    .input-wrapper {
        pointer-events: auto;

    .el-input {
                    width: 210px;
                    height: 40px;
                }
                .el-select {
                    width: 210px;
                    height: 40px;

                }
                :deep(.el-cascader) {
                    width: 210px !important;
                }
                :deep(.el-select__wrapper){
                    height: 40px !important;
                    padding: 10px 12px !important;
                }
                .el-date-editor {
                    width: 210px;
                }

                :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
                /* 复选框容器样式 */
                .checkbox-container {
                    display: flex;
                    align-items: center;
                }

                .custom-checkbox-group {
                    display: flex;
                    flex-wrap: wrap;
                }

                /* 黄色复选框样式 - 选中状态 */
                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
                    background-color: rgba(255, 198, 26, 0.3) !important;
                    border-color: rgba(255, 198, 26, 1) !important;
                }

                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner::after) {
                    border-color: rgba(255, 255, 255, 1) !important;
                }

}



}

.kedu {
    width: 694px;
    height: 48px;
    position: absolute;
    top: 839px;
    left: 663px;
    display: flex;
    gap: 30px;

    .kedu-item {
        width: 10px;

        .kedu-text {
            margin-left: -18px;
            color: var(--unnamed, #47ebeb);
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        .kedu-text-no {
            margin-left: -18px;
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        .kedu-text-none {
            width: 2px;
            height: 22px;
        }

        .kedu-liner {
            margin-top: 8px;
            width: 1px;
            height: 12px;
            // flex-shrink: 0;
            // stroke-width: 1px;
            // color: #fff;
            background-color: #fff;
        }

        .kedu-liner-no {
            margin-top: 8px;
            width: 1px;
            height: 12px;
            // flex-shrink: 0;
            // stroke-width: 1px;
            // color: #fff;
            background-color: var(--unnamed, #47ebeb);
        }

        .kedu-icon {
            margin-left: -5px;
            margin-top: 8px;
            // color: #fff;
            width: 12px;
            height: 8px;
            background: url("../../../assets/images/icon/kedu-icon.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }
    }

    // flex-shrink: 0;
    // // background-color: red;
    // background: url("../../../assets/images/icon/kedu.svg")
    //     no-repeat;
    //   background-size: cover;
    //   background-position: center;
    :deep(.el-slider__runway) {
        background-color: transparent !important;
    }

    :deep(.el-slider__bar) {
        background-color: transparent !important;
    }

    :deep(.el-tooltip__trigger) {
        margin-top: 15px;
    }

    :deep(.el-slider__runway) {
        height: 0px;
    }

    :deep(.el-slider__bar) {
        height: 0px;
    }

    :deep(.el-slider__button-wrapper) {
        top: 22px;
    }

    :deep(.el-slider__button) {
        border: none;
        background-color: transparent;
    }

    :deep(.el-slider__button::after, .el-slider__button.dragging::after) {
        content: "";
        display: block;
        position: absolute;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #ff9901;
        top: 3px;
        left: 12px;
    }

    :deep(
            .el-slider__button-wrapper .el-tooltip,
            .el-slider__button-wrapper::after
        ) {
        display: block;
    }

    :deep(.el-slider__button.hover, .el-slider__button.dragging) {
        transform: scale(1);
    }

    :deep(.el-slider__input) {
        margin-top: 3px;
        width: 50%;
        position: absolute;
        right: 25%;
        top: 170px;
    }

    :deep(.el-slider__runway.show-input) {
        margin-right: 0;
        width: auto;
    }
}

.dynamic-thermodynamics-btns {
    display: flex;
    gap: 8px;
    color: #fff;
    position: absolute;
    top: 925px;
    left: 623px;
    text-align: center;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 157.143% */

    .dynamic-btn {
        width: 48px;
        height: 32px;
        background: url("../../../assets/images/jump-btn/dynamic-btn.svg")
            no-repeat;

        background-size: cover;
        background-position: center;
    }

    .active-dynamic-btn {
        width: 48px;
        height: 32px;
        background: url("../../../assets/images/jump-btn/active-dynamic-btn.svg")
            no-repeat;

        background-size: cover;
        background-position: center;
    }
}
</style>
