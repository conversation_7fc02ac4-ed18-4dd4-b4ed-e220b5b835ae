<template>
    <div :class="`emergency-archives${size}`">
        <div class="top-btns">
                <div
                    :class="innerActive === 1? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(1)"
                >
                    应急资源
                </div>
                <div
                    :class="innerActive === 2? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(2)"
                >
                    应急预案
                </div>
                <div
                    :class="innerActive === 3? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(3)"
                >
                    应急演练
                </div>
            </div>
        
            <div class="resources alarm" v-if="innerActive == 1">
                <el-table
                    class="tablebox"
                    :data="tableDataFirst"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="num"
                        label="序号"
                        style="width: 5%"
                    />
                    <el-table-column
                        prop="emergency_team"
                        label="应急队伍"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="emergency_matter"
                        label="应急物资"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="emergency_equip"
                        label="应急装备"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="event_type"
                        label="应对事件"
                        show-overflow-tooltip
                    />
                </el-table>
                <el-pagination
                    class="pagination"
                    background
                    layout="->,total, prev, pager, next"
                    :total="totalFirst"
                    v-model:currentPage="pageNumFirst"
                    :page-size="pageSizeFirst"
                    @current-change="handleCurrentChangeFirst"
                />
            </div>
            <div class="plan alarm" v-else-if="innerActive == 2">
                <el-table
                    class="tablebox"
                    :data="tableDataSecond"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="num"
                        label="序号"
                        style="width: 5%"
                    />
                    <el-table-column
                        prop="plan_name"
                        label="预案名称"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="plan_type"
                        label="预案类型"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="plan_update_date"
                        label="预案更新时间"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="plan_valid"
                        label="预案有效期至"
                        show-overflow-tooltip
                    />
                </el-table>
                <el-pagination
                    class="pagination"
                    background
                    layout="->,total, prev, pager, next"
                    :total="totalSecond"
                    v-model:currentPage="pageNumSecond"
                    :page-size="pageSizeSecond"
                    @current-change="handleCurrentChangeSecond"
                />
            </div>
            <div class="drill alarm" v-else>
                <div class="bottom-table">
                    <el-table
                        class="tablebox"
                        :data="tableDataThird"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="num"
                            label="序号"
                            style="width: 5%"
                        />
                        <el-table-column
                            prop="rehearsal_name"
                            label="演练名称"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="sd_rehearsal_form"
                            label="演练形式"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="rehearsal_date"
                            label="演练时间"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="rehearsal_number"
                            label="演练人数"
                            show-overflow-tooltip
                        />
                    </el-table>
                    <el-pagination
                        class="pagination"
                        background
                        layout="->,total, prev, pager, next"
                        :total="totalThird"
                        v-model:currentPage="pageNumThird"
                        :page-size="pageSizeThird"
                        @current-change="handleCurrentChangeThird"
                    />
                </div>
            </div>
        
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
import {
    emergency_management,
    emergency_plan,
    emergency_rehearsal,
} from "../../../../assets/js/api/parkArchives";
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const emit = defineEmits(["sizeChange"]);
const size = ref(1);
const innerActive = ref(1);
watch(innerActive, (a, b) => {
    //   console.log(a, b);
    //   if (a == 1) {
    //     emit("sizeChange", 6);
    //   } else if (a == 2) {
    //     emit("sizeChange", 5);
    //   } else if (a == 3) {
    //     emit("sizeChange", 4);
    //   }
});
const tableDataFirst = ref([]);
const middleTableDataFirst = ref([]);
const totalFirst = ref(0);
const pageNumFirst = ref(1);
const pageSizeFirst = ref(3);
const tableDataSecond = ref([]);
const middleTableDataSecond = ref([]);
const totalSecond = ref(0);
const pageNumSecond = ref(1);
const pageSizeSecond = ref(3);
const tableDataThird = ref([]);
const middleTableDataThird = ref([]);
const totalThird = ref(0);
const pageNumThird = ref(1);
const pageSizeThird = ref(3);
const selectInnerType = (type) => {
    innerActive.value = type;
    if (type == 1) {
        middleTableDataFirst.value = [];
        emergency_management({ enterprise_name: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalFirst.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableDataFirst.value.push(item);
                });
                searchEmergencyManagement();
                console.log(middleTableDataFirst.value);
            }
        });
    } else if (type == 2) {
        middleTableDataSecond.value = [];
        emergency_plan({ enterprise_name: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalSecond.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableDataSecond.value.push(item);
                });
                searchEmergencyPlan();
                console.log(middleTableDataSecond.value);
            }
        });
    } else {
        middleTableDataThird.value = [];
        emergency_rehearsal({ enterprise_name: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalThird.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableDataThird.value.push(item);
                });
                searchEmergencyRehearsal();
                console.log(middleTableDataThird.value);
            }
        });
    }
};
const searchEmergencyManagement = () => {
    tableDataFirst.value = [];
    for (let i = 0; i < pageSizeFirst.value; i++) {
        let pageIndex = (pageNumFirst.value - 1) * pageSizeFirst.value + i;
        console.log(pageIndex);
        if (pageIndex < totalFirst.value) {
            tableDataFirst.value[i] =
                middleTableDataFirst.value[
                    (pageNumFirst.value - 1) * pageSizeFirst.value + i
                ];
        }
    }
};
const searchEmergencyPlan = () => {
    tableDataSecond.value = [];
    for (let i = 0; i < pageSizeSecond.value; i++) {
        let pageIndex = (pageNumSecond.value - 1) * pageSizeSecond.value + i;
        console.log(pageIndex);
        if (pageIndex < totalSecond.value) {
            tableDataSecond.value[i] =
                middleTableDataSecond.value[
                    (pageNumSecond.value - 1) * pageSizeSecond.value + i
                ];
        }
    }
};
const searchEmergencyRehearsal = () => {
    tableDataThird.value = [];
    for (let i = 0; i < pageSizeThird.value; i++) {
        let pageIndex = (pageNumThird.value - 1) * pageSizeThird.value + i;
        console.log(pageIndex);
        if (pageIndex < totalThird.value) {
            tableDataThird.value[i] =
                middleTableDataThird.value[
                    (pageNumThird.value - 1) * pageSizeThird.value + i
                ];
        }
    }
};
const handleCurrentChangeFirst = (e) => {
    pageNumFirst.value = e;
    searchEmergencyManagement();
};
const handleCurrentChangeSecond = (e) => {
    pageNumSecond.value = e;
    searchEmergencyPlan();
};
const handleCurrentChangeThird = (e) => {
    pageNumThird.value = e;
    searchEmergencyRehearsal();
};
onMounted(() => {
    middleTableDataFirst.value = [];
    emergency_management({ enterprise_name: pickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            totalFirst.value = res.data.data.length;
            res.data.data.forEach((item, index) => {
                Reflect.set(item, "num", index + 1);
                middleTableDataFirst.value.push(item);
            });
            searchEmergencyManagement();
            console.log(middleTableDataFirst.value);
        }
    });
});
</script>

<style lang="less" scoped>
.emergency-archives1 {
    width: 792px;
    height: 328px;
    margin-top: 16px;
    // background-color: aquamarine;
}
.emergency-archives2 {
    width: 792px;
    height: 184px;
    margin-top: 16px;
    // background-color: aquamarine;
}
 .top-btns{
        display: flex;
        gap:8px;
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 16px;
text-align: center;

        .active-top-btn{
           
border-radius: 2px;
padding: 4px 8px;
border: 1px solid #FFC61A;
color:#FFC61A;
        }
        .top-btn{
            border-radius: 2px;
padding: 4px 8px;
border: 1px solid #80EAFF;
color:#80EAFF;
        }
    }
.resources {
    width: 792px;
    height: 500px;
    // margin-top: 16px;
    // background-color: antiquewhite;
}
.plan {
    width: 792px;
    height: 140px;
    // background-color:#30ABE8;
    margin-top: 12px;
}
.drill {
    width: 792px;
    height: 372px;
    // background-color:#30ABE8;
    margin-top: 12px;
}
.alarm {
    width: 792px;
    height: 288px;
    margin-top: 12px;
    .tablebox {
        //表格四个边框的颜色
        border: 1px solid #30abe8 !important;
        th.el-table__cell {
            border: none !important;
        }
    }
    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        border-bottom: 1px solid #30abe8 !important;
        height: 36px;
        background-color: rgba(48, 171, 232, 0.1) !important;
        color: #47ebeb;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        // border-color: #30ABE8 !important;
    }
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: 1px solid #30abe8 !important;
            border-right: 1px solid #30abe8 !important;
        }
    }
    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
        // border-color: #30ABE8 !important;
    }
    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    :deep(.el-table__cell) {
        border-right: 1px solid #30abe8 !important ;
        border-bottom: 1px solid #30abe8 !important  ;
    }
    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }
    .pagination {
        margin-top: 16px;
    }
    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 2px;
        border: 1px solid #47ebeb;
        background: linear-gradient(
            180deg,
            rgba(71, 235, 235, 0) 50%,
            rgba(71, 235, 235, 0.45) 100%
        );
        color: #47ebeb;
        text-align: center;

        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 2px;
        border: 1px solid #47ebeb;
        background: linear-gradient(
            180deg,
            rgba(71, 235, 235, 0) 50%,
            rgba(71, 235, 235, 0.45) 100%
        );
        color: #47ebeb;
        text-align: center;

        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination .btn-next) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination .btn-prev) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination__total) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}
.bottom-title {
    width: 70px;
    height: 24px;
    display: flex;
    gap: 4px;
    margin-top: 16px;
    .icon {
        width: 2px;
        height: 8px;
        background: #47ebeb;
        margin: auto 0;
    }
    .text {
        color: #47ebeb;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
    }
}
.bottom-table {
    width: 792px;
    height: 144px;
    margin-top: 12px;
}
.bottom-table1 {
    width: 792px;
    height: 108px;
    margin-top: 12px;
}
table {
    border-collapse: collapse;
}
table td {
    border-style: solid;
    border-width: 1px;
    border-color: #30abe8;
}

.title1 {
    width: 240px;
    height: 36px;
    padding: 0 12px;
    color: #47ebeb;
    text-align: left;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    background-color: rgba(48, 171, 232, 0.1);
}
.value1 {
    width: 240px;
    height: 36px;
    padding: 0 12px;
    color: #fff;
    text-align: justify;
    font-family: Noto Sans SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
}
</style>
