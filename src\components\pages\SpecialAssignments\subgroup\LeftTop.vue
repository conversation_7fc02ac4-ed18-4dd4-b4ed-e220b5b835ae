<template>
    <TwoCard title="特殊作业总览">
        <template v-slot:title>
            <div class="title-bar">
                <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
                <div class="title-bar-right"  style="cursor: pointer;" @click="openDialog('specialWorkDialogShow','所有')">
                    <div>查看详情</div>
                    <img class="right-arrow" src="@/assets/newImages/right-arrow.svg">
                </div>
            </div>
        </template>
        <template v-slot>
            <div class="overview-special-wrapper">
              <div class="top">
                <div class="top-item top-item-1" style="cursor: pointer;" @click="openDialog('specialWorkDialogShow','已报备')">
                    <div class="top-item-content">
                        <div class="top-title">报备总数</div>
                        <div class="top-num">{{ topData.sum }}</div>
                    </div>
                </div>
                <div class="top-item top-item-2" style="cursor: pointer;" @click="openDialog('specialWorkDialogShow','已开票')">
                    <div class="top-item-content">
                        <div class="top-title">开票总数</div>
                        <div class="top-num">{{ topData.bill }}</div>
                    </div>
                </div>
                <div class="top-item top-item-3" style="cursor: pointer;" @click="openDialog('specialWorkDialogShow','进行中')">
                    <div class="top-item-content">
                        <div class="top-title">作业中总数</div>
                        <div class="top-num">{{ topData.current_work }}</div>
                    </div>
                </div>
                <div class="top-item top-item-4" style="cursor: pointer;" @click="openDialog('specialWorkDialogShow','已完工')">
                    <div class="top-item-content">
                        <div class="top-title">完工总数</div>
                        <div class="top-num">{{topData.final_work}}</div>
                    </div>
                </div>
              </div>
              <div class="bottom">
                <div class="list-header">
                    <div class="td1">作业类别</div>
                    <div class="td2">报备</div>
                    <div class="td3">开票</div>
                    <div class="td4">作业中</div>
                    <div class="td5">完工</div>
                </div>   
                <div v-if="noDataFlag" class="list-content">
                    <div class="img-no">暂无数据</div>
                </div>
                <div v-else>
                    <autoScroll class="card-container" :step="2" :deltaTime="100" v-if="list&&list.length>8">   
                        <div class="list-content">
                            <div class="list-item" v-for="(item,index) in list" :key="index">
                                <div class="td1">{{ item.risk_type }}</div>
                                <div class="td2">{{item.sum}}</div>
                                <div class="td3">{{item.bill}}</div>
                                <div class="td4">{{item.current_work}}</div>
                                <div class="td5">{{item.final_work}}</div>
                            </div>
                        </div>
                    </autoScroll>  
                    <div v-else class="list-content">
                        <div class="list-item" v-for="(item,index) in list" :key="index">
                            <div class="td1">{{ item.risk_type }}</div>
                                <div class="td2">{{item.sum}}</div>
                                <div class="td3">{{item.bill}}</div>
                                <div class="td4">{{item.current_work}}</div>
                                <div class="td5">{{item.final_work}}</div>
                        </div>
                    </div>
                </div>
              </div>
            </div>
        </template>
    </TwoCard>
</template>

<script setup>
import TwoCard from "@/components/commenNew/TwoCard.vue";
import selectVue from "@/components/commenNew/selectVue.vue";
import autoScroll from "@/components/commenNew/autoScroll.vue";
import {
    ref,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    computed,
    defineEmits
} from "vue";
import { overview,overview_sum } from "@/assets/js/api/specialAssignments.js";
import LeftTopJson from './json/LeftTop.json';
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val,val2) => {
    proxy.$emit('openDialog',val,nowChoose.value,val2);
}
//按钮选项
const btnOptions = ref([
    {
        label:'今日',
        value:1
    },
    {
        label:'本周',
        value:2
    },
    {
        label:'本月',
        value:3
    },
    {
        label:'本年',
        value:4
    }
])
//按钮
let chooseValue = ref('本年');
//暂无数据展示
let noDataFlag = ref(false);
//当前选择
let nowChoose = ref(4);
const selectBtn = (val) => {
    console.log(val,'vallllllllll');
    nowChoose.value=val.value;
    getData(val.value);
};
//顶部数据
const topData = ref({
    bill:0,
    current_work:0,
    final_work:0,
    sum:0
});
//列表
const list = ref([]);
//获取日期时间
// // 定义日期变量
// const currentDate = ref(new Date());

// // 获取本周第一天（周一）
// const getFirstDayOfWeek = (date) => {
//     const day = date.getDay() || 7;
//     const diff = date.getDate() - day + 1;
//     return new Date(date.setDate(diff));
// };

// // 定义时间变量
// const currentDate = ref(new Date());

// // 格式化日期时间函数
// const formatDateTime = (date) => {
//     const year = date.getFullYear();
//     const month = String(date.getMonth() + 1).padStart(2, '0');
//     const day = String(date.getDate()).padStart(2, '0');
//     const hours = String(date.getHours()).padStart(2, '0');
//     const minutes = String(date.getMinutes()).padStart(2, '0');
//     const seconds = String(date.getSeconds()).padStart(2, '0');
    
//     return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
// };

// // 今天23:59:59
// const todayEnd = computed(() => {
//     const date = new Date(currentDate.value);
//     date.setHours(23, 59, 59, 999);
//     return formatDateTime(date);
// });

// // 今天00:00:00
// const todayStart = computed(() => {
//     const date = new Date(currentDate.value);
//     date.setHours(0, 0, 0, 0);
//     return formatDateTime(date);
// });

// // 本周第一天00:00:00
// const weekStart = computed(() => {
//     const date = getFirstDayOfWeek(new Date(currentDate.value));
//     date.setHours(0, 0, 0, 0);
//     return formatDateTime(date);
// });

// // 本月第一天00:00:00
// const monthStart = computed(() => {
//     const date = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1);
//     date.setHours(0, 0, 0, 0);
//     return formatDateTime(date);
// });

// // 本年第一天00:00:00
// const yearStart = computed(() => {
//     const date = new Date(currentDate.value.getFullYear(), 0, 1);
//     date.setHours(0, 0, 0, 0);
//     return formatDateTime(date);
// });
//获取日期
// 定义日期变量
const currentDate = ref(new Date());
const firstDayOfMonth = ref(new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1));
const firstDayOfYear = ref(new Date(currentDate.value.getFullYear(), 0, 1));

// 格式化日期函数
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 获取格式化后的日期
const today = ref(formatDate(currentDate.value));
const monthStart = ref(formatDate(firstDayOfMonth.value));
const yearStart = ref(formatDate(firstDayOfYear.value));

// 获取本周第一天（周一）
const getFirstDayOfWeek = (date) => {
    const day = date.getDay() || 7; // 获取星期几，将周日的0转换为7
    const diff = date.getDate() - day + 1; // 计算到本周一的天数差
    return new Date(date.setDate(diff)); // 设置到本周一
};

// 定义本周第一天变量
const firstDayOfWeek = ref(formatDate(getFirstDayOfWeek(new Date())));
//获取数据
const getData = (val) => {
    let beginTime = null;
    let endTime = null;
    list.value = [];
    noDataFlag.value = true;
    topData.value = {
        bill:0,
        current_work:0,
        final_work:0,
        sum:0
    }
    if(val==1){
        beginTime = today.value;
        endTime = today.value;
    }else if(val==2){
        beginTime = firstDayOfWeek.value;
        endTime = today.value;
    }else if(val==3){
        beginTime = monthStart.value;
        endTime = today.value;
    }else if(val==4){
        beginTime = yearStart.value;
        endTime = today.value;
    }
    overview({
        beginTime:beginTime,
        endTime:endTime
    }).then(res=>{
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            topData.value = res.data.data[0];
        }
    })
    overview_sum({
        beginTime:beginTime,
        endTime:endTime
    }).then(res=>{
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            // list.value = res.data.data;
            list.value = LeftTopJson.data;
            noDataFlag.value = false;
        }
    })
}
onMounted(() => {
console.log(today.value,monthStart.value,yearStart.value,firstDayOfWeek.value,'firstDayOfWeek');
getData(4);
});

onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>
.title-bar{
    display: flex;
    gap: 16px;

    .title-bar-right{
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: center;
color: #80EAFF;

        display: flex;
        gap: 4px;
        .right-arrow{
            width: 16px;
            height: 16px;
            margin: auto;
        }
    }
}
.overview-special-wrapper{
    width: 416px;
height: 552px;
padding: 16px;
display: flex;
flex-direction: column;
gap: 16px;
.top{
    width: 416px;
height: 162px;
display: flex;
gap: 16px;
flex-wrap: wrap;
.top-item{
    width: 200px;
height: 75px;
.top-item-content{
    margin-left: 83px;
    margin-top: 9px;
    text-align: left;
    .top-title{
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 100%;
color: #FFFFFF;

    }
    .top-num{
        font-family: DIN Alternate;
font-weight: 700;
font-size: 20px;
line-height: 28px;
color: #FFFFFF;
    }
}
}
.top-item-1{
    background: url("@/assets/newImages/SpecialAssignments/overview1.svg") no-repeat;
            background-size: cover;
            background-position: center;
}
.top-item-2{
    background: url("@/assets/newImages/SpecialAssignments/overview2.svg") no-repeat;
            background-size: cover;
            background-position: center;
}   
.top-item-3{
    background: url("@/assets/newImages/SpecialAssignments/overview3.svg") no-repeat;
            background-size: cover;
            background-position: center;
}
.top-item-4{
    background: url("@/assets/newImages/SpecialAssignments/overview4.svg") no-repeat;
            background-size: cover;
            background-position: center;
}   
}
.bottom{
    width: 416px;
    height: 374px;
    // background-color: #1A9FFF;
   .list-header{
    width: 416px;
height: 40px;
background: linear-gradient(90deg, 
        rgba(26, 159, 255, 0) 0%, 
        #1A9FFF4D, 
        rgba(26, 159, 255, 0) 100%
    );
    display: flex;
    align-items: center;

    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 40px;
color: #FFFFFF;
   }
   .card-container{
    width: 416px;
    height: 334px;
    
   }
   .img-no {
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 334px; /* 150% */
        }
   .list-content{
    width: 416px;
    height: 334px;
    
    
    .list-item{
        width: 416px;
height: 40.8px;
display: flex;
align-items: center;
border-bottom: 1px solid #1A9FFF73;
font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: #FFFFFF;

    }
   }
   .td1{
    width: 83.2px;
text-align: center;
   }
   .td2{
    width: 67.2px;
    padding-left: 16px;
text-align: left;
   }
   .td3{
    width: 67.2px;

    text-align: left;
    padding-left: 16px;
   }
   .td4{
    width: 67.2px;

    text-align: left;
    padding-left: 16px;
   }
   .td5{
    width: 67.2px;

    text-align: left;
    padding-left: 16px;
   }
   .td-index{
    width: 24px;
height: 24px;
background: url("@/assets/newImages/td-index.svg") no-repeat;
            background-size: cover;
            background-position: center;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
margin:auto;
   }
}
}
</style>
