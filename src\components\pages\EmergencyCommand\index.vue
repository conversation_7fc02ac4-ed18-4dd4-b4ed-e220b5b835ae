<template>
    <div class="safety-supervision">
        <smart-energy-left v-if="!showEvent"></smart-energy-left>
        <event-left v-if="showEvent"></event-left>
        <smart-energy-right v-if="!showEvent"></smart-energy-right>
        <event-right v-else></event-right>
        <smart-energy-navigation></smart-energy-navigation>
        <div class="return-btn" v-if="showEvent" @click="returnBtn"></div>
    </div>
</template>
<script setup>
import {
    onMounted,
    reactive,
    ref,
    getCurrentInstance,
    onBeforeUnmount,
    onUnmounted,
    nextTick,
} from "vue";
const { proxy } = getCurrentInstance();
import SmartEnergyLeft from "./SmartEnergyLeft.vue";
import SmartEnergyRight from "./SmartEnergyRight.vue";
import eventLeft from "./eventLeft.vue";
import eventRight from "./eventRight.vue";

import SmartEnergyNavigation from "./SmartEnergyNavigation.vue";
const showEvent = ref(false);
const returnBtn = () => {
    showEvent.value = false;
    proxy.$bus.emit("initStart");
};
proxy.$bus.on("showEvent", (val) => {
    showEvent.value = val;
});
</script>

<style lang="less" scoped>
.return-btn {
    z-index: 1001;
    width: 40px;
    height: 40px;
    top: 96px;
    left: 496px;
    position: absolute;
    background: url("../../../assets/images/btns/return-btn.svg") no-repeat;
    background-size: cover;
    background-position: center;
}
</style>
