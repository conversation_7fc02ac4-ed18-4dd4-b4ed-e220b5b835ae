<template>
    <div class="secure-base">
        <double-prevention-left  @openDialog="openDialog"></double-prevention-left>
        <double-prevention-right  @openDialog="openDialog"></double-prevention-right>
        
        <!-- <test></test> -->
        <!-- 对话框组件 -->
        <template v-for="(value, key) in dialogStates" :key="key">
            <component
                :is="getDialogComponent(key)"
                v-if="dialogStates[key]"
                :currentValue="currentValue"
                :currentState="currentState"
                @closeDialog="closeDialog"
                :ref="key + 'Ref'"
            ></component>
        </template>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import DoublePreventionLeft from './DoublePreventionLeft.vue';
import DoublePreventionRight from './DoublePreventionRight.vue'
// 风险管控清单
import riskControlCheckDialog from './Dialog/riskControlCheckDialog/index.vue';
// 风险分析对象
import riskObjectDialog from './Dialog/riskObjectDialog/index.vue';
// 风险分析单元
import riskAnalysisUnitDialog from './Dialog/riskAnalysisUnitDialog/index.vue';
// 风险事件
import riskEventInfoDialog from './Dialog/riskEventInfoDialog/index.vue';
// 风险管控措施
import riskControlMeasureDialog from './Dialog/riskControlMeasureDialog/index.vue';
//隐患排查任务
import taskDistributionDialog from './Dialog/taskDistributionDialog/index.vue';
//隐患排查治理
import dangerReportDialog from './Dialog/dangerReportDialog/index.vue';
//线上督办记录
import dangerRemindRecordDialog from './Dialog/dangerRemindRecordDialog/index.vue';
//短信发送记录
import smsSendInfoDialog from './Dialog/smsSendInfoDialog/index.vue';
//线上线下检查记录
import offlineCheckDialog from './Dialog/offlineCheckDialog/index.vue';
//临期/超期隐患
import dangerReportAdventDialog from './Dialog/dangerReportAdventDialog/index.vue';
// 对话框显示状态管理
const dialogStates = reactive({
    riskControlCheckDialogShow: false,         // 风险管控清单
    riskObjectDialogShow: false,         // 风险分析对象
    riskAnalysisUnitDialogShow: false,         // 风险分析单元
    riskEventInfoDialogShow: false,         // 风险事件
    riskControlMeasureDialogShow: false,         // 风险管控措施
    taskDistributionDialogShow: false,         // 隐患排查任务
    dangerReportDialogShow: false,         // 隐患排查治理
    dangerReportAdventDialogShow: false,         // 临期/超期隐患
    dangerRemindRecordDialogShow: false,         // 线上督办记录
    smsSendInfoDialogShow: false,         // 短信发送记录
    offlineCheckDialogShow: false,         // 线上线下检查记录
});

// 对话框组件映射
const dialogComponents = {
    riskControlCheckDialogShow: riskControlCheckDialog,
    riskObjectDialogShow: riskObjectDialog,
    riskAnalysisUnitDialogShow: riskAnalysisUnitDialog,
    riskEventInfoDialogShow: riskEventInfoDialog,
    riskControlMeasureDialogShow: riskControlMeasureDialog,
    taskDistributionDialogShow: taskDistributionDialog,
    dangerReportDialogShow: dangerReportDialog,
    dangerReportAdventDialogShow: dangerReportAdventDialog,
    dangerRemindRecordDialogShow: dangerRemindRecordDialog,
    smsSendInfoDialogShow: smsSendInfoDialog,
    offlineCheckDialogShow: offlineCheckDialog,

};

// 根据对话框名称获取对应的组件
const getDialogComponent = (dialogName) => {
    return dialogComponents[dialogName];
};
// 弹窗传参
let currentValue = ref(null);
// 弹窗传参2
let currentState = ref(null);
onMounted(() => {
});
// 打开对话框
const openDialog = (dialogName,btnSelect,nowState) => {
    console.log(dialogName, '打开弹窗');
    currentValue.value = btnSelect;
    currentState.value = nowState;
    console.log(currentValue.value, 'currentValue主页');
    
    // 检查对话框名称是否有效
    if (dialogName in dialogStates) {
        dialogStates[dialogName] = true;
    } else {
        console.warn(`未知的对话框名称: ${dialogName}`);
    }
}

// 关闭对话框

const closeDialog = (dialogName) => {
    console.log(dialogName, '关闭弹窗');
    // 检查对话框名称是否有效
    if (dialogName in dialogStates) {
        dialogStates[dialogName] = false;
    } else {
        console.warn(`未知的对话框名称: ${dialogName}`);
    }
}
</script>

<style lang="less" scoped></style>
