<template>
  <Card title="安全包保履职完成率">
    <template v-slot>
  <div class="hidden-danger">
    <div class="show">
      <div class="person-in-charge">
        <div class="title_primary">{{ weekNum }}</div>
        <div class="title_technology">{{ yearNum }}</div>
        <div class="title_controls">{{ monthNum }}</div>
      </div>
    </div>
  </div>
  </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import {
  check_plan_rule,
  check_plan_rule_month,
  check_plan_rule_week,
  check_plan_rule_year, safety_v2_getCount,
} from "@/assets/js/api/safetySupervision";
import {ref, reactive, onMounted, onBeforeUnmount, computed} from "vue";

const searchPlan = () => {
  safety_v2_getCount().then(res=>{
    weekNum.value = res.data.data['主要负责人']!=undefined?res.data.data['主要负责人']:'0%'
    yearNum.value = res.data.data['技术负责人']!=undefined?res.data.data['技术负责人']:'0%'
    monthNum.value = res.data.data['操作负责人']!=undefined?res.data.data['操作负责人']:'0%'
  })
};
const weekNum = ref('0%');
const yearNum = ref('0%');
const monthNum = ref('0%');
onMounted(() => {
  searchPlan();
});
</script>

<style lang="less" scoped>
.hidden-danger {
  width: 416px;
    height: 224px;
    padding: 16px;

  .show {
    width: 416px;
    height: 212px;
    // margin-top: 12px;
    display: flex;
    gap: 12px;

  }
}
.person-in-charge {
  width: 400px;
  height: 216px;
  background: url("../../../../assets/images/updateNew/person-in-charge.svg") no-repeat;
  //background: url("../../../../assets/images/header-bg.svg") no-repeat;
  background-size: cover;
  background-position: center;
  margin-top: 1px;
  z-index: 999;
}
.title_primary{
  font-family: Noto Sans SC;
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  position: absolute;
  top: 141px;
  left: 68px;
}
.title_technology{
  font-family: Noto Sans SC;
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  position: absolute;
  top: 141px;
  left: 204px;
}
.title_controls{
  font-family: Noto Sans SC;
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  position: absolute;
  top: 141px;
  left: 340px;
}
</style>
