<template>
    <div class="secure-base">
        <special-assignments-left @openDialog="openDialog"></special-assignments-left>
        <special-assignments-right @openDialog="openDialog"></special-assignments-right>
        <special-assignments-nav></special-assignments-nav>
    <!-- 对话框组件 -->
        <template v-for="(value, key) in dialogStates" :key="key">
            <component
                :is="getDialogComponent(key)"
                v-if="dialogStates[key]"
                :currentValue="currentValue"
                :currentState="currentState"
                @closeDialog="closeDialog"
                :ref="key + 'Ref'"
            ></component>
        </template>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import SpecialAssignmentsLeft from './SpecialAssignmentsLeft.vue'
import SpecialAssignmentsRight from './SpecialAssignmentsRight.vue';
import SpecialAssignmentsNav from './SpecialAssignmentsNav.vue'
//特殊作业
import specialWorkDialog from './Dialog/specialWorkDialog/index.vue';
//特殊作业抽查
import specialWorkCheckDialog from './Dialog/specialWorkCheckDialog/index.vue';
// 对话框显示状态管理
const dialogStates = reactive({
    specialWorkDialogShow: false,         // 特殊作业
    specialWorkCheckDialogShow: false,         // 特殊作业抽查
});

// 对话框组件映射
const dialogComponents = {
    specialWorkDialogShow: specialWorkDialog,
    specialWorkCheckDialogShow: specialWorkCheckDialog,

};

// 根据对话框名称获取对应的组件
const getDialogComponent = (dialogName) => {
    return dialogComponents[dialogName];
};
// 弹窗传参
let currentValue = ref(null);
// 弹窗传参2
let currentState = ref(null);
onMounted(() => {
});
// 打开对话框
const openDialog = (dialogName,btnSelect,nowState) => {
    console.log(dialogName, '打开弹窗');
    currentValue.value = btnSelect;
    currentState.value = nowState;
    console.log(currentValue.value,currentState.value, 'currentValue主页');
    
    // 检查对话框名称是否有效
    if (dialogName in dialogStates) {
        dialogStates[dialogName] = true;
    } else {
        console.warn(`未知的对话框名称: ${dialogName}`);
    }
}

// 关闭对话框

const closeDialog = (dialogName) => {
    console.log(dialogName, '关闭弹窗');
    // 检查对话框名称是否有效
    if (dialogName in dialogStates) {
        dialogStates[dialogName] = false;
    } else {
        console.warn(`未知的对话框名称: ${dialogName}`);
    }
}
</script>

<style lang="less" scoped></style>
