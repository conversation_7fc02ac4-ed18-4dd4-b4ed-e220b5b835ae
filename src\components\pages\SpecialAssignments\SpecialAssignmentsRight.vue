<template>
    <div class="secure-base-right">
       <right-top></right-top>
       <right-center></right-center>
    <right-bottom @openDialog="openDialog"></right-bottom>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,

} from "vue";
import Card from "../../commenNew/Card.vue";
import WrapSlot from "../../commenNew/WrapSlot.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightBottom from "./subgroup/RightBottom.vue";
import RightCenter from "./subgroup/RightCenter.vue";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

const openDialog = (val,btnSelect,nowState) => {
    console.log(val,btnSelect,nowState, '打开弹窗');
    emit('openDialog',val,btnSelect,nowState);
}
</script>

<style lang="less" scoped>
.secure-base-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
