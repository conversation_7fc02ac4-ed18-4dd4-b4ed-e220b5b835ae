<template>
    <Card title="重点卡口监控">
        <template v-slot:title>
            <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
        </template>
        <template v-slot>
    <div class="checkpoint-monitor">
        <div class="video">
            <div ref="img" id="imgCCC" class="img"></div>
        </div>
    </div>
</template>
</Card>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
import { getFlv } from "../../../../assets/js/api/safetySupervision";
import Card from "@/components/commenNew/Card.vue";
import selectVue from "@/components/commenNew/selectVue.vue";
import {
    gate_video,
    nj_iocGateC,
} from "../../../../assets/js/api/comprehensiveSupervision";
const { proxy } = getCurrentInstance();
var jessibuca = null;
const img = ref(null);
const active = ref(0);
const list = ref([]);
//按钮选项
const btnOptions = ref([
])
//按钮
let chooseValue = ref('');
const selectBtn = (val) => {
    console.log(val,'vallllllllll');
    // getData(chooseValue.value);
    destroy();
    getFlv({
        equipmentIdList: [val.value],
    }).then((res3) => {
        // hazardDialog.value = true
        // myDiv.style.display = "block";
        console.log(res3.data);
        if (
            res3.data[0].flvAddress &&
            res3.data[0].flvAddress.indexOf(".live.flv") != -1
        ) {
            jessibuca = new JessibucaPro({
                container: img.value,
                videoBuffer: 0.2, // 缓存时长
                isResize: false,
                decoder: "/Jessibuca/decoder-pro.js",
                text: "",
                loadingText: "加载中",
                showBandwidth: false, // 显示网速
                operateBtns: {
                    fullscreen: false,
                    screenshot: false,
                    play: false,
                    audio: false,
                    performance: false,
                },
                forceNoOffscreen: true,
                isNotMute: false,
                heartTimeout: 10,
                ptzClickType: "mouseDownAndUp",
            });
            console.log(res3.data[0].flvAddress);
            if (res3.data[0].flvAddress)
                jessibuca.play(proxy.$changeUrl(res3.data[0].flvAddress));
        }
    });
};
const setButton = (val, type) => {
    active.value = type;
    console.log(val);
    
};
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
onMounted(() => {
    nj_iocGateC({ "": "" }).then((re) => {
        if (re.data.success && re.data.data && re.data.data.length) {
            if (re.data.data.length > 4) {
                list.value = re.data.data.slice(0, 4);
                
            } else {
                list.value = re.data.data;
            }
            btnOptions.value = list.value.map(item=>{
                return{
                    label: item.areaName,
                    value: item.cameraId
                }
            })
            console.log(btnOptions.value,'btnOptions');
            
            chooseValue.value =btnOptions.value[0].label;
            console.log(chooseValue.value);
            
            getFlv({
                equipmentIdList: [list.value[0].cameraId],
            }).then((res3) => {
                if (
                    res3.data[0].flvAddress &&
                    res3.data[0].flvAddress.indexOf(".live.flv") != -1
                ) {
                    // hazardDialog.value = true
                    // myDiv.style.display = "block";
                    jessibuca = new JessibucaPro({
                        container: img.value,
                        videoBuffer: 0.2, // 缓存时长
                        isResize: false,
                        decoder: "/Jessibuca/decoder-pro.js",
                        text: "",
                        loadingText: "加载中",
                        showBandwidth: true, // 显示网速
                        operateBtns: {
                            fullscreen: false,
                            screenshot: false,
                            play: false,
                            audio: false,
                            performance: false,
                        },
                        forceNoOffscreen: true,
                        isNotMute: false,
                        heartTimeout: 10,
                        ptzClickType: "mouseDownAndUp",
                    });
                    console.log(res3.data[0].flvAddress);
                    if (res3.data[0].flvAddress)
                        jessibuca.play(
                            proxy.$changeUrl(res3.data[0].flvAddress),
                        );
                }
            });
        }
    });
});
onBeforeUnmount(() => {
    destroy();
});
</script>

<style lang="less" scoped>
.checkpoint-monitor {
    width: 416px;
    height: 224px;
    padding: 16px;
    .video {
        width: 416px;
    height: 224px;
        background-color: rgba(48, 171, 232, 0.15);
        .img {
            width: 416px;
    height: 224px;
        }
    }
}
</style>
