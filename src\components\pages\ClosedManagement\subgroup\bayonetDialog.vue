<template>
<div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">卡口详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    
                   <div class="container">
                        <div class="details">
                            <el-row :gutter="10">
                                <el-col :span="12" class="innner special">
                                    <div class="headline">卡口名称：</div>
                                    <div class="content" :title="dialogData.equipment_name">
                                        {{ dialogData.equipment_name }}
                                    </div>
                                </el-col>
                                <el-col :span="12" class="innner special">
                                    <div class="headline">卡口编号：</div>
                                    <div class="content" :title="dialogData.equipment_id">
                                        {{ dialogData.equipment_id }}
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12" class="innner">
                                    <div class="headline">设备状态：</div>
                                    <div class="content">
                                        {{
                                            dialogData.equipment_status == 1
                                                ? "在线"
                                                : "离线"
                                        }}
                                    </div>
                                </el-col>
                                <el-col :span="12" class="innner">
                                    <div class="headline">设备编号：</div>
                                    <div class="content" :title="dialogData.cameraId">
                                        {{ dialogData.cameraId }}
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12" class="innner">
                                    <div class="headline">设备位置：</div>
                                    <div class="content">
                                        {{ dialogData.longitude }}，{{
                                            dialogData.latitude
                                        }}
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="top-selects">
                            <div class="slect-tabs" ref="tabsRef">
                                <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="selectType(item.value)">
                                    <div class="tab-item-text" :class="{'active-tab-item':currentTab === item.value}" >{{ item.label }}</div>
                                    <div class="tab-item-line" v-if="currentTab === item.value"></div>
                                </div>
                            </div>
                        </div>
                       
                        <!-- <div class="separate"></div> -->
                        <div class="bottom">
                            <div class="video" v-if="currentTab == 1">
                                <div ref="img" id="imgCCC" class="img">
                                    <div v-if="!videoGrey" class="img-no">暂无数据</div>
                                </div>
                            </div>
                            <div class="in" v-if="currentTab === 2">
                                <el-input
                                    v-model="queryParamsIn.carPlate"
                                    placeholder="请输入车牌号"
                                    clearable
                                    @change="changeIn"
                                />
                                <el-table
                                    class="tablebox"
                                    :data="tableData2"
                                    style="width: 100%"
                                >
                                    <el-table-column
                                        prop="num"
                                        label="序号"
                                        style="width: 5%"
                                    />
                                    <el-table-column
                                        prop="carPlate"
                                        label="车牌号"
                                        show-overflow-tooltip
                                    />
                                    <el-table-column
                                        prop="carType"
                                        label="车辆类型"
                                        show-overflow-tooltip
                                    />
                                    <el-table-column
                                        prop="snapTime"
                                        label="时间"
                                        show-overflow-tooltip
                                    />
                                    <el-table-column
                                        prop="insource"
                                        label="来源"
                                        show-overflow-tooltip
                                    />
                                </el-table>
                                <el-pagination
                                    class="pagination"
                                    background
                                    layout="->,total, prev, pager, next"
                                    :total="totalSecond"
                                    v-model:currentPage="pageNumSecond"
                                    :page-size="pageSizeSecond"
                                    @current-change="handleCurrentChangeSecond2"
                                />
                            </div>
                            <div class="out" v-if="currentTab === 3">
                                <el-input
                                    v-model="queryParamsOut.carPlate"
                                    placeholder="请输入车牌号"
                                    clearable
                                    @change="changeOut"
                                />
                                <el-table
                                    class="tablebox"
                                    :data="tableData"
                                    style="width: 100%"
                                >
                                    <el-table-column
                                        prop="num"
                                        label="序号"
                                        style="width: 5%"
                                    />
                                    <el-table-column
                                        prop="carPlate"
                                        label="车牌号"
                                        show-overflow-tooltip
                                    />
                                    <el-table-column
                                        prop="carType"
                                        label="车辆类型"
                                        show-overflow-tooltip
                                    />
                                    <el-table-column
                                        prop="snapTime"
                                        label="时间"
                                        show-overflow-tooltip
                                    />
                                    <el-table-column
                                        prop="insource"
                                        label="来源"
                                        show-overflow-tooltip
                                    />
                                </el-table>
                                <el-pagination
                                    class="pagination"
                                    background
                                    layout="->,total, prev, pager, next"
                                    :total="total"
                                    v-model:currentPage="pageNum"
                                    :page-size="pageSize"
                                    @current-change="handleCurrentChange"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>

</template>

<script setup>
import { get } from "@vueuse/shared";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
import {
    security_pass_records,
    nj_equipment_entrance_guard_info,
    getPassRecord,
} from "../../../../assets/js/api/closedManagement";
import { getFlv } from "../../../../assets/js/api/safetySupervision";
const emit = defineEmits(["closeDialog"]);
const { proxy } = getCurrentInstance();
const clickId = ref();
const dialogData = ref({
    equipment_name: "",
    equipment_id: "",
    equipment_status: "",
    longitude: "",
    latitude: "",
    cameraId: "",
});
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
    equipmentId: {
        type: [Number, String],
    },
    positionData: {
        type: [Array],
    },
});
const { pickId, equipmentId } = toRefs(props);
const { positionData } = toRefs(props);
const videoGrey = ref(false);

watch(
    pickId,
    (a, b) => {
        console.log(a, b);

        clickId.value = a;
        // if (a != undefined) {
        //   security_pass_records({ device_id: a, pass_type: 1 }).then((res) => {});
        //   security_pass_records({ device_id: a, pass_type: 0 }).then((res) => {});
        // }
    },
    {
        immediate: true,
    },
);
const formatDate = (date) => {
    let year = date.getFullYear();
    let month = `0${date.getMonth() + 1}`.slice(-2);
    let day = `0${date.getDate()}`.slice(-2);

    return `${year}-${month}-${day} 00:00:00`;
};
const today = ref(null);
onMounted(() => {
    var a;
    a = new Date();
    today.value = formatDate(a);
    guardDetails();
});
const queryParamsIn = ref({
    carPlate: "",
});
const queryParamsOut = ref({
    carPlate: "",
});

//获取卡口详情
const guardDetails = () => {
    nj_equipment_entrance_guard_info({ id: clickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            dialogData.value = res.data.data[0];
            console.log(
                positionData.value.find((i) => i.name == clickId.value)
                    .cameraId,
            );
            if (
                positionData.value.find((i) => i.name == clickId.value) !=
                undefined
            ) {
                getVideo();
            }
        }
    });
};
//获取视频
var jessibuca = null;
const img = ref(null);
const getVideo = () => {
    if (
        positionData.value.find((i) => i.name == clickId.value).cameraId ==
            null ||
        positionData.value.find((i) => i.name == clickId.value).cameraId ==
            "离线" ||
        positionData.value.find((i) => i.name == clickId.value).cameraId ==
            undefined
    ) {
        videoGrey.value = true;
    }
    destroy();
    getFlv({
        equipmentIdList: [
            positionData.value.find((i) => i.name == clickId.value).cameraId,
        ],
    }).then((res3) => {
        // hazardDialog.value = true
        // myDiv.style.display = "block";
        jessibuca = new JessibucaPro({
            container: img.value,
            videoBuffer: 0.2, // 缓存时长
            isResize: false,
            decoder: "/Jessibuca/decoder-pro.js",
            text: "",
            loadingText: "加载中",
            showBandwidth: true, // 显示网速
            operateBtns: {
                fullscreen: false,
                screenshot: false,
                play: false,
                audio: false,
                performance: false,
            },
            forceNoOffscreen: true,
            isNotMute: false,
            heartTimeout: 10,
            ptzClickType: "mouseDownAndUp",
        });
        console.log(res3.data[0].flvAddress);
        if (res3.data[0].flvAddress)
            jessibuca.play(proxy.$changeUrl(res3.data[0].flvAddress));
    });
};
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
const typeActive = ref(1);
//顶部tab
let topTabs = ref([
    {
        value:1,
        label:'实时视频'
    },
    {
        value:2,
        label:'今日入园'
    },
    {
        value:3,
        label:'今日出园'
    }
]);
let currentTab = ref(1);
const tabsRef = ref(null);
const selectType = (type) => {
        currentTab.value = type;
    if (type == 2) {
        pageNumSecond.value = 1;
        getIn();
    } else if (type == 3) {
        pageNum.value = 1;
        getOut();
    } else if (type == 1) {
        if (dialogData.value.cameraId != null) {
            getVideo();
        }
    }
};
const changeIn = () => {
    pageNumSecond.value = 1;
    getIn();
};
const changeOut = () => {
    pageNum.value = 1;
    getOut();
};
const getOut = () => {
    tableData.value = [];
    getPassRecord({
        ...queryParamsOut.value,
        device_id: clickId.value,
        pass_type: 0,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        begin: today.value,
    }).then((res) => {
        if (
            res.data.data &&
            res.data.data.records &&
            res.data.data.records.length
        ) {
            tableData.value = res.data.data.records;
            let middleA = (pageNum.value - 1) * pageSize.value;
            tableData.value.forEach((item, index) => {
                Reflect.set(item, "num", middleA + index + 1);
            });
            console.log(tableData.value);
            total.value = Number(res.data.data.total);
            console.log(total.value);
        }
    });
};
const getIn = () => {
    tableData2.value = [];
    getPassRecord({
        ...queryParamsIn.value,
        device_id: clickId.value,
        pass_type: 1,
        pageNum: pageNumSecond.value,
        pageSize: pageSizeSecond.value,
        begin: today.value,
    }).then((res) => {
        if (
            res.data.data &&
            res.data.data.records &&
            res.data.data.records.length
        ) {
            tableData2.value = res.data.data.records;
            let middleA = (pageNumSecond.value - 1) * pageSizeSecond.value;
            tableData2.value.forEach((item, index) => {
                Reflect.set(item, "num", middleA + index + 1);
            });
            totalSecond.value = Number(res.data.data.total);
        }
    });
    
};
const tableData = ref([]);
const tableData2 = ref([]);
const totalData = ref([]);

const totalSecond = ref(0);
const pageNumSecond = ref(1);
const pageSizeSecond = ref(10);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const handleCurrentChangeSecond2 = (e) => {
    pageNumSecond.value = e;
    getIn();
};
const handleCurrentChange = (e) => {
    pageNum.value = e;
    getOut();
};
const closeDialog = () => {
    console.log("bayonet");
    destroy();
    emit("closeDialog", "bayonet");
};
</script>
<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 805px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 805px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 709px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.bayonet-dialog {
    width: 840px;
    height: 805px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}
.header {
    width: 840px;
    height: 52px;
    flex-shrink: 0;
    background: url("../../../../assets/images/dialog/header52.svg") no-repeat
        center center;
    background-size: cover;
    background-position: center;
    display: flex;
    .title {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 52px; /* 150% */
        margin-left: 32px;
    }
    .close {
        width: 24px;
        height: 24px;
        background: url("../../../../assets/images/dialog/close.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: auto 0;
        margin-left: 698px;
    }
}
.container {
    // width: 792px;
    // height: 721px;
    // padding: 16px 24px;

    .details {
        // margin-top: 12px;
        width: 792px;
        height: 97px;
        // background-color: #47ebeb;
        .innner {
            display: flex;
            margin-top: 8px;

            .headline {
                width: 140px;
                color: #47ebeb;
                text-align: right;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }
            .content {
                width: 251px;
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
        .special {
            margin-top: 0px;
        }
    }
    .separate {
        width: 792px;
        height: 2px;
        background: url("../../../../assets/images/dialog/separate.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
    .top-selects{
            width: 8000px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 800px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
    .bottom {
        width: 792px;
        height: 538px;
        // background-color: #47ebeb;
                margin-top: 16px;

        .video {
            width: 792px;
            height: 488px;
            // background-color: #fff;
            background-color: rgba(48, 171, 232, 0.15);
            .img {
                width: 792px;
                height: 444px;
                // margin-top: 16px;
                background: rgba(48, 171, 232, 0.15);
                display: flex;
                justify-content: center;
                .img-no {
                    margin: auto 0;
                    color: #fff;
                    text-align: center;
                    font-family: "Noto Sans SC";
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 28px; /* 140% */
                }
            }
        }
        .tablebox {
            margin-top: 8px;
        }
        .el-input {
            width: 122px;
            height: 40px;
        }
        .el-select {
            width: 122px;
        }
        .el-date-editor {
            width: 210px;
        }
        :deep(.el-input__wrapper) {
            background-color: transparent;
            box-shadow: 0 0 0 0;
            border-radius: 4px;
            border: 1px solid #30abe8;
            height: 22px;
            padding: 7px 12px;
        }
        :deep(.el-input__inner) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-input__inner::placeholder) {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
    }
    .operateButton{
        color:rgba(128, 234, 255, 1)
    }
    
}
</style>
