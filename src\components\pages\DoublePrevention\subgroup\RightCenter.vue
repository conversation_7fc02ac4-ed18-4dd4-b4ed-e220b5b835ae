<template>
    <Card title="隐患治理情况">
        <template v-slot:title>
            <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
        </template>
        <template v-slot>
            <div class="hidden-danger-treatment-wrapper">
                <div class="top">
                    <div class="top-left">
                        <div class="top-left-num">{{percent}}%</div>
                        <div class="top-left-text">隐患整改完成率</div>
                    </div>
                    <div class="top-right">
                        <div class="top-right-item" style="cursor: pointer;" @click="openDialog('dangerReportDialogShow','整改中')">
                            <div class="top-right-item-text">整改中</div>
                            <div class="top-right-item-num">{{ dataObj.reformingCount?dataObj.reformingCount:0 }}<span class="top-right-item-num-unit">个</span></div>
                        </div>
                        <div class="top-right-item" style="cursor: pointer;" @click="openDialog('dangerReportDialogShow','待验收')">
                            <div class="top-right-item-text">待验收</div>
                            <div class="top-right-item-num">{{ dataObj.pendingAcceptanceCount?dataObj.pendingAcceptanceCount:0 }}<span class="top-right-item-num-unit">个</span></div>
                        </div>
                        <div class="top-right-item" style="cursor: pointer;" @click="openDialog('dangerReportDialogShow','已验收')">
                            <div class="top-right-item-text">已验收</div>
                            <div class="top-right-item-num">{{ dataObj.acceptedCount?dataObj.acceptedCount:0 }}    <span class="top-right-item-num-unit">个</span></div>
                        </div>
                    </div>
                </div>
                <div class="bottom">
                    <div class="bottom-item" style="cursor: pointer;" @click="openDialog('dangerReportAdventDialogShow')">
                        <div class="bottom-item-text">临期/超期整改</div>
                        <div class="bottom-item-num">{{dataObj.abnormalRectificationCount?dataObj.abnormalRectificationCount:0}}</div>
                    </div>
                    <div class="bottom-item" style="cursor: pointer;" @click="openDialog('dangerRemindRecordDialogShow')">
                        <div class="bottom-item-text">线上督办</div>
                        <div class="bottom-item-num">{{ dataObj.onlineRemindCount?dataObj.onlineRemindCount:0 }}</div>
                    </div>
                    <div class="bottom-item" style="cursor: pointer;" @click="openDialog('smsSendInfoDialogShow')">
                        <div class="bottom-item-text">短信提醒</div>
                        <div class="bottom-item-num">{{ dataObj.smsRemindCount?dataObj.smsRemindCount:0 }}</div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue"
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance } from "vue";
import {dangerReformStatistics} from '@/assets/js/api/doublePrevention.js';  
import selectVue from "@/components/commenNew/selectVue.vue";
const { proxy } = getCurrentInstance();

const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val,val2) => {
    proxy.$emit('openDialog',val,nowChoose.value,val2);
}
//按钮选项
const btnOptions = ref([
    {
        label:'今日',
        value:1
    },
    {
        label:'本周',
        value:2
    },
    {
        label:'本月',
        value:3
    },
    {
        label:'本年',
        value:4
    }
]);
let percent = ref(0);
let dataObj=ref({
            completedTaskCount: 0,
            incompleteTaskCount: 0,
            totalTaskCount: 0,
            completionRate: 0,
            generalRiskCount:0,
            majorRiskCount:0
        })
//按钮
let chooseValue = ref('本年');
//获取日期时间

// 获取本周第一天（周一）
const getFirstDayOfWeek = (date) => {
    const day = date.getDay() || 7;
    const diff = date.getDate() - day + 1;
    return new Date(date.setDate(diff));
};

// 定义时间变量
const currentDate = ref(new Date());

// 格式化日期时间函数
const formatDateTime = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 今天23:59:59
const todayEnd = computed(() => {
    const date = new Date(currentDate.value);
    date.setHours(23, 59, 59, 999);
    return formatDateTime(date);
});

// 今天00:00:00
const todayStart = computed(() => {
    const date = new Date(currentDate.value);
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});

// 本周第一天00:00:00
const weekStart = computed(() => {
    const date = getFirstDayOfWeek(new Date(currentDate.value));
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});

// 本月第一天00:00:00
const monthStart = computed(() => {
    const date = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1);
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});

// 本年第一天00:00:00
const yearStart = computed(() => {
    const date = new Date(currentDate.value.getFullYear(), 0, 1);
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});
//当前选择
let nowChoose = ref(4);
const selectBtn = (val) => {
    console.log(val,'vallllllllll');
    nowChoose.value=val.value;
    if(val.value==1){
        beginTime.value=todayStart.value;
        endTime.value=todayEnd.value;
    }else if(val.value==2){
        beginTime.value=weekStart.value;
        endTime.value=todayEnd.value;
    }else if(val.value==3){
        beginTime.value=monthStart.value;
        endTime.value=todayEnd.value;
    }else if(val.value==4){
        beginTime.value=yearStart.value;
        endTime.value=todayEnd.value;
    }
    getData();
};
let beginTime = ref('');
let endTime = ref('');
const getData = () => {
    dangerReformStatistics({
        beginTime:beginTime.value,
        endTime:endTime.value
    }).then(res=>{
        console.log(res,'res');
        if(res.data&&res.data.data&&res.data.data.length>0){
            dataObj.value=res.data.data[0];
            console.log(dataObj.value,'dataObj.value右上');
            percent.value = Number(res.data.data[0].completionRate).toFixed(0);
        }
    })
}
onMounted(()=>{
    beginTime.value=yearStart.value;
        endTime.value=todayEnd.value;
    getData();
})
</script>

<style lang="less" scoped>

    .hidden-danger-treatment-wrapper{
        width: 416px;
    height: 224px;
    padding: 16px;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    .top{
        width: 416px;
    height: 152px;
    display: flex;
    gap: 48px;
    .top-left{
        width: 120px;
height: 152px;
display: flex;
flex-direction: column;
gap: 8px;
.top-left-num{
    width: 120px;
height: 120px;
font-family: DIN Alternate;
font-weight: 700;
font-size: 28px;
line-height: 36px;
text-align: center;
color: rgba(255, 255, 255, 1);
display: flex;
justify-content: center;
align-items: center;
background: url("@/assets/newImages/DoublePrevention/hidden-danger-treatment-left.svg") no-repeat;
            background-size: cover;
            background-position: center;

}

.top-left-text{
    font-family: Noto Sans SC;
font-weight: 700;
font-size: 16px;
line-height: 24px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);

}
    }
    .top-right{
        width: 216px;
height: 132px;
display: flex;
flex-direction: column;
justify-content: space-between;
.top-right-item{
    width: 192px;
    padding: 0 12px;
height: 36px;
display: flex;
justify-content: space-between;
background: url("@/assets/newImages/DoublePrevention/hidden-danger-treatment-right.svg") no-repeat;
            background-size: cover;
            background-position: center;

.top-right-item-text{
font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
text-align: left;
color: rgba(255, 255, 255, 1);

}
.top-right-item-num{
    font-family: Noto Sans SC;
font-weight: 700;
font-size: 20px;
line-height: 28px;
text-align: right;
color: rgba(255, 255, 255, 1);
.top-right-item-num-unit{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
color: rgba(255, 255, 255, 1);

}
}

}     

    }
    }
    .bottom{
        width: 416px;
    height: 56px;
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    .bottom-item{
        width: 128px;
        height: 56px;
        .bottom-item-text{
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
color: rgba(255, 255, 255, 1);
text-align: center;
        }
        .bottom-item-num{
            font-family: DIN Alternate;
font-weight: 700;
font-size: 24px;
line-height: 32px;
color: rgba(255, 255, 255, 1);
text-align: center;
        }
    }
    }
    }
</style>
