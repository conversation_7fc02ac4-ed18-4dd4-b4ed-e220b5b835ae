<svg width="48" height="62" viewBox="0 0 48 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M29 54.5L24 61.166L19 54.5H29Z" fill="url(#paint0_linear_93_78)" stroke="url(#paint1_linear_93_78)"/>
<mask id="path-2-inside-1_93_78" fill="white">
<path d="M0 24C0 10.7452 10.7452 0 24 0V0C37.2548 0 48 10.7452 48 24V24C48 37.2548 37.2548 48 24 48V48C10.7452 48 0 37.2548 0 24V24Z"/>
</mask>
<g clip-path="url(#paint2_angular_93_78_clip_path)" data-figma-skip-parse="true" mask="url(#path-2-inside-1_93_78)"><g transform="matrix(0 0.024 -0.024 0 24 24)"><foreignObject x="-1062.5" y="-1062.5" width="2125" height="2125"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(255, 217, 102, 0.5476) 0deg,rgba(255, 236, 179, 1) 90deg,rgba(255, 198, 26, 0) 90deg,rgba(255, 236, 179, 1) 270deg,rgba(255, 198, 26, 0.0953) 270deg,rgba(255, 217, 102, 0.5476) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M24 48V46.5C11.5736 46.5 1.5 36.4264 1.5 24H0H-1.5C-1.5 38.0833 9.91674 49.5 24 49.5V48ZM48 24H46.5C46.5 36.4264 36.4264 46.5 24 46.5V48V49.5C38.0833 49.5 49.5 38.0833 49.5 24H48ZM24 0V1.5C36.4264 1.5 46.5 11.5736 46.5 24H48H49.5C49.5 9.91674 38.0833 -1.5 24 -1.5V0ZM24 0V-1.5C9.91674 -1.5 -1.5 9.91674 -1.5 24H0H1.5C1.5 11.5736 11.5736 1.5 24 1.5V0Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.70196080207824707,&#34;a&#34;:1.0},&#34;position&#34;:0.250},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.10196078568696976,&#34;a&#34;:0.0},&#34;position&#34;:0.250},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.70196080207824707,&#34;a&#34;:1.0},&#34;position&#34;:0.750},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.10196078568696976,&#34;a&#34;:0.095288276672363281},&#34;position&#34;:0.750}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.70196080207824707,&#34;a&#34;:1.0},&#34;position&#34;:0.250},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.10196078568696976,&#34;a&#34;:0.0},&#34;position&#34;:0.250},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.70196080207824707,&#34;a&#34;:1.0},&#34;position&#34;:0.750},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.10196078568696976,&#34;a&#34;:0.095288276672363281},&#34;position&#34;:0.750}],&#34;transform&#34;:{&#34;m00&#34;:2.9391524462044029e-15,&#34;m01&#34;:-48.0,&#34;m02&#34;:48.0,&#34;m10&#34;:48.0,&#34;m11&#34;:2.9391524462044029e-15,&#34;m12&#34;:-2.9391524462044029e-15},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-2-inside-1_93_78)"/>
<g filter="url(#filter0_i_93_78)">
<rect x="4" y="4" width="40" height="40" rx="20" fill="url(#paint3_radial_93_78)"/>
</g>
<g clip-path="url(#clip0_93_78)">
<path d="M23.959 27C22.0494 27.0001 20.2028 27.6831 18.7529 28.9258L17.8496 29.7002C17.628 29.8902 17.5 30.168 17.5 30.46V30.9336C17.5 31.2848 17.6843 31.6103 17.9854 31.791L19.7627 32.8574C19.9181 32.9506 20.0961 33 20.2773 33H27.7227C27.9039 33 28.0819 32.9506 28.2373 32.8574L30.0146 31.791C30.3157 31.6103 30.5 31.2848 30.5 30.9336V30.46C30.5 30.168 30.372 29.8902 30.1504 29.7002L29.2471 28.9258C27.7972 27.6831 25.9506 27.0001 24.041 27H23.959ZM27.709 25.002C27.4129 25.7324 26.9072 26.3557 26.2666 26.7969C27.4758 27.125 28.6069 27.7185 29.5723 28.5459L30.4766 29.3213C30.8088 29.6063 31 30.0222 31 30.46V30.9336C31 30.9557 31 30.978 30.999 31H31.2227C31.4039 31 31.5819 30.9506 31.7373 30.8574L33.5146 29.791C33.8157 29.6103 34 29.2848 34 28.9336V28.46C34 28.168 33.872 27.8902 33.6504 27.7002L32.7471 26.9258C31.3398 25.7195 29.5586 25.0409 27.709 25.002ZM20.291 25.002C18.4414 25.0409 16.6602 25.7195 15.2529 26.9258L14.3496 27.7002C14.128 27.8902 14 28.168 14 28.46V28.9336C14 29.2848 14.1843 29.6103 14.4854 29.791L16.2627 30.8574C16.4181 30.9506 16.5961 31 16.7773 31H17.001C17 30.978 17 30.9557 17 30.9336V30.46C17 30.0222 17.1912 29.6063 17.5234 29.3213L18.4277 28.5459C19.3931 27.7185 20.5242 27.125 21.7334 26.7969C21.0928 26.3557 20.5871 25.7324 20.291 25.002ZM20.5361 23C20.5128 23.1633 20.5 23.3302 20.5 23.5C20.5 25.433 22.067 27 24 27C25.933 27 27.5 25.433 27.5 23.5C27.5 23.3302 27.4872 23.1633 27.4639 23H20.5361ZM29.25 21C29.4069 21.2089 29.5 21.4686 29.5 21.75C29.5 22.4404 28.9404 23 28.25 23H27.9688C27.9892 23.164 28 23.3309 28 23.5C28 24.028 27.8978 24.5326 27.7119 24.9941C29.5462 24.8846 31 23.3619 31 21.5C31 21.3303 30.9882 21.1633 30.9648 21H29.25ZM17.0352 21C17.0118 21.1633 17 21.3303 17 21.5C17 23.3619 18.4538 24.8846 20.2881 24.9941C20.1022 24.5326 20 24.028 20 23.5C20 23.3309 20.0108 23.164 20.0312 23H19.75C19.0596 23 18.5 22.4404 18.5 21.75C18.5 21.4686 18.5931 21.2089 18.75 21H17.0352ZM24 17C23.7239 17 23.5 17.2239 23.5 17.5V17.5352C21.8038 17.7778 20.5 19.2367 20.5 21H19.75C19.3358 21 19 21.3358 19 21.75C19 22.1642 19.3358 22.5 19.75 22.5H28.25C28.6642 22.5 29 22.1642 29 21.75C29 21.3358 28.6642 21 28.25 21H27.5C27.5 19.2367 26.1962 17.7778 24.5 17.5352V17.5C24.5 17.2239 24.2761 17 24 17ZM27.5 15C27.2239 15 27 15.2239 27 15.5V15.5352C26.0894 15.6654 25.2915 16.1467 24.748 16.8369C24.8186 16.9164 24.8774 17.0074 24.9199 17.1064C26.5276 17.4848 27.7609 18.8331 27.9688 20.5H31.75C32.1642 20.5 32.5 20.1642 32.5 19.75C32.5 19.3358 32.1642 19 31.75 19H31C31 17.2368 29.6961 15.7778 28 15.5352V15.5C28 15.2239 27.7761 15 27.5 15ZM20.5 15C20.2239 15 20 15.2239 20 15.5V15.5352C18.3039 15.7778 17 17.2368 17 19H16.25C15.8358 19 15.5 19.3358 15.5 19.75C15.5 20.1642 15.8358 20.5 16.25 20.5H20.0312C20.2391 18.8331 21.4724 17.4848 23.0801 17.1064C23.1226 17.0074 23.1814 16.9164 23.252 16.8369C22.7085 16.1467 21.9106 15.6654 21 15.5352V15.5C21 15.2239 20.7761 15 20.5 15Z" fill="white"/>
</g>
<rect x="20" y="50" width="8" height="2" fill="#FFECB3"/>
<defs>
<clipPath id="paint2_angular_93_78_clip_path"><path d="M24 48V46.5C11.5736 46.5 1.5 36.4264 1.5 24H0H-1.5C-1.5 38.0833 9.91674 49.5 24 49.5V48ZM48 24H46.5C46.5 36.4264 36.4264 46.5 24 46.5V48V49.5C38.0833 49.5 49.5 38.0833 49.5 24H48ZM24 0V1.5C36.4264 1.5 46.5 11.5736 46.5 24H48H49.5C49.5 9.91674 38.0833 -1.5 24 -1.5V0ZM24 0V-1.5C9.91674 -1.5 -1.5 9.91674 -1.5 24H0H1.5C1.5 11.5736 11.5736 1.5 24 1.5V0Z" mask="url(#path-2-inside-1_93_78)"/></clipPath><filter id="filter0_i_93_78" x="4" y="4" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect1_innerShadow_93_78"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.92549 0 0 0 0 0.701961 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_93_78"/>
</filter>
<linearGradient id="paint0_linear_93_78" x1="24" y1="54" x2="24" y2="62" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC61A" stop-opacity="0"/>
<stop offset="1" stop-color="#FFECB3"/>
</linearGradient>
<linearGradient id="paint1_linear_93_78" x1="24" y1="62" x2="24" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFECB3"/>
<stop offset="0.85" stop-color="#FFECB3" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint3_radial_93_78" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(19.5 16.5) rotate(52.7336) scale(28.9007)">
<stop stop-color="#FFC61A"/>
<stop offset="0.7" stop-color="#FF8000"/>
</radialGradient>
<clipPath id="clip0_93_78">
<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 36 12)"/>
</clipPath>
</defs>
</svg>
