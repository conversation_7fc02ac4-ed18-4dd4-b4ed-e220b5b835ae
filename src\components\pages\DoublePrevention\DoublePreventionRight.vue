<template>
    <div>
    <div class="secure-base-right">
        <right-top @openDialog="openDialog"></right-top>
        <right-center @openDialog="openDialog"></right-center>
        <right-bottom @openDialog="openDialog"></right-bottom>
    </div>
    <risk-control-measures-dialog  v-if="controlDialogShow" @closeDialog="closeControlDialog"></risk-control-measures-dialog>
</div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    defineEmits,
    getCurrentInstance,
} from "vue";
import WrapSlot from "../../commen/WrapSlot.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightBottom from "./subgroup/RightBottom.vue";
import RightCenter from "./subgroup/RightCenter.vue";
import RiskControlMeasuresDialog from './subgroup/RiskControlMeasuresDialog.vue'
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

const openDialog = (val,btnSelect,nowState) => {
    console.log(val,btnSelect,nowState, '打开弹窗');
    emit('openDialog',val,btnSelect,nowState);
}
const controlDialogShow = ref(false)
const control = () => {
    controlDialogShow.value = true;
    proxy.$loading.show();
};
const closeControlDialog = () => {
    proxy.$loading.hide();
    controlDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.secure-base-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
