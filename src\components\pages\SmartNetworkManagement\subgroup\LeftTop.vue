<template>
    <CardOne title="管网分布">
        <template v-slot>
            <div class="pipeline-network-distribution">
      <el-input v-model="filterText" placeholder="搜索" :prefix-icon="Search" />
  
      <el-tree
        accordion
        ref="treeRef"
        class="filter-tree"
        :data="data"
        node-key="id"
        :props="defaultProps"
        :filter-node-method="filterNode"
        @check-change="handleCheckChange"
        @node-click="handleNodeClick"
      />
    </div>
        </template>
    </CardOne>
</template>

  <script setup>
  import CardOne from "@/components/commenNew/CardOne.vue";
  import { ref, watch } from "vue";
  import { ElTree } from "element-plus";
  import { Search } from "@element-plus/icons-vue";
  
  const filterText = ref("");
  
  const defaultProps = {
    children: "children",
    label: "label",
  };
  const treeRef = ref();
  const handleNodeClick = (data) => {
    console.log(data.id);
  };
  watch(filterText, (val) => {
    treeRef.value.filter(val);
  });
  
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
  };
  
  const data = [
    {
      id: 1,
      label: "燃气管道",
      children: [
        {
          id: 11,
          label: "园区北路燃气管道",
        },
        {
          id: 12,
          label: "纬一路燃气管道",
        },
        {
          id: 13,
          label: "纬二路燃气管道",
        },
        {
          id: 14,
          label: "纬三路燃气管道",
        },
        {
          id: 15,
          label: "纬四路燃气管道",
        },
        {
          id: 16,
          label: "纬五路燃气管道",
        },
        {
          id: 17,
          label: "经五路燃气管道",
        },
        {
          id: 18,
          label: "经六路燃气管道",
        },
        {
          id: 19,
          label: "经七路燃气管道",
        },
        {
          id: 111,
          label: "经八路燃气管道",
        },
        {
          id: 111,
          label: "工业路燃气管道",
        },
      ],
    },
    {
      id: 2,
      label: "污水管道",
      children: [
        {
          id: 21,
          label: "纬一路污水管道",
        },
        {
          id: 22,
          label: "纬二路污水管道",
        },
        {
          id: 23,
          label: "纬三路污水管道",
        },
        {
          id: 24,
          label: "纬四路污水管道",
        },
        {
          id: 25,
          label: "纬五路污水管道",
        },
        {
          id: 26,
          label: "经六路污水管道",
        },
        {
          id: 27,
          label: "经七路污水管道",
        },
        {
          id: 28,
          label: "工业路污水管道",
        },
      ],
    },
    {
      id: 3,
      label: "热力管道",
      children: [
        {
          id: 31,
          label: "纬一路热力管道",
        },
        {
          id: 32,
          label: "纬二路热力管道",
        },
        {
          id: 33,
          label: "纬三路热力管道",
        },
        {
          id: 34,
          label: "纬四路热力管道",
        },
        {
          id: 35,
          label: "纬五路热力管道",
        },
        {
          id: 36,
          label: "经五路热力管道",
        },
        {
          id: 37,
          label: "经六路热力管道",
        },
        {
          id: 38,
          label: "经七路热力管道",
        },
        {
          id: 39,
          label: "经八路热力管道",
        },
        {
          id: 331,
          label: "工业路热力管道",
        },
        {
          id: 332,
          label: "企业路热力管道",
        },
      ],
    },
    {
      id: 4,
      label: "雨水管道",
      children: [
        {
          id: 41,
          label: "纬一路雨水管道",
        },
        {
          id: 42,
          label: "纬二路雨水管道",
        },
        {
          id: 43,
          label: "纬三路雨水管道",
        },
        {
          id: 44,
          label: "纬四路雨水管道",
        },
        {
          id: 45,
          label: "纬五路雨水管道",
        },
        {
          id: 46,
          label: "经六路雨水管道",
        },
        {
          id: 48,
          label: "工业路雨水管道",
        },
      ],
    },
    {
      id: 5,
      label: "中水管道",
      children: [
        {
          id: 51,
          label: "纬一路中水管道",
        },
        {
          id: 52,
          label: "纬二路中水管道",
        },
        {
          id: 53,
          label: "纬三路中水管道",
        },
        {
          id: 54,
          label: "纬四路中水管道",
        },
        {
          id: 55,
          label: "纬五路中水管道",
        },
        {
          id: 56,
          label: "经六路中水管道",
        },
        {
          id: 58,
          label: "工业路中水管道",
        },
      ],
    },
  ];
  const handleCheckChange = (data, checked, indeterminate) => {
    console.log(data, checked, indeterminate);
    console.log(data.id);
  };
  </script>
  
  <style lang="less" scoped>
  .pipeline-network-distribution {
    width: 416px;
    height: 928px;
    padding: 16px;
    // background-color: antiquewhite;
    .el-tree {
      background-color: transparent;
    }
    .el-input {
      background-color: transparent;
    }
    :deep(.el-input__wrapper) {
      background-color: transparent;
    }
    :deep(.el-tree-node__label) {
      color: #fff;
      font-family: Noto Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }
    :deep(.el-tree-node__content:hover) {
      // .is-expanded{
      // background-color: aqua;
  
      // }
      //   :deep(.is-focusable){
      //   background-color: aqua;
      // }
      background-color: rgba(48, 171, 232, 0.3);
    }
    :deep(.el-tree-node:focus > .el-tree-node__content) {
      background-color: transparent !important;
      color: #fff;
    }
    :deep(.el-tree-node > .el-tree-node__content) {
      height: 24px;
      padding: 8px 16px;
    }
    :deep(.el-input__wrapper) {
      border-radius: 20px;
      border: 1px solid #31aae8;
      // border-color: aquamarine !important;
      // box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      // border: 1px solid #31AAE8;
      //   :deep(.el-input__wrapper) {
      //   box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      //   cursor: default;
      //   .el-input__inner {
      //     cursor: default !important;
      //   }
      // }
    }
    :deep(.el-input__inner) {
      border-radius: 20px;
      // border-color: aquamarine !important;
      // border: 1px solid #31AAE8;
      color: #fff !important;
  
      font-family: Noto Sans SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    :deep(.el-input__prefix) {
      // border-color: aquamarine !important;
      // border: 1px solid #31AAE8;
      color: #fff !important;
    }
    .filter-tree {
      margin-top: 12px;
    }
  }
  </style>
  