﻿kind: Service
apiVersion: v1
metadata:
  name: ioc
  namespace: jinzhou-park-dev
  labels:
    app: ioc
    version: v1
  annotations:
    kubesphere.io/creator: mashuang
spec:
  ports:
    - name: http-80
      protocol: TCP
      port: 80
      targetPort: 80
      nodePort: 53347
  selector:
    app: ioc
  clusterIP: *************
  type: NodePort
  sessionAffinity: None
  externalTrafficPolicy: Cluster
