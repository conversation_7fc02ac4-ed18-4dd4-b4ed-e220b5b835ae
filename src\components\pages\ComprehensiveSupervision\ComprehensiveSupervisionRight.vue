<template>
    <div class="comprehensive-supervision-right">
        <!-- <wrap-slot title1="车辆统计" title2="实时监控" title3="实时告警">
            <template #content1>
            </template>
            <template #content2>
            </template>
            <template #content3>
            </template>
        </wrap-slot> -->
        <right-top-new></right-top-new>
        <right-center></right-center>
        <right-bottom></right-bottom>

    </div>
</template>

<script setup>
import WrapSlot from "../../commenNew/WrapSlot.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightTopNew from "./subgroup/RightTopNew.vue";
import RightBottom from "./subgroup/RightBottom.vue";
import RightCenter from "./subgroup/RightCenter.vue";
</script>

<style lang="less" scoped>
.comprehensive-supervision-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
