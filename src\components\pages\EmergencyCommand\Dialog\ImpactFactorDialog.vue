<template>
    <div class="impactDialog">
        <div class="command_title">
            <div>
                <img
                    class="title_image"
                    src="../../../../assets/images/card/title_left.svg"
                />
            </div>
            影响因子
            <img
                src="../../../../assets/images/card/index2_7.png"
                alt=""
                srcset=""
                class="closeImg"
                @click.stop="handleClose"
            />
        </div>
        <div class="impact_content">
            <el-form
                ref="formImpact1"
                :model="formImpact"
                :rules="rulesImpact"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="分析类型" prop="analysisType">
                            <el-select
                                v-model="formImpact.analysisType"
                                placeholder="请选择分析类型"
                                :popper-append-to-body="false"
                                popper-class="select-popper"
                                @change="selectType"
                            >
                                <el-option
                                    v-for="dict in optionsList"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="温度(T)" prop="temperature">
                            <el-input
                                v-model="formImpact.temperature"
                                placeholder="请输入温度"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="湿度(h)" prop="humidity">
                            <el-input
                                v-model="formImpact.humidity"
                                placeholder="请输入湿度"
                            />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="风级(W)" prop="windScale">
                            <el-input
                                v-model="formImpact.windScale"
                                placeholder="请输入风级"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="风速(m/min)" prop="windSpeed">
                            <el-input
                                v-model="formImpact.windSpeed"
                                placeholder="请输入风速"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="地形坡度(W)" prop="slope">
                            <el-input
                                v-model="formImpact.slope"
                                placeholder="请输入小于等于60的地形坡度"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="风向" prop="windDirection">
                            <el-select
                                v-model="formImpact.windDirection"
                                placeholder="请选择风向"
                                style="width: 10vw"
                                clearable
                            >
                                <el-option
                                    v-for="dict in dicts.fire_wind"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 1">
                        <el-form-item label="时间" prop="time">
                            <el-input
                                v-model="formImpact.time"
                                placeholder="请输入时间"
                            >
                                <template #suffix>
                                    <div class="text">min</div>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 2">
                        <el-form-item
                            label="爆炸物料总质量(Wf)"
                            prop="weight"
                        >
                            <el-input
                                v-model="formImpact.weight"
                                placeholder="爆炸物料总质量"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 2">
                        <el-form-item label="爆炸物料名称" prop="kd">
                            <el-select
                                filterable
                                class="text-case-type-select"
                                v-model="formImpact.matterId"
                                placeholder="请选择爆炸物料名称"
                            >
                                <el-option
                                    v-for="dict in materialNameList"
                                    :key="dict.id"
                                    :label="dict.name"
                                    :value="dict.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 3">
                        <el-form-item label="容积(m³)" prop="weight">
                            <el-input
                                v-model="formImpact.weight"
                                placeholder="请输入小于10000的容积"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 3">
                        <el-form-item label="温度(T)" prop="temperature">
                            <el-input
                                v-model="formImpact.temperature"
                                placeholder="请输入温度"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 3">
                        <el-form-item label="物料名称" prop="matterId">
                            <el-select
                                filterable
                                v-model="formImpact.matterId"
                                placeholder="请选择物料名称"
                            >
                                <el-option
                                    v-for="dict in materialNameListone"
                                    :key="dict.id"
                                    :label="dict.name"
                                    :value="dict.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 3">
                        <el-form-item label="风向" prop="windDirection">
                            <el-select
                                v-model="formImpact.windDirection"
                                placeholder="请选择风向"
                                style="width: 10vw"
                                clearable
                            >
                                <el-option
                                    v-for="dict in dicts.fire_wind"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 3">
                        <el-form-item label="风速(m/min)" prop="windSpeed">
                            <el-input
                                v-model="formImpact.windSpeed"
                                placeholder="请输入风速"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="formImpact.analysisType == 3">
                        <el-form-item label="时间" prop="time">
                            <el-input
                                v-model="formImpact.time"
                                placeholder="请输入时间"
                                ><template #suffix>
                                    <div class="text">min</div>
                                </template></el-input
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <div class="impactButton">
                <div
                    class="impactbutton_little_1"
                    @click="handleCancel"
                >
                    取消
                </div>
                <div
                    class="impactbutton_little"
                    @click="submitImpact"
                >
                    确定
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted, getCurrentInstance } from 'vue'
import { analysisdecision, getMatterMsds } from '../../../../assets/js/api/fire.js'

const { proxy } = getCurrentInstance()

// 定义props
const props = defineProps({
    propsList: {
        type: Object,
        required: true
    },
    dicts: {
        type: Object,
        default: () => ({})
    },
    nowWea: {
        type: Object,
        default: () => ({})
    },
    initialPoint: {
        type: Object,
        required: true
    },
    longAdd: {
        type: Number,
        required: true
    },
    latAdd: {
        type: Number,
        required: true
    }
})

// 定义emits
const emit = defineEmits(['close', 'click-list-button', 'remove-list'])

// 响应式数据
const formImpact1 = ref(null)
const formImpact = ref({})
const materialNameList = ref([])
const materialNameListone = ref([])

// 分析类型选项
const optionsList = ref([
    {
        label: "火灾影响分析",
        value: 1,
    },
    {
        label: "爆炸影响分析",
        value: 2,
    },
    {
        label: "毒气扩散分析",
        value: 3,
    },
])

// 表单验证规则
const rulesImpact = {
    // temperature: [{ required: true, message: "请输入", trigger: "blur" }],
    // humidity: [{ required: true, message: "请输入", trigger: "blur" }],
    // ks: [{ required: true, message: "请选择", trigger: "select" }],
    // windScale: [{ required: true, message: "请输入", trigger: "blur" }],
    // slope: [{ required: true, message: "请输入", trigger: "blur" }],
    // windDirection: [{ required: true, message: "请选择", trigger: "select" }],
    // time: [{ required: true, message: "请输入", trigger: "blur" }],
}

// 初始化表单数据
const initFormData = () => {
    formImpact.value = {
        temperature: "",
        humidity: "",
        windScale: "",
        windSpeed: "",
        slope: "",
        windDirection: "",
        time: "",
        longitude: props.propsList.itemList.longitude,
        latitude: props.propsList.itemList.latitude,
        analysisType: 1,
        analysisModel: 2,
    }
    
    // 设置天气数据
    if (props.nowWea) {
        formImpact.value.temperature = props.nowWea.temperature || ""
        formImpact.value.humidity = props.nowWea.humidity || ""
        formImpact.value.windScale = props.nowWea.windScale || ""
        
        if (props.nowWea.windDirection && props.dicts.fire_wind) {
            let xDir = props.nowWea.windDirection + "风"
            const windItem = props.dicts.fire_wind.find((i) => i.name === xDir)
            if (windItem) {
                formImpact.value.windDirection = windItem.code
            }
        }
    }
}

// 获取物料名称数据
const getMaterialName = () => {
    getMatterMsds({ analysisType: 1 }).then((res) => {
        materialNameList.value = res.data.data
    })
    getMatterMsds({ analysisType: 2 }).then((res) => {
        materialNameListone.value = res.data.data
    })
}

// 选择分析类型
const selectType = () => {
    // 可以在这里添加类型选择的逻辑
}

// 关闭弹窗
const handleClose = () => {
    proxy.$loading.hide()
    emit('close')
}

// 取消操作
const handleCancel = () => {
    proxy.$loading.hide()
    emit('close')
}

// 提交影响因子分析
const submitImpact = async () => {
    let x = parseInt(
        (props.propsList.itemList.longitude - props.initialPoint.longitude) /
            props.longAdd,
    )
    let y = parseInt(
        (props.initialPoint.latitude - props.propsList.itemList.latitude) /
            props.latAdd,
    )
    formImpact.value.x = x
    formImpact.value.y = y
    
    if (!formImpact1.value) return
    
    await formImpact1.value.validate((valid, fields) => {
        if (valid) {
            analysisdecision({
                ...formImpact.value,
            }).then((res) => {
                proxy.$loading.hide()
                emit('close')
                emit('click-list-button', false, [])
                
                if (formImpact.value.analysisType == 1) {
                    // 火灾影响分析
                    let saveData = []
                    if (
                        res.data.success &&
                        res.data.data.spreadModel &&
                        res.data.data.spreadModel.length
                    ) {
                        saveData = res.data.data.spreadModel.map((x) => {
                            return {
                                lnglat: x.split(","),
                            }
                        })
                        clickMass(saveData)
                    }
                } else if (formImpact.value.analysisType == 2) {
                    // 爆炸影响分析
                    clickExplosion(res.data.data)
                } else {
                    // 毒气扩散分析
                    let saveData = []
                    if (
                        res.data.success &&
                        res.data.data.spreadModel &&
                        res.data.data.spreadModel.length
                    ) {
                        saveData = res.data.data.spreadModel.map((x) => {
                            return {
                                lnglat: x.split(","),
                            }
                        })
                        clickMass(saveData)
                    }
                }
            })
        } else {
            console.log("error submit!", fields)
        }
    })
}

// 火灾/毒气扩散可视化
const clickMass = (valData) => {
    var style = [
        {
            url: "https://webapi.amap.com/images/mass/mass0.png",
            anchor: new AMap.Pixel(6, 6),
            size: new AMap.Size(11, 11),
            zIndex: 3,
        },
        {
            url: "https://webapi.amap.com/images/mass/mass1.png",
            anchor: new AMap.Pixel(4, 4),
            size: new AMap.Size(7, 7),
            zIndex: 2,
        },
        {
            url: "https://webapi.amap.com/images/mass/mass2.png",
            anchor: new AMap.Pixel(3, 3),
            size: new AMap.Size(5, 5),
            zIndex: 1,
        },
    ]
    let massAllPoints = new AMap.MassMarks(valData, {
        opacity: 0.8,
        zIndex: 111,
        cursor: "pointer",
        style: style,
    })
    window.massAllPoints = massAllPoints
    massAllPoints.setMap(window.map1)
    window.map1.setFitView()
}

// 爆炸影响可视化
const clickExplosion = (val) => {
    const center = new AMap.LngLat(
        props.propsList.itemList.longitude,
        props.propsList.itemList.latitude,
    )
    const blowData = [
        {
            radius: val.deathR,
            title: "",
        },
        {
            radius: val.seriousWoundR,
            title: "重伤",
        },
        {
            radius: val.slightWoundR,
            title: "轻伤",
        },
        {
            radius: val.propertyLossR,
            title: "财损",
        },
    ]
    blowData.sort((a, b) => {
        return a.radius - b.radius
    })
    const color = ["#d38b1d", "#e2ac5f", "#e2cfa1", "#e9efcf"]
    window.circleone = []
    blowData.forEach((item, index) => {
        var circleone = new AMap.Circle({
            center: center,
            radius: item.radius,
            borderWeight: 1,
            strokeColor: "#b8741b",
            strokeOpacity: 1,
            strokeWeight: 1,
            fillOpacity: 0.4,
            strokeStyle: "solid",
            strokeDasharray: [10, 10],
            fillColor: color[index],
            zIndex: 50 - index,
        })
        let bounds = circleone.getBounds()
        let point_lon = bounds.northEast.lng
        let point_lat = (bounds.northEast.lat + bounds.southWest.lat) / 2
        let text_lon = (circleone.getCenter().lng + point_lon) / 2
        let text_lat = point_lat
        circleone.on("mouseover", (e) => {
            window.infoWindow = new AMap.InfoWindow({
                position: [text_lon, text_lat],
                offset: new AMap.Pixel(0, -40),
                content: `${item.title}半径R${index}=${item.radius}m`,
            })
            window.infoWindow.open(window.map1)
        })
        window.circleone.push(circleone)
        window.map1.add(circleone)
        emit('remove-list', circleone)
    })
    window.map1.setFitView()
}

// 组件挂载时初始化
onMounted(() => {
    initFormData()
    getMaterialName()
})
</script>

<style scoped>
.title_image {
    width: 12px;
    height: 12px;
    margin: auto 4px auto 16px;
}

.impactDialog {
    /*background: url("../../../../assets/images/card/fire_5.png");*/
    background: url("../../../../assets/images/card/fire_2_1.png");
    background-size: cover;
    width: 845px;
    height: 410px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    z-index: 1005;
}

.command_title {
    margin-left: 17px;
    margin-top: 12px;
    font-style: normal;
    width: 100%;
    display: flex;
    margin-bottom: 30px;
    color: #fff;
    font-family: Noto Sans SC;
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
}

.closeImg {
    width: 14px;
    height: 14px;
    margin-right: 40px;
    margin-top: 10px;
    margin-left: auto;
}

.impact_content {
    margin-left: 24px;
}

.impact_content .el-input {
    width: 205px;
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.impact_content .el-select {
    width: 205px;
    border-radius: 2px;
    border: 1px solid #47ebeb;
    box-shadow: 0 0 0 0px !important;
}

.impact_content :deep(.el-input__wrapper) {
    background-color: transparent !important;
    box-shadow: 0 0 0 0px !important;
}

:deep(.el-form-item__label) {
    color: #fff;
    width: 142px;
    text-align: right;
}

.impact_content :deep(.el-form-item) {
    margin-bottom: 36px;
}

:deep(.el-input__inner) {
    color: #fff !important;
}

.impactButton {
    display: flex;
    gap: 40px;
    width: 100%;
    justify-content: center;
}

.impactbutton_little {
    width: 120px;
    height: 44px;
    color: #fff;
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 44px;
    border-radius: 4px;
    background: linear-gradient(180deg, #25c3ba 0%, #259fc3 100%);
    cursor: pointer;
}

.impactbutton_little_1 {
    width: 120px;
    height: 44px;
    color: #30abe8;
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 44px;
    border-radius: 4px;
    border: 1px solid #30abe8;
    cursor: pointer;
}

.text {
    color: #fff;
}
</style>
