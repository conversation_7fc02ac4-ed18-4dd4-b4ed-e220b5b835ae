<template>
    <Card title="企业作业报备排名">
        <template v-slot:title>
            <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
        </template>
        <template v-slot>
            <div class="spot-check-wrapper">
                <div class="list-header">
                    <div class="td1">排名</div>
                    <div class="td2">企业名称</div>
                    <div class="td3">报备总数</div>
                </div>   
                <div v-if="noDataFlag" class="list-content">
                    <div class="img-no">暂无数据</div>
                </div>
                <div v-else>
                    <autoScroll class="card-container" :step="2" :deltaTime="100" v-if="list&&list.length>4">   
                        <div class="list-content">
                            <div class="list-item" v-for="(item,index) in list" :key="index">
                                <div class="td1"><div class="td-index">{{index<10? '0'+(index+1):(index+1)}}</div></div>
                                <div class="td2">{{item.company_name}}</div>
                                <div class="td3 ">{{item.report_count}}</div>
                            </div>
                        </div>
                    </autoScroll>  
                    <div v-else class="list-content">
                        <div class="list-item" v-for="(item,index) in list" :key="index">
                            <div class="td1"><div class="td-index">{{index<10? '0'+(index+1):(index+1)}}</div></div>
                            <div class="td2">{{item.company_name}}</div>
                            <div class="td3 ">{{item.report_count}}</div>
                        </div>
                    </div>
                </div>
        </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import selectVue from "@/components/commenNew/selectVue.vue";
import autoScroll from "@/components/commenNew/autoScroll.vue";
import RightCenterJson from "./json/RightCenter.json";
import {
    ref,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from "vue";
const { proxy } = getCurrentInstance();
import { company_trend } from "@/assets/js/api/specialAssignments.js";
//按钮选项
const btnOptions = ref([
    {
        label:'本月',
        value:1
    },
    {
        label:'本年',
        value:2
    }
])
//按钮
let chooseValue = ref('本年');
//暂无数据展示
let noDataFlag = ref(false);

const selectBtn = (val) => {
    console.log(val,'vallllllllll');
    getData(chooseValue.value);
};
//列表
const list = ref([]);
// 定义日期变量
const currentDate = ref(new Date());
const firstDayOfMonth = ref(new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1));
const firstDayOfYear = ref(new Date(currentDate.value.getFullYear(), 0, 1));

// 格式化日期函数
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 获取格式化后的日期
const today = ref(formatDate(currentDate.value));
const monthStart = ref(formatDate(firstDayOfMonth.value));
const yearStart = ref(formatDate(firstDayOfYear.value));

const getData = (num) => {
    list.value = [];
    noDataFlag.value = true;
    // console.log(today.value,monthStart.value,yearStart.value);
    if(num==1){
        company_trend({
            beginTime:monthStart.value,
            endTime:today.value
        }).then(res=>{
            console.log(res);
            if(res.data&&res.data.data&&res.data.data.length>0){
                list.value = res.data.data;
                noDataFlag.value = false;
                // list.value = RightCenterJson.data;

            }
        })
    }else{
        company_trend({
            beginTime:yearStart.value,
            endTime:today.value
        }).then(res=>{
            console.log(res.data.data);
            if(res.data&&res.data.data&&res.data.data.length>0){
                list.value = res.data.data;
                noDataFlag.value = false;
                // list.value = RightCenterJson.data;
            }
        })
    }
};
onMounted(() => {
    getData(2);
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>
.spot-check-wrapper{
    width: 416px;
    height: 224px;
    padding: 16px;
    // background-color: #1A9FFF;
   .list-header{
    width: 416px;
height: 40px;
background: linear-gradient(90deg, 
        rgba(26, 159, 255, 0) 0%, 
        #1A9FFF4D, 
        rgba(26, 159, 255, 0) 100%
    );
    display: flex;
    align-items: center;

    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 40px;
color: #FFFFFF;
   }
   .card-container{
    width: 416px;
    height: 184px;
    
   }
   .img-no {
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 184px; /* 150% */
        }
   .list-content{
    width: 416px;
    height: 184px;
    
    
    .list-item{
        width: 416px;
height: 40.8px;
display: flex;
align-items: center;
border-bottom: 1px solid #1A9FFF73;
font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: #FFFFFF;

    }
   }
   .td1{
    width: 64px;
text-align: center;

   }
   .td2{
    width: 248px;
    padding-left: 16px;
text-align: left;
   }
   .td3{
    width: 72px;
    text-align: left;
    padding-left: 16px;
   }
   .td-index{
    width: 24px;
height: 24px;
margin:auto;
background: url("@/assets/newImages/td-index.svg") no-repeat;
            background-size: cover;
            background-position: center;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;

   }
}
</style>
