<template>
    <Card title="在线设备数量">
        <template v-slot>
    <div class="device-num">
        <div
            :class="[
                list.length > 3 ? 'normal-container_warp' : 'normal-container',
            ]"
        >
            <div
                :class="index > 2 ? 'container' : 'container-two'"
                v-for="(item, index) in showList"
                :key="index"
            >
                <div class="num">{{ item.count }}</div>
                <div class="text" :title="item.value">{{ item.value }}</div>
            </div>
        </div>
        <div class="switch-button">
            <div class="switch-button-inner">
                <div
                    v-for="index in Math.ceil(list.length / 6)"
                    :key="'list' + index"
                    :class="
                        activePage == index - 1 ? 'active-switch' : 'switch'
                    "
                    @click="switchButton(index)"
                ></div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import { List } from "echarts";
import { ref, reactive, onMounted, onBeforeUnmount, computed } from "vue";
import { njdevice_type } from "../../../../assets/js/api/comprehensiveSupervision";

const list = ref([
{
            count: "32",
            value: "摄像头",
            total: "55"
        },
        {
            count: "0",
            value: "电表",
            total: "55"
        },
        {
            count: "17",
            value: "雨水设备",
            total: "55"
        },
        {
            count: "1",
            value: "废气设备",
            total: "55"
        },
        {
            count: "5",
            value: "环境空气微站",
            total: "55"
        },
        {
            count: "0",
            value: "道闸相机",
            total: "55"
        },
        {
            count: "0",
            value: "道闸",
            total: "55"
        },
        {
            count: "0",
            value: "消防主机",
            total: "55"
        }
]);
const showList = ref([]);
const activePage = ref(0);
const switchButton = (val) => {
    activePage.value = val - 1;
    if (list.value.length > activePage.value * 6 + 6) {
        showList.value = list.value.slice(
            activePage.value * 6,
            activePage.value * 6 + 6,
        );
    } else {
        showList.value = list.value.slice(activePage.value * 6);
    }
};

onMounted(() => {
    showList.value = list.value.slice(
                activePage.value * 6,
                activePage.value * 6 + 6,
            );
    // njdevice_type({ "": "" }).then((res) => {
    //     if (res.data.success && res.data.data && res.data.data.length) {
    //         list.value = res.data.data;
    //         showList.value = list.value.slice(
    //             activePage.value * 6,
    //             activePage.value * 6 + 6,
    //         );
    //     }
    // });
});
</script>

<style lang="less" scoped>
.device-num {
    width: 416px;
    height: 224px;
    padding: 16px;
    position: relative;
    .normal-container {
        flex-direction: row;
        align-items: center;
        display: flex;
        gap: 22px;
    }
    .normal-container_warp {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 22px;
    }
    .container {
        width: 123px;
        height: 100px;
        background: url("../../../../assets/images/card/device.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        // margin-top: 4px;
    }
    .container-two {
        width: 123px;
        height: 100px;
        background: url("../../../../assets/images/card/device.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        // margin-top: 16px;
    }
    .num {
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px; /* 133.333% */
        margin-top: 46px;
    }
    .text {
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .switch-button {
        text-align: center;
        height: 4px;
        position: absolute;
        bottom: 8px;
        margin: 0 auto;
        width: 100%;

        .switch-button-inner {
            margin: 0 auto;
            display: flex;
            justify-content: center;
            gap: 4px;
        }
        .active-switch {
            width: 24px;
            height: 4px;
            border-radius: 2px;
            background: #47ebeb;
        }
        .switch {
            width: 16px;
            height: 4px;
            border-radius: 2px;
            background: rgba(48, 171, 232, 0.6);
        }
    }
}
</style>
