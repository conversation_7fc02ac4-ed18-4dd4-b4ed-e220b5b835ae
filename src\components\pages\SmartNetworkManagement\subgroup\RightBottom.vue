<template>
    <Card title="管网告警">
        <template v-slot>
            <div class="pipe-network-alarm">
                <div v-if="alarmList == null || alarmList.length == 0">
                    <div class="none-img"></div>
            <div class="text">暂无告警数据</div>
        </div>
        <table class="alarm-table" cellspacing="4" cellpadding="0" v-else>
            <tr class="row" v-for="(item, index) in alarmList" :key="index">
                <td class="alarm-id-small" v-if="index < 5">{{ index + 1 }}</td>
                <td class="alarm-id-big" v-else></td>
                <td class="alarm-detail">{{ item.door }}</td>
                <td class="alarm-detail">{{ item.event }}</td>
                <td class="alarm-detail">{{ item.text }}</td>
                <td class="alarm-date">{{ item.date }}</td>
                <!-- <td class="alarm-time">{{item.time}}</td> -->
            </tr>
            <!-- <tr v-if="alarmList == null || alarmList.length ==0">
        <td colspan="4">暂无数据</td>
      </tr> -->
                </table>
            </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
const alarmList = ref([]);

onMounted(() => {
    alarmList.value = [];
});
</script>

<style lang="less" scoped>
.pipe-network-alarm {
    // width: 400px;
    // height: 256px;
    width: 416px;
    height: 224px;
    padding: 16px;
    overflow: auto;
    .none-img {
        width: 190px;
        height: 134px;
        background: url("../../../../assets/images/card/none.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: 40px auto 10px;
    }
    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 157.143% */
        text-align: center;
    }
    .alarm-table {
        .row {
            margin-top: 54px;

            .alarm-id-small {
                width: 24px;
                height: 24px;
                background: url("../../../../assets/images/card/order-number.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
            .alarm-id-big {
                width: 24px;
                height: 24px;
                background: url("../../../../assets/images/card/alarm-order.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .alarm-detail {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
            .alarm-date {
                padding-left: 24px;
                color: #47ebeb;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
            .alarm-time {
                color: #47ebeb;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
        }
    }
}
.pipe-network-alarm::-webkit-scrollbar {
    width: 3px;
    height: 10px;
    background-color: transparent;
}
</style>
