<template>
    <Card title="能耗季度同比分析">
        <template v-slot>
            <div class="linewrap" ref="linewrap"></div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    energy_quarter_yoy_water,
    energy_quarter_yoy_gas,
    energy_quarter_yoy_steam,
    energy_quarter_yoy_electricity,
} from "../../../../assets/js/api/smartEnergy";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
const timer = ref(null);
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let electricity = ref([]);
let water = ref([]);
let naturalGas = ref([]);
let steam = ref([]);
const initChart = () => {
    option = {
        grid: {
            left: 10,
            right: 25,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        tooltip: {
            show: true,
            trigger: "axis",
            //     formatter: function (params) {
            //         console.log(params);
            //     return params.name + '<br />'+params.seriesName+ '<br />' + params.value+'%';
            //   },
        },
        legend: {
            show: false,
            x: "right", // 图例水平居中
            y: "top", // 图例垂直居上
            itemStyle: { opacity: 0 }, //去圆点
            textStyle: {
                color: "#fff",
            },
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    color: "#1AB2FF",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "#195384",
                    },
                },
                data: xData,
            },
        ],
        yAxis: [
            {
                type: "value",
                name: "%",
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#fff",
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#27b4c2",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
            },
        ],
        series: [
            {
                name: "电",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: false,
                itemStyle: {
                    normal: {
                        color: "#30ABE8",
                        lineStyle: {
                            color: "#30ABE8",
                            width: 1,
                        },
                    },
                },
                data: electricity,
            },
            {
                name: "水",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: false,
                itemStyle: {
                    normal: {
                        color: "#4DCB62",
                        lineStyle: {
                            color: "#4DCB62",
                            width: 1,
                        },
                    },
                },
                data: water,
            },
            {
                name: "天然气",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: false,
                itemStyle: {
                    normal: {
                        color: "#99B2FF",
                        lineStyle: {
                            color: "#99B2FF",
                            width: 1,
                        },
                    },
                },
                data: naturalGas,
            },
            {
                name: "蒸汽",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: false,
                itemStyle: {
                    normal: {
                        color: "#FF791A",
                        lineStyle: {
                            color: "#FF791A",
                            width: 1,
                        },
                    },
                },
                data: steam,
            },
        ],
    };
};
onMounted(() => {
    xData = ["第一季度", "第二季度", "第三季度", "第四季度"];
    electricity = [0, 0, 0, 0];
    water = [0, 0, 0, 0];
    naturalGas = [0, 0, 0, 0];
    steam = [0, 0, 0, 0];
    energy_quarter_yoy_water({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            res.data.data.forEach((item) => {
                if (item.quarter == "1") {
                    water[0] =
                        item.YOY == null
                            ? 0
                            : Number(item.YOY.replace("%", ""));
                } else if (item.quarter == "2") {
                    water[1] =
                        item.YOY == null
                            ? 0
                            : Number(item.YOY.replace("%", ""));
                } else if (item.quarter == "3") {
                    water[2] =
                        item.YOY == null
                            ? 0
                            : Number(item.YOY.replace("%", ""));
                } else {
                    water[3] =
                        item.YOY == null
                            ? 0
                            : Number(item.YOY.replace("%", ""));
                }
            });
        }
        energy_quarter_yoy_gas({ "": "" }).then((res1) => {
            if (res1.data.success && res1.data.data && res1.data.data.length) {
                res1.data.data.forEach((item) => {
                    if (item.quarter == "1") {
                        naturalGas[0] =
                            item.YOY == null
                                ? 0
                                : Number(item.YOY.replace("%", ""));
                    } else if (item.quarter == "2") {
                        naturalGas[1] =
                            item.YOY == null
                                ? 0
                                : Number(item.YOY.replace("%", ""));
                    } else if (item.quarter == "3") {
                        naturalGas[2] =
                            item.YOY == null
                                ? 0
                                : Number(item.YOY.replace("%", ""));
                    } else {
                        naturalGas[3] =
                            item.YOY == null
                                ? 0
                                : Number(item.YOY.replace("%", ""));
                    }
                });
                console.log(naturalGas);
            }
            energy_quarter_yoy_steam({ "": "" }).then((res2) => {
                if (
                    res2.data.success &&
                    res2.data.data &&
                    res2.data.data.length
                ) {
                    res2.data.data.forEach((item) => {
                        console.log(Number(item.YOY.replace("%", "")));
                        if (item.quarter == "1") {
                            steam[0] =
                                item.YOY == null
                                    ? 0
                                    : Number(item.YOY.replace("%", ""));
                        } else if (item.quarter == "2") {
                            steam[1] =
                                item.YOY == null
                                    ? 0
                                    : Number(item.YOY.replace("%", ""));
                        } else if (item.quarter == "3") {
                            steam[2] =
                                item.YOY == null
                                    ? 0
                                    : Number(item.YOY.replace("%", ""));
                        } else {
                            steam[3] =
                                item.YOY == null
                                    ? 0
                                    : Number(item.YOY.replace("%", ""));
                        }
                    });
                    console.log(steam);
                }
                energy_quarter_yoy_electricity({ "": "" }).then((res3) => {
                    if (
                        res3.data.success &&
                        res3.data.data &&
                        res3.data.data.length
                    ) {
                        res3.data.data.forEach((item) => {
                            console.log(Number(item.YOY.replace("%", "")));
                            if (item.quarter == "1") {
                                electricity[0] =
                                    item.YOY == null
                                        ? 0
                                        : Number(item.YOY.replace("%", ""));
                            } else if (item.quarter == "2") {
                                electricity[1] =
                                    item.YOY == null
                                        ? 0
                                        : Number(item.YOY.replace("%", ""));
                            } else if (item.quarter == "3") {
                                electricity[2] =
                                    item.YOY == null
                                        ? 0
                                        : Number(item.YOY.replace("%", ""));
                            } else {
                                electricity[3] =
                                    item.YOY == null
                                        ? 0
                                        : Number(item.YOY.replace("%", ""));
                            }
                        });
                        console.log(steam);
                        lineChart = echarts.init(linewrap.value);
                        initChart();
                        lineChart.setOption(option);
                        window.addEventListener("resize", () => {
                            lineChart.resize();
                        });
                    } else {
                        lineChart = echarts.init(linewrap.value);
                        initChart();
                        lineChart.setOption(option);
                        window.addEventListener("resize", () => {
                            lineChart.resize();
                        });
                    }
                });
            });
        });
    });
});
onBeforeUnmount(() => {
    // loopShowTooltip(lineChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.linewrap {
    width: 416px;
    height: 224px;
    padding: 16px;
}
</style>
