import axios from "axios";
import { ElLoading, ElMessage } from "element-plus";

/**
 * 严格模拟qs库的URL编码行为
 */
const strictQs = {
  stringify(obj, options = {}) {
    const encode = (val, isKey = false) => {
      if (val === null || val === undefined) return '';
      const str = String(val);
      // 对键和值都进行编码，但保留方括号的编码方式
      return encodeURIComponent(str)
        .replace(/%5B/g, '[').replace(/%5D/g, ']') // 先解码方括号
        .replace(/\[/g, '%5B').replace(/\]/g, '%5D'); // 再重新编码
    };

    const processKey = (key, value, path = []) => {
      if (Array.isArray(value)) {
        return value.map((item, i) => 
          processKey(key, item, [...path, i])
        ).join('&');
      } else if (typeof value === 'object' && value !== null) {
        return Object.entries(value).map(([k, v]) => 
          processKey(key, v, [...path, k])
        ).join('&');
      } else {
        const encodedKey = path.reduce((acc, p) => 
          `${acc}%5B${encode(p, true)}%5D`, 
          encode(key, true)
        );
        return `${encodedKey}=${encode(value)}`;
      }
    };

    return Object.entries(obj)
      .filter(([_, v]) => v !== null && v !== undefined)
      .map(([k, v]) => processKey(k, v))
      .join('&');
  }
};

// 加载状态管理（保持不变）
let loading = {
  loadingInstance: null,
  loadingStart() {
    this.loadingInstance = ElLoading.service({
      fullscreen: true,
      lock: true,
      text: "数据加载中...",
      background: "rgba(0, 0, 0, 0.6)",
    });
  },
  loadingEnd() {
    this.loadingInstance?.close();
  },
};

export function fetch(
  url,
  params = {},
  method = "POST",
  sign,
  timestamp,
  formData = false,
  { withMask = false, ...config } = {}
) {
  if (!url) {
    throw new Error("interface path not found");
  }

  withMask && loading.loadingStart();

  // 请求头配置（保持原样）
  let _headers;
  const token = localStorage.getItem("token");
  if (
    url.indexOf("/emergency-v2/") != -1 ||
    url.indexOf("/schedule/schedule/hxGetCurrentFireWatch") != -1
  ) {
    _headers = {
      Authorization: token,
      "Content-Type": formData ? "multipart/form-data" : "application/json",
    };
  } else if (
    url.indexOf("/equipment/") != -1 ||
    url.indexOf("/security/") != -1 ||
    url.indexOf("/security-v2/") != -1
  ) {
    _headers = {
      Authorization: token,
      "Content-Type": formData ? "multipart/form-data" : "application/json",
    };
  } else {
    _headers = {
      Authorization: token,
      "Content-Type": formData ? "multipart/form-data" : "application/json",
      sign: sign,
      timestamp: timestamp,
    };
  }

// 使用严格模式的序列化
  const instance = axios.create({
    paramsSerializer: params => strictQs.stringify(params)
  });

  const isPOST = method === "POST";

  return new Promise((resolve, reject) => {
    instance({
      url: url,
      method,
      headers: _headers,
      params: isPOST ? null : params,
      data: isPOST ? params : null,
      ...config,
    })
    .then((res) => {
      resolve(res); // 保持原样
    })
    .catch((err) => {
      reject({ requestUrl: url, resErr: err }); // 保持原样
    })
    .finally(() => {
      withMask && loading.loadingEnd();
    });
  });
}