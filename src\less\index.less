* {
    padding: 0;
    margin: 0;
}
*{
  /* 输入框背景颜色 */
  --el-fill-color-blank: transparent;
  /* 输入框禁用时的背景色 */
  --el-fill-color-light: #336788;
  /* 输入框边框颜色 */
  --el-border-color: #1A9FFF;
  /* 日期弹窗背景色 */
  --el-bg-color-overlay: #1A9FFF;
  /* 日期弹窗边框色 */
  --el-border-color-light: #1A9FFF;
  /* 日期弹窗文字 */
  --el-text-color-regular: white;
  /* 日期选中后的背景颜色 */
  --el-border-color-extra-light: #274966;
  /* 日期选中时的高亮颜色 */
  --el-text-color-primary: #409eff;

}

/* 下拉箭头图标样式 */
.el-select .el-input .el-select__caret.el-icon {
    /* 恢复显示下拉箭头 */
    display: inline-flex !important;
    color: rgba(26, 159, 255, 0.6);
}

/* 下拉选择框样式 */
.el-select .el-input__wrapper {
    border: 1px solid #1A9FFF !important;
}

/* 全局样式：下拉菜单样式 */
.el-popper.is-light {
    border: 1px solid #1A9FFF !important;
    background-color: rgba(5, 29, 46, 0.9) !important;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* el-select 下拉框样式 */
.el-select-dropdown__item {
  background-color: transparent !important;
  color: #1A9FFF !important;
}

.el-select-dropdown__item:hover,
.el-select-dropdown__item.hover {
  background-color: rgba(26, 159, 255, 0.1) !important;
  color: rgba(128, 234, 255, 1) !important;
}

/* 下拉框本身样式 */
.el-select .el-input.el-input--suffix {
  background-color: transparent !important;
}

/* 输入框包装器 */
.el-select .el-select__wrapper,
.el-select .el-select-selection,
.el-select .el-input__wrapper {
  background-color: transparent !important;
  border: 1px solid #1A9FFF !important;
  box-shadow: none !important;
}

/* 输入框文本 */
.el-select .el-select__input,
.el-select .el-input__inner {
  color: #fff !important;
  background-color: transparent !important;
}

/* 下拉菜单容器 */
.el-select-dropdown.el-popper {
  background-color: rgba(5, 29, 46, 0.9) !important;
  border: 1px solid #1A9FFF !important;
}

/* 所有输入框的共同样式 */
.el-input .el-input__wrapper {
    background-color: transparent !important;
    box-shadow: 0 0 0 0 !important;
    border-radius: 4px !important;
    border: 1px solid #1A9FFF !important;
    // height: 22px !important;
    // padding: 7px 12px !important;
}

.el-input .el-input__inner {
    color: #fff !important;
    font-family: Noto Sans SC !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 20px !important;
}

.el-input .el-input__inner::placeholder {
    color: #fff !important;
    font-family: Noto Sans SC !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 20px !important;
}

/* 日期选择器样式 */
.el-date-editor.el-input {
    --el-date-editor-width: 210px;
}

.el-date-editor .el-input__wrapper {
    border: 1px solid #1A9FFF !important;
}

.el-date-editor .el-input__inner {
    color: #fff !important;
    font-family: Noto Sans SC !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 20px !important;
}

.el-date-editor .el-input__prefix-icon,
.el-date-editor .el-input__suffix-icon {
    color: rgba(26, 159, 255, 0.6) !important;
}

/* 日期选择器弹出框样式 */
.el-picker__popper.el-popper {
    border: 1px solid #1A9FFF !important;
    background-color: rgba(5, 29, 46, 0.9) !important;
}

.el-picker__popper .el-popper__arrow::before {
    background-color: rgba(5, 29, 46, 0.9) !important;
    border-top-color: #1A9FFF !important;
    border-left-color: #1A9FFF !important;
}

/* 日期选择器内部样式 */
.el-picker-panel {
    color: #fff !important;
    background-color: rgba(5, 29, 46, 0.9) !important;
    border: none !important;
}

.el-date-picker__header {
    color: #fff !important;
}

.el-date-picker__header-label {
    color: #fff !important;
}

.el-date-table th {
    color: rgba(128, 234, 255, 1) !important;
}

.el-date-table td.available:hover {
    color: rgba(128, 234, 255, 1) !important;
}

.el-date-table td.available:hover span {
    background-color: rgba(26, 159, 255, 0.3) !important;
}

.el-date-table td.current:not(.disabled) span {
    background-color: rgba(26, 159, 255, 0.3) !important;
    color: rgba(128, 234, 255, 1) !important;
}

.el-date-table td.today span {
    color: rgba(128, 234, 255, 1) !important;
}

.el-picker-panel__icon-btn {
    color: rgba(26, 159, 255, 0.6) !important;
}

.el-date-picker__header-label:hover {
    color: rgba(128, 234, 255, 1) !important;
}

/* 日期表格单元格样式 */
.el-date-table td {
    color: #fff !important;
}

/* 日期选择器底部按钮 */
.el-picker-panel__footer {
    background-color: transparent !important;
    border-top: 1px solid rgba(26, 159, 255, 0.3) !important;
}

.el-picker-panel__footer .el-button {
    color: rgba(26, 159, 255, 0.6) !important;
    border-color: rgba(26, 159, 255, 0.6) !important;
    background-color: transparent !important;
}

.el-picker-panel__footer .el-button:hover {
    color: rgba(128, 234, 255, 1) !important;
    border-color: rgba(26, 159, 255, 0.6) !important;
    background-color: rgba(26, 159, 255, 0.1) !important;
}

/* 复选框样式 */
.el-checkbox {
    margin-right: 20px !important;
}

/* 未选中状态 - 蓝色边框 */
.el-checkbox__input .el-checkbox__inner {
    background-color: transparent !important;
    border: 1px solid #1A9FFF !important;
    border-radius: 2px !important;
    width: 16px !important;
    height: 16px !important;
}

/* 选中状态 - 黄色背景和边框 */
.el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: rgba(255, 198, 26, 0.3) !important;
    border-color: rgba(255, 198, 26, 1) !important;
}

/* 选中状态的勾选图标 */
.el-checkbox__input.is-checked .el-checkbox__inner::after {
    border-color: rgba(255, 198, 26, 1) !important;
    transform: rotate(45deg) scaleY(1) !important;
    width: 5px !important;
    height: 7px !important;
    border-width: 2px !important;
    left: 5px !important;
    top: 2px !important;
}

/* 复选框文字样式 */
.el-checkbox__label {
    color: #fff !important;
    font-size: 14px !important;
    padding-left: 8px !important;
}

/* 选中状态的文字样式 */
.el-checkbox__input.is-checked + .el-checkbox__label {
    color: #fff !important;
}

/* 确保下拉框中显示的文本颜色与输入框一致 */
.el-select .el-select__wrapper .el-input__inner,
.el-select .el-input__wrapper .el-input__inner,
.el-select .el-input .el-input__inner {
  color: #fff !important;
  font-family: Noto Sans SC !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 20px !important;
}

/* 确保选中的值显示颜色正确 */
.el-select-v2__selected-item,
.el-select-v2__selected,
.el-select__selected,
.el-select .el-select-tags-wrapper .el-tag {
  color: #fff !important;
}

/* 确保占位符文本颜色也一致 */
.el-select .el-input__inner::placeholder {
  color: #fff !important;
}
/* 确保占位符文本颜色也一致 */
.el-select .el-select__placeholder {
  color: #fff !important;
}

*{
/* 日期弹窗背景色 */
--el-bg-color-overlay: transparent;
/* 日期弹窗边框色 */
--el-border-color-light: transparent;
}

/* 级联选择器样式 - 完全重写 */


.el-cascader .el-input__wrapper {
  background-color: transparent !important;
  box-shadow: none !important;
  border-radius: 4px !important;
  border: 1px solid #1A9FFF !important;
}

.el-cascader .el-input__inner {
  color: #fff !important;
  font-family: Noto Sans SC !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 20px !important;
  background-color: transparent !important;
}

/* 下拉箭头图标 */
.el-cascader .el-input__suffix-inner .el-icon,
.el-cascader .el-input .el-cascader__caret.el-icon,
.el-cascader .el-input__icon {
  color: rgba(26, 159, 255, 0.6) !important;
  display: inline-flex !important;
}

/* 下拉面板容器 */
.el-cascader-panel {
  border: 1px solid #1A9FFF !important;
  background-color: rgba(5, 29, 46, 0.9) !important;
}

/* 级联菜单 */
.el-scrollbar.el-cascader-menu {
  border-right: 1px solid rgba(26, 159, 255, 0.3) !important;
  background-color: transparent !important;
}

/* 级联菜单项 - 精确匹配DOM结构 */
.el-scrollbar__view.el-cascader-menu__list .el-cascader-node {
  color: #1A9FFF !important;
  background-color: transparent !important;
  font-family: Noto Sans SC !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 20px !important;
  padding: 8px 20px !important;
}

/* 悬停状态 */
.el-scrollbar__view.el-cascader-menu__list .el-cascader-node:hover,
.el-scrollbar__view.el-cascader-menu__list .el-cascader-node:focus {
  background-color: rgba(26, 159, 255, 0.1) !important;
  color: rgba(128, 234, 255, 1) !important;
}

/* 选中状态 */
.el-scrollbar__view.el-cascader-menu__list .el-cascader-node.is-selectable.in-active-path,
.el-scrollbar__view.el-cascader-menu__list .el-cascader-node.is-selectable.in-checked-path,
.el-scrollbar__view.el-cascader-menu__list .el-cascader-node.is-active {
  color: rgba(128, 234, 255, 1) !important;
  background-color: rgba(26, 159, 255, 0.1) !important;
  font-weight: 400 !important;
}

/* 展开图标 */
.el-cascader-node__postfix,
.el-icon.arrow-right.el-cascader-node__postfix {
  color: rgba(26, 159, 255, 0.6) !important;
}

/* 隐藏前缀图标（选中标记） */
.el-cascader-node__prefix {
  display: none !important;
}

/* 级联节点标签 */
.el-cascader-node__label {
  color: #1A9FFF !important;
}

/* 选中状态的标签 */
.el-cascader-node.is-active .el-cascader-node__label,
.el-cascader-node.in-active-path .el-cascader-node__label {
  color: rgba(128, 234, 255, 1) !important;
}

/* 确保占位符文本颜色也一致 */
.el-cascader .el-input__inner::placeholder {
  color: #fff !important;
  opacity: 0.8 !important;
}

/* 单选框样式 */
.el-radio {
  margin-right: 12px !important;
}

/* 未选中状态 - 蓝色边框和背景透明 */
.el-radio__input .el-radio__inner {
  background-color: transparent !important;
  border: 1px solid #1A9FFF !important;

}

/* 选中状态 - 黄色边框 */
.el-radio__input.is-checked .el-radio__inner {
  background-color: transparent !important;
  border: 2px solid #FFC61A !important; /* 金黄色 */
}

/* 选中状态的内部圆点 */
.el-radio__input.is-checked .el-radio__inner::after {
  background-color: #FFC61A !important; /* 金黄色 */

  transform: translate(-50%, -50%) !important;
}

/* 单选框文字样式 */
.el-radio__label {
  color: #fff !important;
  font-size: 14px !important;
  padding-left: 4px !important;
}

/* 选中状态的文字样式 */
.el-radio__input.is-checked + .el-radio__label {
  color: #fff !important;
}

// /* 悬停状态 */
// .el-radio:hover .el-radio__inner {
//   border-color: #47EBEB !important;
// }

// /* 禁用状态 */
// .el-radio.is-disabled .el-radio__input .el-radio__inner {
//   background-color: rgba(255, 255, 255, 0.1) !important;
//   border-color: rgba(255, 255, 255, 0.3) !important;
// }

// .el-radio.is-disabled .el-radio__label {
//   color: rgba(255, 255, 255, 0.5) !important;
// }
