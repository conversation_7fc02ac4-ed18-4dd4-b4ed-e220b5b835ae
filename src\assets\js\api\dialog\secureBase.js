import { fetch } from "../../request";
//园区专利档案
export const dict_allList = (params) =>fetch("/information-v2/ioc/dict-allList", { ...params }, "POST");
//园区信息
export const park_basic_page = (params) =>fetch("/information-v2/ioc/park-basic-page", { ...params }, "POST");
//园区设立信息
export const establish_info_page = (params) =>fetch("/information-v2/ioc/establish-info-page", { ...params }, "POST");
//园区产业定位
export const industry_location_page = (params) =>fetch("/information-v2/ioc/industry-position-page", { ...params }, "POST");
//园区规划审批
export const plan_approval_page = (params) =>fetch("/information-v2/ioc/plan-approval-page", { ...params }, "POST");
//园区获奖档案
export const award_file_page = (params) =>fetch("/information-v2/ioc/award-file-page", { ...params }, "POST");
//园区专利档案
export const patent_file_page = (params) =>fetch("/information-v2/ioc/patent-file-page", { ...params }, "POST");
//园区安全管理机构
export const emergency_institution_page = (params) =>fetch("/information-v2/ioc/emergency-institution-page", { ...params }, "POST");
//园区安全管理机构-人员列表
export const emergency_institution_person_page = (params) =>fetch("/information-v2/ioc/emergency-institution-person-page", { ...params }, "POST");
//人员档案
export const page_enterprise_staff = (params) =>fetch("/information-v2/ioc/page-enterprise-staff", { ...params }, "POST");
//第三方单位
export const page_third_supervision = (params) =>fetch("/information-v2/ioc/page-third-supervision", { ...params }, "POST");
//事故事件
export const accident_case_page = (params) =>fetch("/information-v2/ioc/accident-case-page", { ...params }, "POST");
//危险化学品禁限控目录
export const chemical_substance_page = (params) =>fetch("/information-v2/ioc/chemical-substance-page", { ...params }, "POST");
//工艺禁限控目录
export const craft_forbidden_page = (params) =>fetch("/information-v2/ioc/craft-forbidden-page", { ...params }, "POST");
//装置禁限控目录
export const equipment_control_page = (params) =>fetch("/information-v2/ioc/equipment-control-page", { ...params }, "POST");
//安全生产行政许可
export const product_certificate_page = (params) =>fetch("/information-v2/ioc/product-certificate-page", { ...params }, "POST");
//即将到期安全生产行政许可
export const product_certificate_expire = (params) =>fetch("/information-v2/ioc/product-certificate-expire", { ...params }, "POST");
//装置开停车和大检修
export const outage_maintenance_page = (params) =>fetch("/information-v2/ioc/outage-maintenance-page", { ...params }, "GET");
//监督检查
export const supervision_plan_list = (params) =>fetch("/information-v2/ioc/supervision-plan-list", { ...params }, "POST");
//执法任务
export const execution_task_page = (params) =>fetch("/information-v2/ioc/execution-task-page", { ...params }, "POST");
//危险化工工艺
export const danger_chemical_process_page = (params) =>fetch("/information-v2/ioc/danger-chemical-process-page", { ...params }, "POST");
//危险化学品
export const danger_chemical_page = (params) =>fetch("/information-v2/ioc/danger-chemical-page", { ...params }, "POST");
//重大危险源
export const major_danger_page = (params) =>fetch("/safety-v2/major-danger-source-information/select-page", { ...params }, "GET");
//值班值守
export const duty_list = (params) =>fetch("/information-v2/ioc/duty-list", { ...params }, "POST");

/*
企业信息
* */
//企业基本分页列表
export const enterprise_info_page = (params) =>fetch("/information-v2/ioc/enterprise-info-page", { ...params }, "POST");
//企业详情
export const enterprise_info_detail = (params) =>fetch(`/information-v2/ioc/enterprise-info-detail/${params}`, { ...params }, "GET");
//厂房车间
export const plant_workshop_page = (params) =>fetch("/information-v2/ioc/plant-workshop-page", { ...params }, "POST");
//生产线
export const product_line_page = (params) =>fetch("/information-v2/ioc/product-line-page", { ...params }, "POST");
//生产设备
export const product_equipment_page = (params) =>fetch("/information-v2/ioc/product-equipment-page", { ...params }, "POST");
//特种设备
export const special_equipment_page = (params) =>fetch("/information-v2/ioc/special-equipment-page", { ...params }, "POST");
//消防设备
export const firefight_equipment_page = (params) =>fetch("/information-v2/ioc/firefight-equipment-page", { ...params }, "POST");
//工艺
export const craft_page = (params) =>fetch("/information-v2/ioc/craft-page", { ...params }, "POST");
//仓库
export const entrepo_page = (params) =>fetch("/information-v2/ioc/entrepo-page", { ...params }, "POST");
//罐区
export const tank_farm_page = (params) =>fetch("/information-v2/ioc/tank-farm-page", { ...params }, "POST");
//储罐
export const storage_tank_page= (params) =>fetch("/information-v2/ioc/storage-tank-page", { ...params }, "POST");
//原辅料
export const raw_material_page= (params) =>fetch("/information-v2/ioc/raw-material-page", { ...params }, "POST");
//产品
export const product_page= (params) =>fetch("/information-v2/ioc/product-page", { ...params }, "POST");
//产能耗
export const energy_data_report_page= (params) =>fetch("/information-v2/ioc/energy-data-report-page", { ...params }, "GET");
//周边敏感信息
export const sensitive_target_page= (params) =>fetch("/information-v2/ioc/sensitive-target-page", { ...params }, "POST");
//固废
export const solid_waste_page= (params) =>fetch("/information-v2/ioc/solid-waste-page", { ...params }, "POST");
//废水
export const waste_water_page= (params) =>fetch("/information-v2/ioc/waste-water-page", { ...params }, "POST");
//废气
export const fluegas_page= (params) =>fetch("/information-v2/ioc/fluegas-page", { ...params }, "POST");
//用电量-实时
export const equipment_data_current= (params) =>fetch("/information-v2/ioc/equipmentData-current", { ...params }, "GET");
//用电量-历史
export const equipment_data_history= (params) =>fetch("/information-v2/ioc/equipmentData-history", { ...params }, "GET");
//企业图库
export const page_photo_album= (params) =>fetch("/information-v2/ioc/pagePhotoAlbum", { ...params }, "POST");
//企业相册---需要token
export const page_photos= (params) =>fetch("/information-v2/enterprise-photos/pagePhotos", { ...params }, "POST");

