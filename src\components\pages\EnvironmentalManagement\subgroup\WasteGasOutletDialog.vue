<template>
<div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">废气详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    
                   <div class="container">
            <div class="title-detail">
                区域名称：{{ deviceDetail.area_name }}
            </div>
            <div class="title-detail">
                设备名称：{{ deviceDetail.equipment_name }}
            </div>
            <div class="top-selects">
                                <div class="slect-tabs" ref="tabsRef">
                                    <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="selectType(item.value)">
                                        <div class="tab-item-text" :class="{'active-tab-item':currentTab === item.value}" >{{ item.label }}</div>
                                        <div class="tab-item-line" v-if="currentTab === item.value"></div>
                                    </div>
                                </div>
                            </div>
            
            <!-- <div class="separate"></div> -->

            <div class="center" v-if="currentTab != 3">
                <div
                    class="left-arrow"
                    @click="prev()"
                    :class="{ hide: startIndex <= 0 }"
                ></div>
                <div class="center-content">
                    <transition-group
                        name="list-animate"
                        tag="div"
                        style="display: flex; justify-content: space-between"
                    >
                        <div
                            class="event-btn-wrap list-animate-item"
                            :class="{ selectActive: selectValue == item.id }"
                            v-for="item in showList"
                            :key="item.id"
                            style="cursor: pointer"
                            @click="clickSelect(item)"
                        >
                            <span
                                v-if="
                                    item.factor_unit != null &&
                                    item.factor_unit != ''
                                "
                                >{{ item.factor_name }}({{
                                    item.factor_unit
                                }})</span
                            >
                            <span v-else>{{ item.factor_name }}</span>
                        </div>
                    </transition-group>
                </div>
                <div
                    class="right-arrow"
                    @click="next()"
                    :class="{ hide: startIndex + limit >= list.length }"
                ></div>
            </div>
            <div class="bottom1 alarm" v-if="currentTab === 1">
                <el-table
                    class="tablebox"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="num"
                        label="序号"
                        style="width: 5%"
                    />
                    <el-table-column
                        prop="chemical_name"
                        label="监测因子"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="monitor_value"
                        label="采集值"
                        show-overflow-tooltip
                    />
                    <!-- <el-table-column prop="cas_num" label="cas号" show-overflow-tooltip /> -->
                    <el-table-column
                        prop="range"
                        label="标准范围"
                        show-overflow-tooltip
                    />
                    <!-- <el-table-column
            prop="physical_statue"
            label="采样流量"
            show-overflow-tooltip
          />
          <el-table-column
            prop="physical_statue"
            label="采样频率"
            show-overflow-tooltip
          /> -->
                    <el-table-column
                        prop="acquisition_time"
                        label="采集时间"
                        show-overflow-tooltip
                    />
                </el-table>
                <el-pagination
                    class="pagination"
                    background
                    layout="->,total, prev, pager, next"
                    :total="totalSecond"
                    v-model:currentPage="pageNumSecond"
                    :page-size="pageSizeSecond"
                    @current-change="handleCurrentChangeSecond"
                />
            </div>
            <div class="bottom2" v-show="currentTab == 2">
                <div class="linewrap" ref="linewrap" v-show="!showText"></div>
                <div v-show="showText" class="no-text">暂无数据</div>
            </div>
            <div class="alarm" v-if="currentTab === 3">
                <el-table
                    class="tablebox"
                    :data="tableAlarm"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="num"
                        label="序号"
                        style="width: 5%"
                    />
                    <el-table-column
                        prop="enterpriseName"
                        label="企业名称"
                        width="100"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="tabFlag"
                        label="报警类型"
                        width="100"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="alarmTime"
                        label="报警时间"
                        width="100"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="content"
                        label="报警信息"
                        width="100"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="acquisitionValue"
                        label="采集数值"
                        width="100"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="standardRange"
                        label="标准值"
                        width="100"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="processState"
                        label="状态"
                        width="100"
                        show-overflow-tooltip
                    />
                </el-table>
                <el-pagination
                    class="pagination"
                    background
                    v-model:currentPage="pageNum"
                    :page-size="pageSize"
                    @current-change="handleCurrentChange"
                    layout="->,total, prev, pager, next"
                    :total="total"
                />
            </div>
        </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
    
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    computed,
    toRefs,
} from "vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    waste_indicator,
    waste_three_data,
    threshold_gas,
    area_equipment,
    threshold_gas_line,
} from "../../../../assets/js/api/environmentalManagement";
import { alarmRecordSelectPage } from "../../../../assets/js/api/closedManagement";
const size = ref(1);
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const clickId = ref();
const { pickId } = toRefs(props);
watch(
    pickId,
    (a, b) => {
        console.log(a, b);
        clickId.value = a;
    },
    {
        immediate: true,
    },
);
const list = ref([]);
let startIndex = ref(0);
const selectValue = ref(1);
const selectUnit = ref("");
const limit = ref(6);
const next = () => {
    if (startIndex.value + limit.value < list.value.length) {
        startIndex.value += 1;
    }
};
const prev = () => {
    if (startIndex.value > 0) {
        startIndex.value -= 1;
    }

    //   else {
    //     startIndex.value = list.value.length - 1;
    //   }
};
//获取红线
const searchThresholdLine = () => {
    limitData.value = [];
    threshold_gas_line({
        equipment_id: clickId.value,
        factor_id: selectValue.value,
    }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            res.data.data.forEach((item) => {
                if (item.threshold2 != null && item.threshold2 != undefined) {
                    allMax =
                        allMax > Number(item.threshold2)
                            ? allMax
                            : Number(item.threshold2);
                    allMin =
                        allMin < Number(item.threshold2)
                            ? allMin
                            : Number(item.threshold2);
                    let obj = { yAxis: Number(item.threshold2) };
                    limitData.value.push(obj);
                }
            });
            console.log(limitData.value);
            searchAnalysis();
        } else {
            searchAnalysis();
        }
    });
};

const clickSelect = (x) => {
    selectValue.value = x.id;
    selectUnit.value = x.factor_unit;
    pageNumSecond.value = 1;
    if (currentTab.value == 1) {
        searchWasteData();
    } else {
        // searchAnalysis();
        searchThresholdLine();
    }
};
const totalSecond = ref(0);
const pageNumSecond = ref(1);
const pageSizeSecond = ref(10);
const handleCurrentChangeSecond = (e) => {
    pageNumSecond.value = e;
    searchSpecificData();
};
const emit = defineEmits(["closeDialog"]);
// const typeActive = ref(1);
//报警信息分页
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableAlarm = ref([]);
const handleCurrentChange = (val) => {
    console.log("报警当前页", val);
    pageNum.value = val;
    searchAlarmData();
};
//获取报警信息
const searchAlarmData = () => {
    tableAlarm.value = [];
    alarmRecordSelectPage({
        tag: "环保废气质量告警",
        equipmentId: pickId.value,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    }).then((res) => {
        if (res.data.data) {
            total.value = Number(res.data.data.total);
            var page = (pageNum.value - 1) * pageSize.value;
            if (res.data.data.list.length > 0) {
                res.data.data.list.forEach((item, index) => {
                    Reflect.set(item, "num", page + index + 1);
                    tableAlarm.value.push(item);
                });
            }
            console.log(res.data.data.list, "res.data.data.list");
            tableAlarm.value = res.data.data.list;
        }
    });
};
//顶部tab
let topTabs = ref([
    {
        value:1,
        label:'实时监测'
    },
    {
        value:2,
        label:'统计分析'
    },
    {
        value:3,
        label:'报警信息'
    }
]);
let currentTab = ref(1);
const tabsRef = ref(null);
const selectType = (type) => {
        currentTab.value = type;
    // size.value = type;
    if (type == 2) {
        // searchAnalysis();
        searchThresholdLine();
        // lineChart = echarts.init(linewrap.value);
        // initChart();
        // lineChart.setOption(option);
        // window.addEventListener("resize", () => {
        //   lineChart.resize();
        // });
    } else if (type == 1) {
        pageNumSecond.value = 1;
        searchWasteData();
    } else {
        searchAlarmData();
    }
};
const showList = computed(() => {
    let computedList = [];
    if (startIndex.value + limit.value > list.value.length) {
        computedList = list.value.slice(startIndex.value, list.value.length);
    } else {
        computedList = list.value.slice(
            startIndex.value,
            startIndex.value + limit.value,
        );
    }

    return computedList;
});
const xData = ref([]);
const yData = ref([]);
const limitData = ref([]);
let allMax = 0;
let allMin = 0;

const totalData = ref([]);
const tableData = ref([]);
let option;
let linewrap = ref(null);
let lineChart;
const initChart = () => {
    option = {
        xAxis: {
            type: "category",
            data: xData.value,
            axisLine: {
                //  改变x轴颜色
                lineStyle: {
                    color: "#1AB2FF",
                },
            },
            axisLabel: {
                //  改变x轴字体颜色和大小
                textStyle: {
                    color: "#fff",
                    fontSize: 14,
                    lineHeight: 22,
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ["rgba(255, 255, 255, 0.15)"],
                    width: 1,
                    type: "solid",
                },
            },
        },
        yAxis: {
            type: "value",
            name: selectUnit.value,
            max: (extent) => (extent.max > allMax ? extent.max : allMax),
            min: (extent) => (extent.min < allMin ? extent.min : allMin),
            axisLine: {
                //  改变y轴颜色
                show: false,
                lineStyle: {
                    color: "#26D9FF",
                },
            },
            axisLabel: {
                //  改变y轴字体颜色和大小
                //formatter: '{value} m³ ', //  给y轴添加单位
                textStyle: {
                    color: "#fff",
                    fontSize: 14,
                    lineHeight: 22,
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ["#315070"],
                    width: 1,
                    type: "solid",
                },
            },
        },
        grid: {
            left: 10,
            right: 10,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        series: [
            {
                markLine: {
                    symbol: "none",
                    show: false,
                    label: {
                        normal: {
                            show: false,
                            color: "#fff",
                            backgroundColor: "rgba(228,0,54,70)",
                            fontSize: 16,
                            padding: 4,
                            borderRadius: 4,
                            show: true,
                            position: "start",
                            distance: 4,
                        },
                    },
                    lineStyle: {
                        type: "dotted",
                        color: "#FF4C4D",
                        width: 1,
                    },
                    data: limitData.value,
                },

                smooth: true,
                symbol: "circle",
                symbolSize: 7,
                markPoint: {
                    symbol: "circle",
                },
                data: yData.value,
                type: "line",
                itemStyle: {
                    normal: {
                        label: {
                            show: true,
                            color: "#fff",
                            fontSize: 12,
                        },
                        color: "#294E8F",
                        borderColor: "3D7EEB",
                        borderWidth: 2,
                    },
                },
                lineStyle: {
                    normal: {
                        width: 2,
                        color: "#1AB2FF",
                        // shadowColor: "#3D7EEB",
                        // shadowBlur: 10
                    },
                },
                areaStyle: {
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0.5,
                                color: "rgba(26, 178, 255, 0.30)", // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: "rgba(26, 178, 255, 0.00)", // 100% 处的颜色
                            },
                        ],
                        global: false, // 缺省为 false
                    },
                },
            },
        ],
    };
};
const closeDialog = () => {
    console.log("wastegas");
    emit("closeDialog", "wastegas");
};
var fanwei = "";
//根据因子查表格数据
const searchWasteData = () => {
    fanwei = "";
    totalData.value = [];
    threshold_gas({
        equipment_id: clickId.value,
        factor_id: selectValue.value,
    }).then((re) => {
        if (re.data.success && re.data.data && re.data.data.length) {
            if (
                (re.data.data[0].threshold1 == null ||
                    re.data.data[0].threshold1 == "") &&
                re.data.data[0].threshold2 != null &&
                re.data.data[0].threshold2 != ""
            ) {
                fanwei = "≤" + re.data.data[0].threshold2;
            } else if (
                (re.data.data[0].threshold2 == null ||
                    re.data.data[0].threshold2 == "") &&
                re.data.data[0].threshold1 != null &&
                re.data.data[0].threshold1 != ""
            ) {
                fanwei = "≥" + re.data.data[0].threshold1;
            } else if (
                re.data.data[0].threshold1 != null &&
                re.data.data[0].threshold1 != "" &&
                re.data.data[0].threshold2 != null &&
                re.data.data[0].threshold2 != ""
            ) {
                fanwei =
                    re.data.data[0].threshold1 +
                    "-" +
                    re.data.data[0].threshold2;
            }
            console.log(fanwei);
        }
        waste_three_data({
            equipment_id: clickId.value,
            factor_id: selectValue.value,
        }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    Reflect.set(item, "range", fanwei);

                    Reflect.set(
                        item,
                        "chemical_name",
                        list.value.find((x) => x.id == item.factor_id)
                            .factor_name,
                    );
                    totalData.value.push(item);
                });
                totalSecond.value = res.data.data.length;
                //获取分页数据
                //   tableData.value = res.data.data;
                searchSpecificData();
            }
        });
    });
};

const searchSpecificData = () => {
    tableData.value = [];
    for (let i = 0; i < pageSizeSecond.value; i++) {
        let pageIndex = (pageNumSecond.value - 1) * pageSizeSecond.value + i;
        console.log(pageIndex);
        if (pageIndex < totalSecond.value) {
            tableData.value[i] =
                totalData.value[
                    (pageNumSecond.value - 1) * pageSizeSecond.value + i
                ];
        }
    }
};
const showText = ref(true);
//查统计分析
const searchAnalysis = () => {
    xData.value = [];
    yData.value = [];
    if (lineChart) {
        lineChart.dispose();
    }
    waste_three_data({
        equipment_id: clickId.value,
        factor_id: selectValue.value,
    }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            showText.value = false;
            res.data.data.forEach((item, index) => {
                xData.value.push(item.acquisition_time);
                yData.value.push(item.monitor_value);
            });
            lineChart = echarts.init(linewrap.value);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        } else {
            showText.value = true;
        }
    });
};

const deviceDetail = ref({ area_name: "", equipment_name: "" });
//监测站信息
const deviceDetails = () => {
    area_equipment({ equipment_id: clickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            deviceDetail.value = res.data.data[0];
        }
    });
};
onMounted(() => {
    deviceDetails();
    waste_indicator({ equipment_type: 106006 }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            console.log(res.data.data);
            list.value = res.data.data;
            selectValue.value = res.data.data[0].id;
            selectUnit.value = res.data.data[0].factor_unit;
            searchWasteData();
        }
    });
});
onBeforeUnmount(() => {
    if (lineChart) {
        lineChart.dispose();
    }
});
</script>
<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 732px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 732px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 636px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.container {
  
    .title-detail {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 26px; /* 150% */
        // margin-left: 32px;
    }
    .separate {
        width: 792px;
        height: 2px;
        background: url("../../../../assets/images/dialog/separate.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
     .top-selects{
            width: 8000px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 800px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
    .center {
        width: 615px;
        height: 22px;
        margin: 16px auto 12px;
        // background-color: antiquewhite;
        display: flex;
        justify-content: space-between;
        .left-arrow {
            width: 16px;
            height: 16px;
            margin: auto 0;
            background: url("../../../../assets/images/left-arrow1.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }
        .center-content {
            width: 551px;
            height: 22px;
            //   background-color: aqua;
            .event-btn-wrap {
                width: 87px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
            }
            .selectActive {
                color: #47ebeb;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
            }
        }
        .right-arrow {
            width: 16px;
            height: 16px;
            margin: auto 0;
            background: url("../../../../assets/images/right-arrow1.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }
    }
    .bottom1 {
        width: 792px;
        height: 472px;
        // background-color: aqua;
    }
    .bottom2 {
        width: 792px;
        height: 472px;
        // background-color: aqua;
        .linewrap {
            width: 792px;
            height: 472px;
        }
        .no-text {
            width: 792px;
            height: 472px;
            margin-top: 16px;
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 472px; /* 150% */
        }
    }
    .alarm {
        width: 792px;
        height: 472px;
        margin-top: 16px;

        .tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
    
    }

    .hide {
        visibility: hidden;
    }
}
</style>
