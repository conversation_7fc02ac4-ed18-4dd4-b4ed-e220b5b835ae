<template>
    <Card title="管网用途分类">
        <template v-slot>
            <div class="piewrap" ref="piewrap"></div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { danger_chemical } from "../../../../assets/js/api/safetySupervision";

const num = reactive({
    total: 0,
    normal: 0,
    abnormal: 0,
});
let option;
let piewrap = ref(null);
let pieChart;
let echartData = ref([]);
const initChart = () => {
    let color = [
        "#30ABE8",
        "#3FF",
        "#27F85D",
        "#FFF04C",
        "#FFB54C",
        "#EB4747",
        "#47EBEB",
        "#C0FFB3",
        "#FF791A",
        "#31ABE8",
    ];

    let formatNumber = function (num) {
        let reg = /(?=(\B)(\d{3})+$)/g;
        return num.toString().replace(reg, ",");
    };
    let total = echartData.value.reduce((a, b) => {
        return a + b.value * 1;
    }, 0);

    option = {
        color: color,
        tooltip: {
            trigger: "item",
        },
        grid: {
            top: "1px",
            right: "1px",
            bottom: "1px",
            left: "2px",
        },
        legend: {
            type: "scroll",
            orient: "vertical",
            icon: "rect",
            x: "80%",
            y: "bottom",
            itemWidth: 12,
            itemHeight: 12,
            align: "left",
            height: 150,
            textStyle: {
                rich: {
                    name: {
                        fontSize: 14,
                        // padding: [0, 10, 0, 4],
                        fontWeight: 400,
                        color: "#ffffff",
                        lineHeight: 22,
                    },
                },
            },
            formatter: function (name) {
                let res = echartData.value.filter((v) => v.name === name);
                res = res[0] || {};
                return "{name|" + name + "}";
            },
            // data: legendName
        },
        series: [
            {
                type: "pie",
                radius: ["35%", "57%"],
                center: ["45%", "50%"],
                data: echartData.value,
                hoverAnimation: false,
                itemStyle: {
                    // normal: {
                    //     borderColor: '#fff',
                    //     borderWidth: 2
                    // }
                },
                labelLine: {
                    normal: {
                        length: 20,
                        length2: 30,
                        // lineStyle: {
                        //     color: '#e6e6e6'
                        // }
                    },
                },
                label: {
                    normal: {
                        formatter: (params) => {
                            console.log(params);
                            return (
                                "{value|" +
                                params.percent +
                                "%\n}{name|" +
                                formatNumber(params.value) +
                                "个}"
                            );
                        },
                        // padding: [0 , -100, 25, -100],
                        rich: {
                            name: {
                                fontSize: 12,
                                // padding: [0, 10, 0, 4],
                                fontWeight: 400,
                                color: "#ffffff",
                                lineHeight: 18,
                            },
                            value: {
                                fontSize: 20,
                                fontWeight: 700,
                                color: "#ffffff",
                                lineHeight: 28,
                            },
                        },
                    },
                },
            },
        ],
    };
};
onMounted(() => {
    pieChart = echarts.init(piewrap.value);
    echartData.value = [
    {
      name: "燃气管道",
      value: 11,
      percent: "25%",
    },
    {
      name: "污水管道",
      value: 8,
      percent: "18%",
    },
    {
      name: "热力管道",
      value: 11,
      percent: "25%",
    },
    {
      name: "雨水管道",
      value: 7,
      percent: "16%",
    },
    {
      name: "中水管道",
      value: 7,
      percent: "16%",
    },
    ];
    initChart();
    pieChart.setOption(option);
    window.addEventListener("resize", () => {
        pieChart.resize();
    });
    // danger_chemical({"":""}).then((res)=>{
    //   if (res.data.success && res.data.data && res.data.data.length) {
    //     echartData.value = res.data.data.map(x => {
    //       return {
    //         name: x.chemical_name,
    //         value: x.count
    //       }
    //     })
    //     console.log(echartData.value);
    //     initChart();
    //     pieChart.setOption(option);
    //     window.addEventListener("resize", () => {
    //       pieChart.resize();
    //     });
    //   }
    // })
});
onBeforeUnmount(() => {
    // loopShowTooltip(pieChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (pieChart) {
        // setTimeout(() => {
        pieChart.dispose();
        // }, 5000)
    }
});
</script>
<style lang="less" scoped>
.piewrap {
    width: 416px;
    height: 224px;
    padding: 16px;
}
</style>
