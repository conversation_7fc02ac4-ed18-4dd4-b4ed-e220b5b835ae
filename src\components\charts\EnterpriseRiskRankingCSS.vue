<template>
  <div class="enterprise-risk-ranking">
    <div class="ranking-item" v-for="(item, index) in enterpriseData" :key="index">
      <div class="ranking-number" :class="`number-${index + 1}`">
        {{ formatNumber(index + 1) }}
      </div>
      <div class="ranking-info">
        <div class="top-info">
          <div class="enterprise-name">{{ item.name }}</div>
          <div><span class="ranking-value">{{ item.value }}</span>次</div>
        </div>
        <div class="progress-container">
          <!-- 使用绝对定位的分段容器 -->
          <div class="segments-container">
            <!-- 生成所有分段，包括暗色和亮色部分 -->
            <div
              v-for="n in 50"
              :key="n"
              class="segment-wrapper"
              :style="{
                left: `${(n-1) * (100/50)}%`,
                width: `${100/50 - 0.4}%` // 减去一点宽度作为间隔
              }"
            >
              <!-- 暗色背景分段 -->
              <div
                class="segment dark"
                :style="{ backgroundColor: getBackgroundColor(item.color[0]) }"
              ></div>

              <!-- 亮色分段，只在当前分段小于或等于计算出的分段数时显示 -->
              <div
                v-if="n <= calculateSegmentCount(item.value, maxValue)"
                class="segment light"
                :style="{ backgroundColor: item.color[0] }"
              ></div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 企业数据
const enterpriseData = [
  { name: '企业名称1', value: 1234, color: ['rgba(77, 203, 98, 1)', 'rgba(35, 118, 49, 1)'] }, // 绿色
  { name: '企业名称1', value: 1034, color: ['rgba(48, 171, 232, 1)', 'rgba(15, 97, 138, 1)'] }, // 蓝色
  { name: '企业名称1', value: 1134, color: ['rgba(153, 178, 255, 1)', 'rgba(92, 122, 214, 1)'] }, // 紫色
  { name: '企业名称1', value: 1034, color: ['rgba(255, 198, 26, 1)', 'rgba(153, 115, 0, 1)'] }, // 黄色
  { name: '企业名称1', value: 934, color: ['rgba(255, 121, 26, 1)', 'rgba(153, 63, 0, 1)'] }, // 橙色
];

// 根据主色调获取对应的半透明背景色
const getBackgroundColor = (color) => {
  // 定义颜色映射关系 - 使用实际的颜色值作为键
  const colorMap = {
    'rgba(77, 203, 98, 1)': 'rgba(77, 203, 98, 0.3)',   // 绿色
    'rgba(48, 171, 232, 1)': 'rgba(48, 171, 232, 0.3)',  // 蓝色
    'rgba(153, 178, 255, 1)': 'rgba(153, 178, 255, 0.3)', // 紫色
    'rgba(255, 198, 26, 1)': 'rgba(255, 198, 26, 0.3)',  // 黄色
    'rgba(255, 121, 26, 1)': 'rgba(255, 121, 26, 0.3)'   // 橙色
  };

  return colorMap[color] || 'rgba(50, 50, 50, 0.3)';
};

// 计算最大值用于百分比显示
const maxValue = computed(() => {
  return Math.max(...enterpriseData.map(item => item.value));
});

// 格式化序号，保证两位数
const formatNumber = (num) => {
  return num < 10 ? `0${num}` : num;
};

// 计算应该显示多少个亮色分段
const calculateSegmentCount = (value, maxValue) => {
  // 计算原始百分比
  const rawPercentage = (value / maxValue) * 100;

  // 总共有50个分段
  const totalSegments = 50;

  // 计算应该显示多少个亮色分段（使用Math.round进行四舍五入）
  return Math.round(rawPercentage / 100 * totalSegments);
};

// 保留这个函数以兼容现有代码，但不再使用
const calculateRoundedPercentage = (value, maxValue) => {
  // 计算原始百分比
  const rawPercentage = (value / maxValue) * 100;
  return rawPercentage;
};
</script>

<style scoped lang="less">
.enterprise-risk-ranking {
  width: 416px;
    height: 224px;
  display: flex;
  flex-direction: column;
  gap: 8.5px;
  .ranking-item {

  width: 416px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .ranking-number {
  width: 24px;
height: 24px;
border-radius: 4px;
font-family: Noto Sans SC;
font-weight: 700;
font-size: 14px;
line-height: 22px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1);

}

/* 不同序号的背景颜色 */
.number-1 {
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(77, 203, 98, 0) 0%, rgba(77, 203, 98, 0.6) 100%);
    border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
      /* 使用渐变作为边框图像 */
      border-image-source: linear-gradient(180deg, rgba(77, 203, 98, 0) 16.27%, rgba(77, 203, 98, 1) 100%);
    border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
    border-image-width: 1px;
}

.number-2 {
 border-radius: 4px;
    background: linear-gradient(180deg, rgba(48, 171, 232, 0) 0%, rgba(48, 171, 232, 0.6) 100%);
    border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
      /* 使用渐变作为边框图像 */
      border-image-source: linear-gradient(180deg, rgba(48, 171, 232, 0) 16.27%, rgba(48, 171, 232, 1) 100%);
    border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
    border-image-width: 1px;
}

.number-3 {
  border-radius: 4px;
    background: linear-gradient(180deg, rgba(153, 178, 255, 0) 0%, rgba(153, 178, 255, 0.6) 100%);
    border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
      /* 使用渐变作为边框图像 */
      border-image-source: linear-gradient(180deg, rgba(153, 178, 255, 0) 16.27%, rgba(153, 178, 255, 1) 100%);
    border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
    border-image-width: 1px;
}

.number-4 {
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(255, 198, 26, 0) 0%, rgba(255, 198, 26, 0.6) 100%);
    border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
      /* 使用渐变作为边框图像 */
      border-image-source: linear-gradient(180deg, rgba(255, 198, 26, 0) 16.27%, rgba(255, 198, 26, 1) 100%);
    border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
    border-image-width: 1px;
}

.number-5 {
  border-radius: 4px;
    background: linear-gradient(180deg, rgba(255, 121, 26, 0) 0%, rgba(255, 121, 26, 0.6) 100%);
    border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
      /* 使用渐变作为边框图像 */
      border-image-source: linear-gradient(180deg, rgba(255, 121, 26, 0) 16.27%, rgba(255, 121, 26, 1) 100%);
    border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
    border-image-width: 1px;
}

.ranking-info {
  width: 384px;
  height: 38px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .top-info{
    width: 384px;
  height: 22px;
  display: flex;
  justify-content: space-between;
  font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
letter-spacing: 0%;
color: rgba(255, 255, 255, 1);
  }
}

.enterprise-name {
  font-weight: 400;
}

.progress-container {
  width: 366px;
  height: 12px;
  overflow: hidden;
  position: relative;
}

/* 分段容器，使用绝对定位 */
.segments-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 每个分段的包装器，使用绝对定位 */
.segment-wrapper {
  position: absolute;
  top: 0;
  height: 100%;
  border-radius: 1px; /* 添加圆角 */
  overflow: hidden; /* 确保内部元素不超出圆角 */
}

/* 分段样式 */
.segment {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 1px; /* 添加圆角 */
}

/* 暗色分段 */
.segment.dark {
  z-index: 1;
}

/* 亮色分段 */
.segment.light {
  z-index: 2;
}

.ranking-value {
  font-family: Noto Sans SC;
font-weight: 700;
font-size: 14px;
line-height: 22px;
letter-spacing: 0%;

  text-align: right;
}

}
}




</style>
