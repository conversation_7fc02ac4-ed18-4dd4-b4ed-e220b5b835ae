<template>
    <Card title="风险等级">
        <template v-slot>
            <div class="risk-level-wrapper">
                <div class="risk-level-item" v-for="(item,index) in riskLevelList" :key="index" style="cursor: pointer;" @click="openDialog('riskObjectDialogShow',item.risk_level)"> 
                    <img  class="risk-level-img" :src="item.img"/>
                    <div class="risk-num">
                        <div class="inner-name">{{item.risk_level}}</div>
                        <div class="inner-num">{{item.riskNum}}个</div>
                    </div>
                    <div class="enterprise-num">
                        <div class="inner-name">涉及企业</div>
                        <div class="inner-num">{{item.companyNum}}个</div>
                    </div>
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import img1 from '@/assets/newImages/DoublePrevention/risk-level-img-1.svg';
import img2 from '@/assets/newImages/DoublePrevention/risk-level-img-2.svg';
import img3 from '@/assets/newImages/DoublePrevention/risk-level-img-3.svg';
import img4 from '@/assets/newImages/DoublePrevention/risk-level-img-4.svg';
import Card from "@/components/commenNew/Card.vue"
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance  } from "vue";
import {riskLevelCount} from '@/assets/js/api/doublePrevention.js';
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val,val2) => {
    
    proxy.$emit('openDialog',val,val2);
}
const riskLevelList = ref([
    {
        risk_level:'重大风险',
        riskNum:0,
        companyNum:0,
        img:img1  
    },
    {   
        risk_level:'较大风险',
        riskNum:0,
        companyNum:0,
        img:img2
    },
    {
        risk_level:'一般风险',
        riskNum:0,
        companyNum:0,
        img:img3
    },
    {
        risk_level:'低风险',
        riskNum:0,
        companyNum:0,
        img:img4
    }
]);
const getData = () => {
    riskLevelCount().then(res => {
        console.log(res,'res');
        if(res.data&&res.data.data&&res.data.data.length>0){
            let result = res.data.data;
            result.forEach(apiItem => {
                const localItem = riskLevelList.value.find(item => 
                    item.risk_level === apiItem.risk_level
                );
                console.log(localItem,'localItem');
                if (localItem) {
                    localItem.riskNum = apiItem.riskNum;
                    localItem.companyNum = apiItem.companyNum;
                }
            });
        }
    })
}
onMounted(() => {
    getData();
})
</script>

<style lang="less" scoped>

    .risk-level-wrapper{
        width: 416px;
    height: 224px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .risk-level-item{
        width: 336px;
        padding-left: 40px;
        padding-right: 40px;
height: 48px;
background: url("@/assets/newImages/DoublePrevention/risk-level.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
.risk-level-img{
    width: 24px;
height: 28px;

}

.risk-num{
    width: 149px;
height: 28px;
display: flex;
gap: 16px;
margin-left: 24px;

}
.enterprise-num{
    width: 115px;
height: 28px;
display: flex;
gap: 16px;
margin-left: 24px;
}
.inner-name{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 28px;
text-align: left;
color:rgba(255, 255, 255, 1);
}
.inner-num{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 20px;
line-height: 28px;
text-align: left;
color:rgba(128, 234, 255, 1);
}
    }
   
    }
</style>
