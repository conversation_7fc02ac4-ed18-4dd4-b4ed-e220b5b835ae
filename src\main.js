import { createApp } from "vue";
import App from "./App.vue";
import router from "./router/index";
import ElementPlus from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import Vue3SeamlessScroll from "vue3-seamless-scroll";
import VueClipboard from "vue3-clipboard";
import aegis from "./utils/aegis";
import mitt from "mitt";
import { createPinia } from "pinia";
import "element-plus/dist/index.css";
import "./less/index.less";
import "./less/table.less";
import "./assets/font/font.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import * as Cesium from "cesium";
// import Loading  from './components/loading/types/type.ts';
import Loading1 from "../src/components/loading/loading.vue";
// import transitionUrl from './utils/transitionUrl'
// import * as CesiumHeatmap from 'cesium-heatmap'
// import CesiumHeatmap from "Cesium-heatmap";
// import AMapUI from 'AMapUI'
const bus = mitt();
import { createVNode, render } from "vue";
const app = createApp(App);
// app.use(transitionUrl)
app.provide("$bus", bus);
app.provide("$aegis", aegis);
app.use(createPinia());
// app.use(Loading);
// app.config.globalProperties.$bus = bus;
app.config.globalProperties.$aegis = aegis;
app.config.globalProperties.$bus = mitt();
app.use(router)
    .use(ElementPlus, { locale: zhCn })
    .use(Vue3SeamlessScroll)
    .use(Cesium);
app.mount("#app");
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}
app.config.globalProperties.$changeUrl = function (url) {
    console.log("changeUrl");
    let font = window.location.protocol;
    let host = window.location.hostname;
    if (url.indexOf("city189.cn") != -1) {
        return url;
        // return url
    } else if (
        window.location.hostname == "***********" &&
        url.indexOf(".live.flv") != -1
    ) {
        if (url.indexOf("ws://") != -1) {
            let newUrl =
                "ws://***********:1506/" +
                url.split("/").splice(3, url.split("/").length).join("/");
            return newUrl;
        } else {
            let newUrl =
                font +
                "//***********:1506/" +
                url.split("/").splice(3, url.split("/").length).join("/");
            return newUrl;
        }
    } else if (
        window.location.hostname == "**************" &&
        url.indexOf(".live.flv") != -1
    ) {
        if (url.indexOf("ws://") != -1) {
            let newUrl =
                "ws://" +
                host +
                ":11004/" +
                url.split("/").splice(3, url.split("/").length).join("/");
            return newUrl;
        } else {
            let newUrl =
                font +
                "//" +
                host +
                ":11004/" +
                url.split("/").splice(3, url.split("/").length).join("/");
            return newUrl;
        }
    } else if (
        window.location.hostname == "*************" &&
        url.indexOf(".live.flv") != -1
    ) {
        if (url.indexOf("ws://") != -1) {
            let newUrl =
                "ws://" +
                host +
                ":11004/" +
                url.split("/").splice(3, url.split("/").length).join("/");
            return newUrl;
        } else {
            let newUrl =
                font +
                "//" +
                host +
                ":11004/" +
                url.split("/").splice(3, url.split("/").length).join("/");
            return newUrl;
        }
    } else {
        // let newUrl =
        //     font +
        //     "//" +
        //     host +
        //     ":10000/" +
        //     url.split("/").splice(3, url.split("/").length).join("/");
        let newUrl = font + '//' + '*************' + ':11004/' + url.split('/').splice(3, url.split('/').length).join('/')
        return newUrl;

    }
};
app.config.globalProperties.$transformGCJ02ToWGS84 = function (gcjLat, gcjLng) {
  const PI = 3.14159265358979324
  const ee = 0.00669342162296594323
  const a = 6378245.0
  
  const transform = (lat, lng) => {
    let dLat = transformLat(lng - 105.0, lat - 35.0)
    let dLng = transformLng(lng - 105.0, lat - 35.0)
    const radLat = lat / 180.0 * PI
    let magic = Math.sin(radLat)
    magic = 1 - ee * magic * magic
    const sqrtMagic = Math.sqrt(magic)
    dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * PI)
    dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * PI)
    const wgsLat = lat - dLat
    const wgsLng = lng - dLng
    return { lat: wgsLat, lng: wgsLng }
  }
  
  const transformLat = (x, y) => {
    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x))
    ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0
    ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0
    ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0
    return ret
  }
  
  const transformLng = (x, y) => {
    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x))
    ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0
    ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0
    ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0
    return ret
  }
  
  return transform(gcjLat, gcjLng)
}

const vNode = createVNode(Loading1); //转成vnode
render(vNode, document.body); //转成真实dom，第二个参数是挂载在哪里
app.config.globalProperties.$loading = {
    // 全局挂载 如果报红,请使用declare 声明文件
    show: vNode.component?.exposed?.show,
    hide: vNode.component?.exposed?.hide,
};
