const path = require('path')
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import cesium from "vite-plugin-cesium";
// https://vitejs.dev/config/
export default defineConfig({
    plugins: [vue(), cesium()],
    resolve: {
        alias: {
          "@": path.resolve(__dirname, './src')
        }
      },
    server: {
        host: "0.0.0.0",
        build: {
            sourcemap: true,
        },
        proxy: {
            "/api-v2/": {
                // target: 'http://**************:8087',
                // target: "http://*************:13005",
                target: "https://city189.cn:2927",
                // target: "http://**********:8187",//wdx

                // target: 'https://city189.cn:2964/',
                changeOrigin: true,
            },
            //安防两侧面板使用
            "/security/": {
                // target: 'http://**************:8087',
                // target: "http://*************:13005",
                // target: "http://**********:9802",
                // target: "http://**********:8187",//wdx

                target: 'https://city189.cn:2926/',
                changeOrigin: true,
            },
             //安全基础弹窗
             "/ioc/": {
                target: "https://city189.cn:2926",
                changeOrigin: true,
            },
            "/security-v2": {
                // target: 'http://**************:8087',
                // target: 'http://**************:9902',

                // target: 'https://city189.cn:2908/',
                // target: 'http://**************:32362',

                // target: "http://*************:13003",
                target: "https://city189.cn:2926",

                changeOrigin: true,
            },

            "/environment": {
                // target: 'http://**************:8087',
                // target: 'http://**************:9003/',
                // target: 'https://city189.cn:2961/',
                // target: 'http://*************:13003',
                // target: "https://city189.cn:2908/",
                target: "https://city189.cn:2926",

                // target: 'http://*************:13000',
                changeOrigin: true,
            },
            "/equipment": {
                // target: 'https://city189.cn:2961/',
                // target: 'https://city189.cn:2905/',
                // target: "http://*************:13003",
                target: "https://city189.cn:2926",

                // target: 'https://city189.cn:2908/',

                // target: 'http://*************:9904',
                changeOrigin: true,     
            },
            "/information-v2": {
                // target: 'https://city189.cn:2908/',
                // target: 'http://*************:13000',
                // target: "http://*************:13003/",
                target: "https://city189.cn:2926",

                // target: 'http://*************:9084',
                changeOrigin: true,
            },
            "/information": {
                // target: 'https://city189.cn:2959/',
                // target: 'http://*************:13000',
                // target: "https://city189.cn:2908/",
                target: "https://city189.cn:2926",

                // target: 'http://*************:9084',
                changeOrigin: true,
            },
           
            "/emergency-v2": {
                // target: 'https://city189.cn:2906/',
                // target: 'https://city189.cn:2908/',
                // target: "http://*************:13003",
                target: "https://city189.cn:2926",

                // target: 'http://*************:9084',
                changeOrigin: true,
            },
            "/emergency": {
                // target: 'https://city189.cn:2906/',
                // target: 'https://city189.cn:2959/',
                // target: "http://*************:13000",
                target: "https://city189.cn:2926",

                // target: 'http://*************:9084',
                changeOrigin: true,
            },
            "/schedule": {
                // target: 'https://city189.cn:2906/',
                // target: "http://*************:13000",
                target: "https://city189.cn:2926",


                changeOrigin: true,
            },
            "/identification": {
                // target: 'https://city189.cn:2906/',
                // target: "http://*************:13003",
                target: "https://city189.cn:2926",


                changeOrigin: true,
            },
            "/air": {
                target: "https://devapi.qweather.com/v7/",

                // target: 'http://*************:9904',
                changeOrigin: true,
            },
            "/safety-v2": {
                // target: 'https://city189.cn:2960',
                // target: "http://*************:13003",
                // target: "http://*********:8841",

                target: 'https://city189.cn:2926/',

                changeOrigin: true,
            },
            "/oauth": {
                target: "https://aip.baidubce.com",
                changeOrigin: true,
            },
            "/pro_api": {
                target: "http://vop.baidu.com",
                changeOrigin: true,
            },
            "/service-api": {
                target: "http://*************:10003",
                changeOrigin: true,
            },

            "/qa_text": {
                target: "http://**************:9000/",
                changeOrigin: true,
            },
            "/firecontrol": {
                // target: 'http://**************:8087',
                // target: 'http://**************:9902',

                // target: 'https://city189.cn:2959/',
                // target: 'http://**************:32362',

                // target: "http://*************:13003",
                target: "https://city189.cn:2926",


                changeOrigin: true,
            },
            "/light": {
                target: "http://*************:10021",
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/light/, ""),
            },
        },
    },
    define: {
        AMap: "AMap",
    },
});
