<template>
    <div class="footer">
        <div class="jump-btn">
            <div class="btn">
                <div
                    :class="['item-nav', active === 1 ? 'active-btn1' : 'btn1']"
                    @click="setPage(1)"
                ></div>
                <div class="tittle">综合监管</div>
            </div>
            <div class="btn">
                <div
                    :class="['item-nav', active === 9 ? 'active-btn9' : 'btn9']"
                    @click="setPage(9)"
                ></div>
                <div class="tittle">安全基础</div>
            </div>
           
            <div class="btn">
                <div
                    :class="['item-nav', active === 3 ? 'active-btn3' : 'btn3']"
                    @click="setPage(3)"
                ></div>
                <div class="tittle">重大危险源</div>
            </div>
            <div class="btn">
                <div
                    :class="['item-nav', active === 10 ? 'active-btn10' : 'btn10']"
                    @click="setPage(10)"
                ></div>
                <div class="tittle">双重预防</div>
            </div>
            <div class="btn">
                <div
                    :class="['item-nav', active === 11 ? 'active-btn11' : 'btn11']"
                    @click="setPage(11)"
                ></div>
                <div class="tittle">特殊作业</div>
            </div>
            <!-- <div class="btn">
        <div :class="['item-nav', active === 2 ? 'active-btn2' : 'btn2']" @click="setPage(2)"></div>
        <div class="tittle">园区档案</div>
      </div> -->
           
            <div class="btn">
                <div
                    :class="['item-nav', active === 5 ? 'active-btn5' : 'btn5']"
                    @click="setPage(5)"
                ></div>
                <div class="tittle">封闭化管理</div>
            </div>
            <div class="btn">
                <div
                    :class="['item-nav', active === 8 ? 'active-btn8' : 'btn8']"
                    @click="setPage(8)"
                ></div>
                <div class="tittle">敏捷应急</div>
            </div>
            <div class="btn">
                <div
                    :class="['item-nav', active === 4 ? 'active-btn4' : 'btn4']"
                    @click="setPage(4)"
                ></div>
                <div class="tittle">环保管理</div>
            </div>

            
            <div class="btn">
                <div
                    :class="['item-nav', active === 7 ? 'active-btn7' : 'btn7']"
                    @click="setPage(7)"
                ></div>
                <div class="tittle">智慧能源</div>
            </div>
          <div class="btn">
        <div :class="['item-nav', active === 6 ? 'active-btn6' : 'btn6']" @click="setPage(6)"></div>
        <div class="tittle">智慧管网</div>
      </div>
            
            
        </div>
        <div class="footer-bg"></div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    onUnmounted,
    nextTick,
} from "vue";
import * as Cesium from "cesium";
const { proxy } = getCurrentInstance();
const emit = defineEmits();
const active = ref(1);
const setPage = (type) => {
    proxy.$bus.emit("current_page", type);
    if (window.toggle == 2) window.viewer.entities.removeAll(); //删除所有
    if (window.toggle == 3 && active.value != type) window.map1.clearMap();
    if (window.mouseTool) {
        window.mouseTool.close(true);
    }
    active.value = type;

    emit("fetchPageType", type);
};
let obj = reactive({
    安全监管: 3,
    封闭管理: 5,
    环保管理: 4,
    应急指挥: 8,
    智慧消防: 8,
    综合监管: 1,
});
proxy.$bus.on("clicJump", (val) => {
    console.log(val);
    if (val.tabFlag != null && val.tabFlag != undefined) {
        let a = val.tabFlag;
        if (val.tabFlag == "安全监管" && val.tag != null) {
            if (val.tag.includes("重大危险源")) {
                a = "安全监管";
            } else {
                a = "综合监管";
            }
        }
        if (val.alarmObjectId != null || val.equipmentId != null) {
            setPage(obj[a]);
            nextTick(() => {
                proxy.$bus.emit("clicJump1", val);
            });
        }
    }
});
onMounted(() => {});
// onUnmounted(() => {
//     proxy.$bus.off("clicJump");

// })
</script>

<style lang="less" scoped>
.footer {
    .jump-btn {
        display: flex;
        position: absolute;
        bottom: 24px;
        left: 50%;
        transform: translateX(-50%);
        // z-index: 1000000002;
        z-index: 1001;

        > div:not(:last-child) {
            margin-right: 20px;
        }

        .btn {
            // width: 56px;
            height: 74px;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .tittle {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
            }

            .item-nav {
                width: 48px;
                height: 48px;
                // background-color: aqua;
            }

            .active-btn1 {
                background: url("../../assets/images/jump-btn/active-btn1.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn1 {
                background: url("../../assets/images/jump-btn/btn1.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn2 {
                background: url("../../assets/images/jump-btn/active-btn2.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn2 {
                background: url("../../assets/images/jump-btn/btn2.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn3 {
                background: url("../../assets/images/jump-btn/active-btn3.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn3 {
                background: url("../../assets/images/jump-btn/btn3.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn4 {
                background: url("../../assets/images/jump-btn/active-btn4.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn4 {
                background: url("../../assets/images/jump-btn/btn4.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn5 {
                background: url("../../assets/images/jump-btn/active-btn5.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn5 {
                background: url("../../assets/images/jump-btn/btn5.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn6 {
                background: url("../../assets/images/jump-btn/active-btn6.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn6 {
                background: url("../../assets/images/jump-btn/btn6.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn7 {
                background: url("../../assets/images/jump-btn/active-btn7.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn7 {
                background: url("../../assets/images/jump-btn/btn7.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .active-btn8 {
                background: url("../../assets/images/jump-btn/active-btn8.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn8 {
                background: url("../../assets/images/jump-btn/btn8.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .active-btn9 {
                background: url("../../assets/images/jump-btn/active-btn9.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn9 {
                background: url("../../assets/images/jump-btn/btn9.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .active-btn10 {
                background: url("../../assets/images/jump-btn/active-btn10.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn10 {
                background: url("../../assets/images/jump-btn/btn10.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
            .active-btn11 {
                background: url("../../assets/images/jump-btn/active-btn11.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }

            .btn11 {
                background: url("../../assets/images/jump-btn/btn11.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
            }
        }
    }

    .footer-bg {
        // width: 100%;
        // // height: 160px;
        // height: 50px;
        // // background: url('../../assets/images/header-masking.svg') no-repeat;
        // background: url("../../assets/images/footer-bg.svg") no-repeat;
        // // background: url("../../assets/images/header-bg.svg") no-repeat, linear-gradient(180deg, rgba(10, 37, 92, 0.45) 0%, rgba(10, 37, 92, 0.45) 71%, rgba(10, 37, 92, 0) 100%);
        // background-size: cover;
        // background-position: center;
        // // position: relative;
        // position: absolute;
        // bottom: 0;
        // left: 0;
        // // z-index: 1000000002;
        // z-index: 1001;
    }
}
</style>
