<template>
    <Card title="实时告警">
        <template v-slot>
    <div class="real-time-alarm">
        <!-- {{ alarmList.length }} -->
        <div v-if="showList == null || showList.length == 0">
            <div class="none-img"></div>
            <div class="text">暂无告警数据</div>
        </div>
        <div v-else>
            <div :class="showList.length > 8 ? 'inner first-marquee' : 'inner'">
                <table class="alarm-table" cellspacing="4" cellpadding="0">
                    <tr
                        class="row"
                        v-for="(item, index) in showList"
                        :key="index"
                    >
                        <td class="alarm-id-big"></td>
                        <td class="alarm-detail">
                            {{ showWords(item.content, 13) }}
                        </td>
                        <td class="alarm-date">{{ item.alarm_time }}</td>
                    </tr>
                </table>
            </div>
            <div v-if="showList.length > 8">
                <div
                    :class="
                        showList.length > 8 ? 'inner second-marquee' : 'inner'
                    "
                >
                    <table class="alarm-table" cellspacing="4" cellpadding="0">
                        <tr
                            class="row"
                            v-for="(item, index) in showList"
                            :key="index"
                        >
                            <td class="alarm-id-big"></td>
                            <td class="alarm-detail">
                                {{ showWords(item.content, 13) }}
                            </td>
                            <td class="alarm-date">{{ item.alarm_time }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    </template>
</Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    onUnmounted,
} from "vue";
import {
    security_alarms,
    unified_alarms,
} from "../../../../assets/js/api/comprehensiveSupervision";
const { proxy } = getCurrentInstance();

const alarmList = ref([]);
const showWords = (content, length) => {
    if (content.length > length) {
        return content.substr(0, length) + "...";
    } else {
        return content;
    }
};
const showList = ref([]);
onMounted(() => {
    // obtainData()
    // filterAlarm();
    // flickerAudioOption();
});
//ws连接
const flickerAudioOption = () => {
    let hostname = window.location.hostname;
    let socket = null;
    if (window.location.protocol === "http:") {
        const wsuri = `ws://${hostname}:32362/safety-v2/ws/alarmRecord`;
        socket = new WebSocket(wsuri);
    } else if (window.location.protocol === "https:") {
        socket = new WebSocket(
            "wss://city189.cn:2960/safety-v2/ws/alarmRecord",
        );
    }
    socket.addEventListener("message", function (event) {
        filterAlarm();
    });
};
//获取告警
const filterAlarm = () => {
    unified_alarms().then((res) => {
        if (res.data.data && res.data.data.length > 9) {
            showList.value = res.data.data.slice(0, 9);
        } else {
            showList.value = res.data.data;
        }
    });
};
</script>

<style lang="less" scoped>
.real-time-alarm {
    width: 416px;
    height: 224px;  
    padding: 16px;
    overflow: auto;
    .none-img {
        width: 190px;
        height: 134px;
        background: url("../../../../assets/images/card/none.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: 40px auto 10px;
    }
    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 157.143% */
        text-align: center;
    }
    .inner {
        width: 416px;
        height: 224px;
        overflow: hidden;

        // animation: scroll 5s linear infinite;
    }

    //   @keyframes scroll {
    //     0% {
    //       transform: translateY(0);
    //     }

    //     100% {
    //       transform: translateY(-100%);
    //     }
    //   }
    .first-marquee {
        animation: 12s first-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes first-marquee {
        0% {
            transform: translate3d(0, 0, 0);
        }
        /* 向上移动 */
        100% {
            transform: translate3d(0, -100%, 0);
        }
    }
    .second-marquee {
        /* 因为要在第一个span播完之前就得出现第二个span，所以就延迟12s才播放 */
        animation: 12s second-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes second-marquee {
        0% {
            transform: translateY(0);
        }
        /* 向上移动 */
        100% {
            transform: translateY(-100%);
        }
    }
    .alarm-table {
        .row {
            margin-top: 54px;

            .alarm-id-small {
                width: 24px;
                height: 24px;
                background: url("../../../../assets/images/card/order-number.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
            .alarm-id-big {
                width: 24px;
                height: 24px;
                background: url("../../../../assets/images/card/alarm-order.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
            }
            .alarm-detail {
                width: 200px;
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
            .alarm-date {
                padding-left: 24px;
                color: #47ebeb;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
            .alarm-time {
                color: #47ebeb;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 157.143% */
            }
        }
    }
}
.real-time-alarm::-webkit-scrollbar {
    //   width: 3px;
    //   height: 10px;
    //   background-color: transparent;
    display: none;
}
</style>
