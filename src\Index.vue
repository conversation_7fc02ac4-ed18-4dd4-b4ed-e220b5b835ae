<template>
    <div class="container" id="containerBox" @click="closeWindow">
        <div class="back"></div>
        <div
            class="comprehensive-supervision-navigation"
            v-if="!monitorFlag && !safetyMonitorFlag"
        >
            <div
                :class="toggle === 1 ? 'active-btn' : 'btn'"
                @click="changeToggle(1)"
            >
                <img class="icon" src="./assets/images/icon/img.svg" />
            </div>
            <div
                :class="toggle === 3 ? 'active-btn' : 'btn'"
                @click="changeToggle(3)"
            >
                <img class="icon" src="./assets/images/icon/2D.svg" />
            </div>
            <div
                :class="toggle === 2 ? 'active-btn' : 'btn'"
                @click="changeToggle(2)"
            >
                <img class="icon" src="./assets/images/icon/3D.svg" />
            </div>
        </div>
        <!--    <div class="flicker" v-if="crazyAlarmShow">-->
        <div class="flicker">
            <!-- <img class="flicker-img" src="./assets/images/flicker.svg">
      <div class="flicker-text" @click="closeCrazyAlarm">关闭弹窗</div> -->
            <div
                class="flicker-text"
                v-if="
                    video1 == null || video1 == './src/assets/mp4/video_2.mp3'
                "
                @click="showDialog"
            ></div>
            <div class="flicker-img" v-else @click="showDialog"></div>
        </div>
        <div v-if="false" class="meeting">
            <div class="meeting-img" @click="showMeeting"></div>
        </div>
        <div v-show="toggle == 1" id="imgContainer"></div>
        <div v-show="toggle == 2" id="cesiumContainer"></div>
        <div v-show="toggle == 3" id="mapContainer"></div>
        <v-header></v-header>
        <transition name="fade">
            <component
                :is="pageType"
                :key="Math.random() * 1000"
                v-if="!monitorFlag && !safetyMonitorFlag"
            ></component>
        </transition>
        <monitor-dialog
            v-if="monitorFlag"
            @closeDialog="closeDialog"
        ></monitor-dialog>
        <safety-monitor-dialog
            v-if="safetyMonitorFlag"
            @closeDialog="closeDialog"
            :enterpriseName="enterpriseName"
        ></safety-monitor-dialog>
        <v-footer @fetchPageType="getPageType"></v-footer>
        <div class="crazy-alarm-dialog" v-if="crazyAlarmDialog">
            <div class="header">
                <div class="close" @click="closeDialogCrazy"></div>
            </div>
            <div class="container">
                <div class="container-inner">
                    <div class="container-text">{{ crazyText }}</div>
                    <div class="container-text">{{ crazyTime }}</div>
                </div>
            </div>
        </div>
        <!--    <audio loop hidden controls="controls" ref="audio_1">-->
        <!--      <source  type="audio/ogg">-->
        <!--      <source :src="video1" type="audio/mpeg">-->
        <!--    </audio>-->
        <audio
            controls="controls"
            loop
            hidden
            :src="video1"
            ref="audio_1"
            type="audio/mp3"
        ></audio>
        <audio
            controls="controls"
            loop
            hidden
            :src="video2"
            ref="audio_2"
            type="audio/mp3"
        ></audio>
        <!--    <iframe-->
        <!--        :src="url"-->
        <!--        frameborder="0"-->
        <!--        wmode="transparent | window"-->
        <!--        style="opacity: 0.0.1;"-->
        <!--        v-if="isAudio"-->
        <!--        ref="audio"-->
        <!--        auto preload="auto"-->
        <!--    ></iframe>-->
        <alarm-query-dialog
            v-if="alarmQueryDialogShow"
            @closeDialog="closeDialogQuery"
        ></alarm-query-dialog>
        <meeting-query-dialog
            v-if="meetingQueryDialogShow"
            @closeDialog="closeDialogQuery"
        ></meeting-query-dialog>
    </div>
</template>

<script setup>
import {
    onMounted,
    reactive,
    ref,
    getCurrentInstance,
    onBeforeUnmount,
    onUnmounted,
    nextTick,
} from "vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import VHeader from "./components/commen/Header.vue";
import VFooter from "./components/commen/Footer.vue";
import ComprehensiveSupervision from "./components/pages/ComprehensiveSupervision/index.vue";
import ParkArchives from "./components/pages/ParkArchives/index.vue";
import SafetySupervision from "./components/pages/SafetySupervision/index.vue";
import EnvironmentalManagementLeft from "./components/pages/EnvironmentalManagement/index.vue";
import ClosedManagement from "./components/pages/ClosedManagement/index.vue";
import SmartEnergy from "./components/pages/SmartEnergy/index.vue";
import EmergencyCommand from "./components/pages/EmergencyCommand/index.vue";
import SmartNetworkManagement from "./components/pages/SmartNetworkManagement/index.vue";
import MonitorDialog from "./components/pages/monitorDialog/monitorDialog.vue";
import safetyMonitorDialog from "./components/pages/safetyMonitorDialog/index.vue";
import SecureBase from './components/pages/SecureBase/index.vue'
import DoublePrevention from './components/pages/DoublePrevention/index.vue'
import SpecialAssignments from './components/pages/SpecialAssignments/index.vue'

import * as Cesium from "cesium";
import MapLocaData from "../public/mapLoca.json";

let video1 = ref(null);
let video2 = ref(null);
let isAudio = ref(false);
let url = ref(null);
const { proxy } = getCurrentInstance();
import AMapLoader from "@amap/amap-jsapi-loader";
import AlarmQueryDialog from "./components/pages/ComprehensiveSupervision/subgroup/alarmQueryDialog.vue";
import MeetingQueryDialog from "./components/pages/ComprehensiveSupervision/subgroup/meetingQueryDialog.vue";

let alarmQueryDialogShow = ref(false);
let meetingQueryDialogShow = ref(false);
const monitorFlag = ref(false);
const safetyMonitorFlag = ref(false);
let pageType = ref(ComprehensiveSupervision);
let obj = reactive({
    1: ComprehensiveSupervision, // 综合监管
    2: ParkArchives, //园区档案,
    3: SafetySupervision, //安全监管
    4: EnvironmentalManagementLeft, //环保管理
    5: ClosedManagement, //封闭化管理
    6: SmartNetworkManagement, //智慧网管
    7: SmartEnergy, //智慧能源
    8: EmergencyCommand, //应急指挥
    9: SecureBase, //安全基础
    10: DoublePrevention,//双重预防
    11:SpecialAssignments,//特殊作业
});
let toggle = ref(3);
window.toggle = 3;
//人员离岗展示与否
const newFlag = ref(1);
const getPageType = (x) => {
    console.log(x);
    newFlag.value = x;
    pageType.value = obj[x];
};
let map1 = reactive(null);
const changeToggle = (x) => {
    console.log(x);
    toggle.value = x;
    window.toggle = x;
    proxy.$bus.emit("change_toggle", toggle.value);
    if (toggle.value == 2) {
        window.viewer.scene.forceRender();
        window.viewer.destroy(); //销毁重新加载 可能不对
        nextTick(() => {
            initCesium();
        });
    } else if (toggle.value == 3) {
        nextTick(() => {
            initMap();
        });
    }
};

const showDialog = () => {
    proxy.$loading.show();
    // app.config.globalProperties.$loading.show()
    alarmQueryDialogShow.value = true;
};
const showMeeting = () => {
    console.log(1111);
    location.assign("https://" + window.location.hostname + ":13004");
    // meetingQueryDialogShow.value = true
};
const state = reactive({
    path: [],
    current_position: [],
});
const initCesium = () => {
    // Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(80, 22, 130, 55)
    let viewer = new Cesium.Viewer("cesiumContainer", {
        imageryProvider: new Cesium.ArcGisMapServerImageryProvider({
            url: "https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer",
        }), //由ArcGIS MapServer托管的瓦片图像。默认情况下，如果可用，将使用服务器的预缓存块。
        animation: false, // Animation控件和Timeline控件，Animetion控制时间的启动和暂停，Timeline控制时间线，两个控件绑定在一起，同时出现或隐藏
        baseLayerPicker: false, //隐藏图层选择
        geocoder: false, //地理位置搜索工具，用于显示相机访问的地理位置
        infoBox: false, //是否显示信息框
        homeButton: false, //返回主视角
        timeline: false,
        fullscreenButton: false, //是否显示全屏按钮
        sceneModePicker: false, //地图按3D/2D显示
        selectionIndicator: false, //选中目标，通过框指示拾取到的对象
        navigationHelpButton: false, //是否显示右上角的帮助按钮

        // navigationInstructionsInitiallyVisible: false, // 帮助按钮，初始化的时候是否展开
        // showRenderLoopErrors: false, //如果设为true，将在一个HTML面板中显示错误信息
        // automaticallyTrackDataSourceClocks: false //自动追踪最近添加的数据源的时钟设置
    });
    window.viewer = viewer;
    console.log(window.viewer);
    console.log(111111111111111111);
    // 是否支持图像渲染像素化处理
    if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
        viewer.resolutionScale = window.devicePixelRatio;
    }
    // 开启抗锯齿
    console.log(2222222222222222);
    viewer.scene.postProcessStages.fxaa.enabled = true;
    viewer._cesiumWidget._creditContainer.style.display = "none";
    console.log(333333333333333);
    let tilesetModel = viewer.scene.primitives.add(
        new Cesium.Cesium3DTileset({
            // url: "http://*************:13005/static/C1/C1/tileset.json",
              url: "https://city189.cn:2927/static/tileset.json",
            // preferLeaves: true,
            // dynamicScreenSpaceError: true, //优化选项，是否减少离相机较远的tileset屏幕空间错误
            // dynamicScreenSpaceErrorFactor: 80, //增加动态屏幕错误的一个因子
            // foveatedScreenSpaceError: false,
            // skipLevelOfDetail: false, //可选选项，是否在遍历时候跳过详情
            maximumScreenSpaceError: 256, // GPU 占用过高,页面停止加载 用于驱动细节细化程度的最大屏幕空间错误。默认16，加载速度关键参数，值越大加载越快 数值加大，能让最终成像变模糊
            maximumCache0verflowBytes: 0,
            cullWithChildrenBounds: true,
            cullRequestsWhileMoving: true,
            cullRequestsWhileMovingMultiplier: 10, // 值越小能够更快的剔除
            preloadWhenHidden: true,
            preferLeaves: true,
            maximumMemoryUsage: 128, // 内存分配变小有利于倾斜摄影数据回收，提升性能体验
            // maximumNumberOfLoadedTiles: 1000,
            // modelMatrix: [
            //   1.0, 0.0, 0.0, 0.0,
            //   0.0, 1.0, 0.0, 0.0,
            //   0.0, 0.0, 1.0, 0.0,
            //   0.0, 0.0, -2000, 1.0,
            // ]
        }),
    );
    // tilesetModel = viewer.scene.primitives.add(
    //     new Cesium.Cesium3DTileset({
    //         // url: "http://*************:13005/static/C2/tileset.json",
    //         url: "/static/C2/tileset.json",
    //         maximumScreenSpaceError: 256, // GPU 占用过高,页面停止加载 用于驱动细节细化程度的最大屏幕空间错误。默认16，加载速度关键参数，值越大加载越快 数值加大，能让最终成像变模糊
    //         maximumCache0verflowBytes: 0,
    //         cullWithChildrenBounds: true,
    //         cullRequestsWhileMoving: true,
    //         cullRequestsWhileMovingMultiplier: 10, // 值越小能够更快的剔除
    //         preloadWhenHidden: true,
    //         preferLeaves: true,
    //         maximumMemoryUsage: 128, // 内存分配变小有利于倾斜摄影数据回收，提升性能体验
    //     }),
    // );
    // tilesetModel = viewer.scene.primitives.add(
    //     new Cesium.Cesium3DTileset({
    //         // url: "http://*************:13005/static/C3/tileset.json",
    //         url: "/static/C3/tileset.json",
    //         maximumScreenSpaceError: 256, // GPU 占用过高,页面停止加载 用于驱动细节细化程度的最大屏幕空间错误。默认16，加载速度关键参数，值越大加载越快 数值加大，能让最终成像变模糊
    //         maximumCache0verflowBytes: 0,
    //         cullWithChildrenBounds: true,
    //         cullRequestsWhileMoving: true,
    //         cullRequestsWhileMovingMultiplier: 10, // 值越小能够更快的剔除
    //         preloadWhenHidden: true,
    //         preferLeaves: true,
    //         maximumMemoryUsage: 128, // 内存分配变小有利于倾斜摄影数据回收，提升性能体验
    //     }),
    // );
    console.log(44444444444444444);
    // tilesetModel.readyPromise.then(res => {
    //   // let translation = Cesium.Cartesian3.fromArray([115.7788, 38.7089, -1771])
    //   // let m = Cesium.Matrix4.fromTranslation(translation) //fromTranslation()方法
    //   // res.modelMatrix = m
    // })
    viewer.flyTo(tilesetModel); //自动定位到模型处
    viewer.scene.globe.depthTestAgainstTerrain = false; //使用地形数据来遮挡三维模型以及其他可视化元素
    // viewer.camera.flyTo({
    //     destination: Cesium.Cartesian3.fromDegrees(106.212479, 37.984586, 0),
    //     orientation: {
    //         heading: 5.931053970155071,//旋转
    //         pitch: -0.37513891846628766,
    //         roll: 0.0
    //     }
    // })
    // viewer.scene.screenSpaceCameraController.maximumZoomDistance = 500;
    // let bounds = {
    //   west: 147.13833844,
    //   east: 147.13856899,
    //   south: -41.43606916,
    //   north: -41.43582929
    // };

    // init heatmap
    //   let heatMap = CesiumHeatmap.create(
    //       viewer, // your cesium viewer
    //       bounds, // bounds for heatmap layer
    //       {
    //         // heatmap.js options go here
    //         // maxOpacity: 0.3
    //       }
    //   );
    //   console.log(heatMap,'我是热力图')
    // // random example data
    //   let data = [{"x":147.1383442264,"y":-41.4360048372,"value":76},{"x":147.1384363011,"y":-41.4360298848,"value":63},{"x":147.138368102,"y":-41.4358360603,"value":1},{"x":147.1385627739,"y":-41.4358799123,"value":21},{"x":147.1385138501,"y":-41.4359327669,"value":28},{"x":147.1385031219,"y":-41.4359730105,"value":41},{"x":147.1384127393,"y":-41.435928255,"value":75},{"x":147.1384551136,"y":-41.4359450132,"value":3},{"x":147.1384927196,"y":-41.4359158649,"value":45},{"x":147.1384938639,"y":-41.4358498311,"value":45},{"x":147.1385183299,"y":-41.4360213794,"value":93},{"x":147.1384007925,"y":-41.4359860133,"value":46},{"x":147.1383604844,"y":-41.4358298672,"value":54},{"x":147.13851025,"y":-41.4359098303,"value":39},{"x":147.1383874733,"y":-41.4358511035,"value":34},{"x":147.1384981796,"y":-41.4359355403,"value":81},{"x":147.1384504107,"y":-41.4360332348,"value":39},{"x":147.1385582664,"y":-41.4359788335,"value":20},{"x":147.1383967364,"y":-41.4360581999,"value":35},{"x":147.1383839615,"y":-41.436016316,"value":47},{"x":147.1384082712,"y":-41.4358423338,"value":36},{"x":147.1385092651,"y":-41.4358577623,"value":69},{"x":147.138360356,"y":-41.436046789,"value":90},{"x":147.138471893,"y":-41.4359184292,"value":88},{"x":147.1385605689,"y":-41.4360271359,"value":81},{"x":147.1383585714,"y":-41.4359362476,"value":32},{"x":147.1384939114,"y":-41.4358844253,"value":67},{"x":147.138466724,"y":-41.436019121,"value":17},{"x":147.1385504355,"y":-41.4360614056,"value":49},{"x":147.1383883832,"y":-41.4358733544,"value":82},{"x":147.1385670669,"y":-41.4359650236,"value":25},{"x":147.1383416534,"y":-41.4359310876,"value":82},{"x":147.138525285,"y":-41.4359394661,"value":66},{"x":147.1385487719,"y":-41.4360137656,"value":73},{"x":147.1385496029,"y":-41.4359187277,"value":73},{"x":147.1383989222,"y":-41.4358556562,"value":61},{"x":147.1385499424,"y":-41.4359149305,"value":67},{"x":147.138404523,"y":-41.4359563326,"value":90},{"x":147.1383883675,"y":-41.4359794855,"value":78},{"x":147.1383967187,"y":-41.435891185,"value":15},{"x":147.1384610005,"y":-41.4359044797,"value":15},{"x":147.1384688489,"y":-41.4360396127,"value":91},{"x":147.1384431875,"y":-41.4360684409,"value":8},{"x":147.1385411067,"y":-41.4360645847,"value":42},{"x":147.1385237178,"y":-41.4358843181,"value":31},{"x":147.1384406464,"y":-41.4360003831,"value":51},{"x":147.1384679169,"y":-41.4359950456,"value":96},{"x":147.1384194314,"y":-41.4358419739,"value":22},{"x":147.1385049792,"y":-41.4359574813,"value":44},{"x":147.1384097378,"y":-41.4358598672,"value":82},{"x":147.1384993219,"y":-41.4360352975,"value":84},{"x":147.1383640499,"y":-41.4359839518,"value":81}];
    //   let valueMin = 0;
    //   let valueMax = 100;
    //
    // // add data to heatmap
    //   heatMap.setData(valueMin, valueMax, data);
};
var mouseTool;
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
var marker = null;
const initMap = () => {
    map1 = new AMap.Map("mapContainer", {
        //设置地图容器id
        viewMode: "3D", //是否为3D地图模式
        zoom: 12, //初始化地图级别
        mapStyle: "amap://styles/db6d462a4571314863ef03382b234c33",
        //   layers: [new AMap.TileLayer.Satellite()],
        center: [115.159226, 37.606716], //初始化地图中心点位置
    });
    window.map1 = map1;
    marker = new AMap.Marker({
        position: new AMap.LngLat(0, 0), // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
        icon: new AMap.Icon({
            image: "https://a.amap.com/jsapi/static/image/plugin/marker_red.png",
            size: [30, 36],
            imageSize: [30, 36],
        }),
        offset: [-15, -36],
    });
    polyline1 = new AMap.Polyline({
        path: path,
        strokeColor: "#49D1AF",
        strokeWeight: 3,
        strokeOpacity: 0.9,
        strokeStyle: "dashed",
        zIndex: 50,
        bubble: true,
    });
    window.map1.add([polyline1]);
    window.map1.setFitView();
    // window.map1.on("click",(ev)=>mapClick(ev))
    //添加插件
    AMap.plugin(
        [
            "AMap.ToolBar",
            "AMap.Scale",
            "AMap.HawkEye",
            "AMap.RangingTool",
            "AMap.Polygon",
            "AMap.MouseTool",
            "AMap.HeatMap",
            "AMap.PolylineEditor",
            "AMap.PolygonEditor",
            "AMap.Polyline",
            "AMap.CircleMarker",
            "AMap.MarkerClusterer",
            "AMap.MoveAnimation",
            "AMap.MassMarks",
            "AMap.Weather",
        ],
        function () {
            //异步同时加载多个插件
            // map.addControl(new AMap.HawkEye()); //显示缩略图
            // map.addControl(new AMap.Scale()); //显示当前地图中心的比例尺
        },
    );
    mouseTool = new AMap.MouseTool(window.map1);
    window.mouseTool = mouseTool;
    console.log("mouse", window.mouseTool);
    var heatmap;
    window.heatmap = heatmap;
};
const audioOption = () => {
    let hostname = window.location.hostname;
    // console.log(hostname)
    let socket = null;
    if (window.location.protocol === "http:") {
        const wsuri = `ws://${hostname}:32362/security/websocket/security_alarms`;

        socket = new WebSocket(wsuri);
    } else if (window.location.protocol === "https:") {
        socket = new WebSocket(
            "wss://city189.cn:2960/security/websocket/security_alarms",
        );
    }
    // const wsuri = `ws://${hostname}:32362/security/websocket/security_alarms`;

    socket.addEventListener("message", function (event) {
        // console.log(event);
        //   console.log(event.data);
        // console.log(newFlag.value == 5);
        let type = event.data;
        if (type == "cars_today") {
            proxy.$bus.emit("cars_today");
        } else if (type == "security_car_statistics") {
            proxy.$bus.emit("security_car_statistics");
        } else if (type == "security_car_io") {
            proxy.$bus.emit("security_car_io");
        } else if (type == "security_car_info") {
            proxy.$bus.emit("security_car_info");
        } else if (type == "deviceNumber") {
            proxy.$bus.emit("deviceNumber");
        } else if (type == "security_alarms") {
            proxy.$bus.emit("security_alarms");
        } else if (type == "cars_today_by_deviceId") {
            proxy.$bus.emit("cars_today_by_deviceId");
        } else if (type == "cameraPersonLink/listIoc" && newFlag.value == 5) {
            proxy.$bus.emit("cameraPersonLink_listIoc");
        }
    });
};
const crazyText = ref("");
const crazyTime = ref("");
const audio_1 = ref(null);
const audio_2 = ref(null);
const startPlay_1 = () => {
    video1.value = "/src/assets/mp4/video_1.mp3";
    audio_1.value.currentTime = 0; //从头开始播放提示音
    setTimeout(() => {
        audio_1.value && audio_1.value.play(); //播放
    });
};
const startPlay_2 = () => {
    video1.value = "/src/assets/mp4/video_2.mp3";
    audio_1.value.currentTime = 0; //从头开始播放提示音
    setTimeout(() => {
        audio_1.value && audio_1.value.play(); //播放
    });
};
const flickerAudioOption = () => {
    let hostname = window.location.hostname;
    let socket = null;
    if (window.location.protocol === "http:") {
        const wsuri = `ws://${hostname}:32362/safety-v2/ws/alarmRecord`;

        socket = new WebSocket(wsuri);
    } else if (window.location.protocol === "https:") {
        socket = new WebSocket(
            "wss://city189.cn:2960/safety-v2/ws/alarmRecord",
        );
    }

    socket.addEventListener("message", function (event) {
        crazyAlarmShow.value = true;
        console.log(event);
        console.log(event.data);
        let obj = JSON.parse(event.data);
        console.log(obj);
        if (obj.alarmType == "1") {
            crazyAlarmDialog.value = true;
            startPlay_1();
            // setTimeout(() => {
            //   url.value = "../src/assets/mp4/video_1.mp3"
            //   isAudio.value = true
            // }, 3000)
        } else {
            // setTimeout(() => {
            //   url.value = "../src/assets/mp4/video_2.mp3"
            //   isAudio.value = true
            // }, 3000)
            //   startPlay_1()
        }
        // console.log(eval(event.data));
        crazyText.value = obj.content;
        crazyTime.value = obj.alarmTime;
        console.log(crazyText.value);
        //   if(JSON.parse(event.data).alarm_type)
    });
};
const crazyAlarmShow = ref(false);
const closeCrazyAlarm = () => {
    // crazyAlarmShow.value = false
    crazyAlarmDialog.value = false;
    isAudio.value = false;
    audio_1.value.pause(); //播放
    // audio_2.value.pause(); //播放
};
const crazyAlarmDialog = ref(false);
const closeDialogCrazy = () => {
    isAudio.value = false;
    crazyAlarmDialog.value = false;
    audio_1.value.pause(); //播放
    // audio_2.value.pause(); //播放
    video1.value = null;
};
//打点工具
const mapClick = (ev) => {
    marker.setPosition(ev.lnglat);
    console.log(ev.lnglat);
    window.map1.add(marker);
};
proxy.$bus.on("uesMonitor", (val) => {
    console.log("test ", val);
    monitorFlag.value = val;
});
const enterpriseName = ref("");
proxy.$bus.on("safetyUesMonitor", (val) => {
    console.log("test ", val);
    enterpriseName.value = val;
    safetyMonitorFlag.value = true;
});
const closeDialog = () => {
    monitorFlag.value = false;
    safetyMonitorFlag.value = false;
};
const closeDialogQuery = () => {
    // console.log(video1.value,'我是video')
    //
    // video1.value==null
    proxy.$loading.hide();
    meetingQueryDialogShow.value = false;
    alarmQueryDialogShow.value = false;
};
onMounted(() => {
    // setTimeout(() => {
    //     location.reload()
    // }, 5000);
    flickerAudioOption();
    audioOption();
    nextTick(() => {
        initCesium();
        initMap();
    });
});
onUnmounted(() => {
    proxy.$bus.off("uesMonitor");
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.container {
    user-select: none;
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;

    .fade-enter-active,
    .fade-leave-active {
        opacity: 0;
        transform: rotate(-90deg);
        // transform: translateX(-150px);
        // transition: all 1s;
        transition: all 0.5s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
        // transition: all 0.5s ease;
    }

    #imgContainer {
        width: 100vw;
        height: 100vh;
        // z-index: 999999;
        position: absolute;
        top: 0;
        left: 0;
        background: url("./assets/images/park.jpg") no-repeat;
        background-size: cover;
        background-position: center;

        /deep/ .cesium-widget {
            width: 100vw !important;
            height: 100vh !important;
        }

        /deep/ canvas {
            width: 100vw !important;
            height: 100vh !important;
        }
    }

    #cesiumContainer {
        width: 100vw;
        height: 100vh;
        // z-index: 999999;
        position: absolute;
        top: 0;
        left: 0;

        /deep/ .cesium-widget {
            width: 100vw !important;
            height: 100vh !important;
        }

        /deep/ canvas {
            width: 100vw !important;
            height: 100vh !important;
        }
    }

    #mapContainer {
        width: 100vw;
        height: 100vh;
        // z-index: 999999;
        position: absolute;
        top: 0;
        left: 0;

        /deep/ .cesium-widget {
            width: 100vw !important;
            height: 100vh !important;
        }

        /deep/ canvas {
            width: 100vw !important;
            height: 100vh !important;
        }
    }

    .back {
        width: calc(100vw);
        height: calc(100vh);
        background: url("./assets/images/back.svg") no-repeat center/cover;
        width: 100%;
        pointer-events: none;
        position: absolute;
        z-index: 1000;
    }
}

.comprehensive-supervision-navigation {
    width: 200px;
    height: 40px;
    z-index: 1000;
    position: absolute;
    //top: 806px;
    //left: 496px;
    display: flex;
    top: 10%;
    left: 68%;

    > div:not(:first-child) {
        margin-left: 12px;
    }

    .btn {
        width: 34px;
        height: 34px;
        background: url("./assets/images/jump-btn/tab.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }

    .active-btn {
        width: 34px;
        height: 34px;
        background: url("./assets/images/jump-btn/tab-active.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        // gap: 24px;
    }

    .icon {
        width: 24px;
        height: 24px;
        // margin: auto 0;
        margin-top: 4px;
        margin-left: 4px;
    }

    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        margin: auto 0;
        margin-left: 24px;
    }
}
.meeting {
    width: 40px;
    height: 88px;
    position: absolute;
    z-index: 1000;

    //top: 806px;
    //left: 496px;
    top: 56%;
    left: 72%;

    .meeting-img {
        width: 40px;
        height: 40px;
        background: url("./assets/images/icon/meeting.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }
}
.flicker {
    width: 40px;
    height: 88px;
    position: absolute;
    z-index: 1000;

    //top: 806px;
    //left: 496px;
    top: 16%;
    left: 72%;

    .flicker-img {
        width: 40px;
        height: 40px;
        background: url("./assets/images/alarm/online.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }

    .flicker-text {
        margin-top: 8px;
        width: 40px;
        height: 40px;
        background: url("./assets/images/alarm/offline.svg") no-repeat;
        background-size: cover;
        background-position: center;
    }
}

.crazy-alarm-dialog {
    width: 603px;
    height: 300px;
    background: url("./assets/images/crazyAlarm.svg") no-repeat center center;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;

    .header {
        width: 603px;
        height: 52px;

        .close {
            width: 24px;
            height: 24px;
            background: url("./assets/images/dialog/close.svg") no-repeat center
                center;
            background-size: cover;
            background-position: center;
            margin-top: 14px;
            margin-left: 570px;
        }
    }

    .container {
        width: 603px;
        height: 248px;
        align-items: center;
        display: flex;

        .container-inner {
            margin: auto;
        }

        .container-text {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 22px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px; /* 157.143% */
            text-align: center;
        }
    }
}
</style>
