<template>
    <div :class="`security-archives${size}`">
        <div class="top-btns">
                <div
                    :class="innerActive === 1? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(1)"
                >
                    建设三同时
                </div>
                <div
                    :class="innerActive === 2? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(2)"
                >
                    安全生产证照
                </div>
                <div
                    :class="innerActive === 3? 'active-top-btn': 'top-btn'"
                    @click="selectInnerType(3)"
                >
                    安全评价
                </div>
            </div>
        
        <div class="construct alarm" v-if="innerActive == 1">
            <el-table
                class="tablebox"
                :data="tableDataFirst"
                style="width: 100%"
            >
                <el-table-column prop="num" label="序号" style="width: 5%" />
                <el-table-column
                    prop="construct_name"
                    label="建设项目名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="sd_construct_type"
                    label="建设项目类型"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                layout="->,total, prev, pager, next"
                :total="totalFirst"
                v-model:currentPage="pageNumFirst"
                :page-size="pageSizeFirst"
                @current-change="handleCurrentChangeFirst"
            />
        </div>
        <div class="production-license alarm" v-else-if="innerActive == 2">
            <el-table
                class="tablebox"
                :data="tableDataSecond"
                style="width: 100%"
            >
                <el-table-column prop="num" label="序号" style="width: 5%" />
                <el-table-column
                    prop="licence_name"
                    label="许可证名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="validity_start"
                    label="有效期始"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="validity_end"
                    label="有效期止"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="licence_status"
                    label="许可证状态"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                layout="->,total, prev, pager, next"
                :total="totalSecond"
                v-model:currentPage="pageNumSecond"
                :page-size="pageSizeSecond"
                @current-change="handleCurrentChangeSecond"
            />
        </div>
        <div class="safety-evaluation alarm" v-else>
            <el-table
                class="tablebox"
                :data="tableDataThird"
                style="width: 100%"
            >
                <el-table-column prop="num" label="序号" style="width: 5%" />
                <el-table-column
                    prop="sd_file_type"
                    label="档案类型"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="file_name"
                    label="档案名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="updated_date"
                    label="上传时间"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                layout="->,total, prev, pager, next"
                :total="totalThird"
                v-model:currentPage="pageNumThird"
                :page-size="pageSizeThird"
                @current-change="handleCurrentChangeThird"
            />
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
import {
    construct_meanwhile,
    product_certificate,
    safety_evaluation_management,
} from "../../../../assets/js/api/parkArchives";
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const emit = defineEmits(["sizeChange"]);
const size = ref(1);
const innerActive = ref(1);
watch(innerActive, (a, b) => {
    // console.log(a,b);
    //   if (a == 1) {
    //     emit("sizeChange", 4);
    //   } else {
    //     emit("sizeChange", 5);
    //   }
});
const tableDataFirst = ref([]);
const middleTableDataFirst = ref([]);
const totalFirst = ref(0);
const pageNumFirst = ref(1);
const pageSizeFirst = ref(3);
const tableDataSecond = ref([]);
const middleTableDataSecond = ref([]);
const totalSecond = ref(0);
const pageNumSecond = ref(1);
const pageSizeSecond = ref(3);
const tableDataThird = ref([]);
const middleTableDataThird = ref([]);
const totalThird = ref(0);
const pageNumThird = ref(1);
const pageSizeThird = ref(3);
const selectInnerType = (type) => {
    innerActive.value = type;
    if (type == 1) {
        middleTableDataFirst.value = [];
        construct_meanwhile({ enterprise_name: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalFirst.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    middleTableDataFirst.value.push(item);
                });
                searchConstructMeanwhile();
                console.log(middleTableDataFirst.value);
            }
        });
    } else if (type == 2) {
        middleTableDataSecond.value = [];
        product_certificate({ enterprise_name: pickId.value }).then((res) => {
            if (res.data.success && res.data.data && res.data.data.length) {
                totalSecond.value = res.data.data.length;
                res.data.data.forEach((item, index) => {
                    Reflect.set(item, "num", index + 1);
                    if (item.licence_status == 0) {
                        item.licence_status = "无效";
                    } else {
                        item.licence_status = "有效";
                    }
                    middleTableDataSecond.value.push(item);
                });
                searchProductCertificate();
                console.log(middleTableDataSecond.value);
            }
        });
    } else {
        middleTableDataThird.value = [];
        safety_evaluation_management({ enterprise_name: pickId.value }).then(
            (res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    totalThird.value = res.data.data.length;
                    res.data.data.forEach((item, index) => {
                        Reflect.set(item, "num", index + 1);
                        middleTableDataThird.value.push(item);
                    });
                    searchSafetyEvaluationManagement();
                    console.log(middleTableDataThird.value);
                }
            },
        );
    }
};
const searchProductCertificate = () => {
    tableDataSecond.value = [];
    for (let i = 0; i < pageSizeSecond.value; i++) {
        let pageIndex = (pageNumSecond.value - 1) * pageSizeSecond.value + i;
        console.log(pageIndex);
        if (pageIndex < totalSecond.value) {
            tableDataSecond.value[i] =
                middleTableDataSecond.value[
                    (pageNumSecond.value - 1) * pageSizeSecond.value + i
                ];
        }
    }
};
const searchConstructMeanwhile = () => {
    tableDataFirst.value = [];
    for (let i = 0; i < pageSizeFirst.value; i++) {
        let pageIndex = (pageNumFirst.value - 1) * pageSizeFirst.value + i;
        console.log(pageIndex);
        if (pageIndex < totalFirst.value) {
            tableDataFirst.value[i] =
                middleTableDataFirst.value[
                    (pageNumFirst.value - 1) * pageSizeFirst.value + i
                ];
        }
    }
};
const searchSafetyEvaluationManagement = () => {
    tableDataThird.value = [];
    for (let i = 0; i < pageSizeThird.value; i++) {
        let pageIndex = (pageNumThird.value - 1) * pageSizeThird.value + i;
        console.log(pageIndex);
        if (pageIndex < totalThird.value) {
            tableDataThird.value[i] =
                middleTableDataThird.value[
                    (pageNumThird.value - 1) * pageSizeThird.value + i
                ];
        }
    }
};
const handleCurrentChangeFirst = (e) => {
    pageNumFirst.value = e;
    searchConstructMeanwhile();
};
const handleCurrentChangeSecond = (e) => {
    pageNumSecond.value = e;
    searchProductCertificate();
};
const handleCurrentChangeThird = (e) => {
    pageNumThird.value = e;
    searchSafetyEvaluationManagement();
};
onMounted(() => {
    middleTableDataFirst.value = [];
    construct_meanwhile({ enterprise_name: pickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            totalFirst.value = res.data.data.length;
            res.data.data.forEach((item, index) => {
                Reflect.set(item, "num", index + 1);
                middleTableDataFirst.value.push(item);
            });
            searchConstructMeanwhile();
            console.log(middleTableDataFirst.value);
        }
    });
});
</script>

<style lang="less" scoped>
.security-archives1 {
    width: 792px;
    height: 416px;
    margin-top: 16px;
    // background-color: antiquewhite;
}
.security-archives2 {
    width: 792px;
    height: 184px;
    margin-top: 16px;
    // background-color: antiquewhite;
}
 .top-btns{
        display: flex;
        gap:8px;
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 16px;
text-align: center;

        .active-top-btn{
           
border-radius: 2px;
padding: 4px 8px;
border: 1px solid #FFC61A;
color:#FFC61A;
        }
        .top-btn{
            border-radius: 2px;
padding: 4px 8px;
border: 1px solid #80EAFF;
color:#80EAFF;
        }
    }
.construct {
    width: 792px;
    height: 376px;
    margin-top: 12px;
    // background-color: antiquewhite;
    table {
        border-collapse: collapse;
    }
    table td {
        border-style: solid;
        border-width: 1px;
        border-color: #30abe8;
    }

    .title1 {
        width: 240px;
        height: 36px;
        padding: 0 12px;
        color: #47ebeb;
        text-align: left;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        background-color: rgba(48, 171, 232, 0.1);
    }
    .value1 {
        width: 240px;
        height: 58px;
        padding: 0 12px;
        color: #fff;
        text-align: justify;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}
.alarm {
    width: 792px;
    height: 288px;
    margin-top: 12px;
    .tablebox {
        //表格四个边框的颜色
        border: 1px solid #30abe8 !important;
        th.el-table__cell {
            border: none !important;
        }
    }
    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        border-bottom: 1px solid #30abe8 !important;
        height: 36px;
        background-color: rgba(48, 171, 232, 0.1) !important;
        color: #47ebeb;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        // border-color: #30ABE8 !important;
    }
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: 1px solid #30abe8 !important;
            border-right: 1px solid #30abe8 !important;
        }
    }
    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
        // border-color: #30ABE8 !important;
    }
    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    :deep(.el-table__cell) {
        border-right: 1px solid #30abe8 !important ;
        border-bottom: 1px solid #30abe8 !important  ;
    }
    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }
    .pagination {
        margin-top: 16px;
    }
    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 2px;
        border: 1px solid #47ebeb;
        background: linear-gradient(
            180deg,
            rgba(71, 235, 235, 0) 50%,
            rgba(71, 235, 235, 0.45) 100%
        );
        color: #47ebeb;
        text-align: center;

        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 2px;
        border: 1px solid #47ebeb;
        background: linear-gradient(
            180deg,
            rgba(71, 235, 235, 0) 50%,
            rgba(71, 235, 235, 0.45) 100%
        );
        color: #47ebeb;
        text-align: center;

        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination .btn-next) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination .btn-prev) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        border-radius: 2px;
        border: 1px solid #30abe8;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
    :deep(.el-pagination__total) {
        background-color: transparent; /*进行修改未选中背景和字体*/
        color: #30abe8;
        text-align: center;
        /* Body/regular */
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}
.production-license {
    width: 792px;
    height: 144px;
    margin-top: 12px;
    table {
        border-collapse: collapse;
    }
    table td {
        border-style: solid;
        border-width: 1px;
        border-color: #30abe8;
    }

    .title1 {
        width: 240px;
        height: 36px;
        padding: 0 12px;
        color: #47ebeb;
        text-align: left;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        background-color: rgba(48, 171, 232, 0.1);
    }
    .value1 {
        width: 240px;
        height: 36px;
        padding: 0 12px;
        color: #fff;
        text-align: justify;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}
.safety-evaluation {
    width: 792px;
    height: 144px;
    margin-top: 12px;
    table {
        border-collapse: collapse;
    }
    table td {
        border-style: solid;
        border-width: 1px;
        border-color: #30abe8;
    }

    .title1 {
        width: 240px;
        height: 36px;
        padding: 0 12px;
        color: #47ebeb;
        text-align: left;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        background-color: rgba(48, 171, 232, 0.1);
    }
    .value1 {
        width: 240px;
        height: 36px;
        padding: 0 12px;
        color: #fff;
        text-align: justify;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
    }
}
</style>
