<template>
    <div class="safety-supervision-right">
          <!-- <wrap-slot
            title1="安全包保履职完成率"
            title2="监测指标告警统计"
            title3="重大危险源监控设备"
        >
            <template #content1>
            </template>
            <template #content2>
            </template>
            <template #content3>
            </template>
        </wrap-slot> -->
        <right-top-update></right-top-update>
        <right-center-update></right-center-update>
        <right-bottom-update></right-bottom-update>

    </div>
</template>

<script setup>
import WrapSlot from "../../commenNew/WrapSlot.vue";
import RightCenter from "./subgroup/RightCenter.vue";
import RightBottom from "./subgroup/RightBottom.vue";
import RightBottomNew from "./subgroup/RightBottomNew.vue";
import RightBottomUpdate from "./subgroup/RightBottomUpdate.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightTopNew from "./subgroup/RightTopNew.vue";
import RightTopUpdate from "./subgroup/RightTopUpdate.vue";
import RightCenterUpdate from "@/components/pages/SafetySupervision/subgroup/RightCenterUpdate.vue";
</script>

<style lang="less" scoped>
.safety-supervision-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
