<svg width="57" height="48" viewBox="0 0 57 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4374_30824)">
<path d="M13.5619 47.7812L0.917384 40L13.5619 32.2188H43.4381L56.0826 40L43.4381 47.7812H13.5619Z" fill="url(#paint0_linear_4374_30824)" stroke="url(#paint1_linear_4374_30824)" stroke-width="0.4375"/>
<path d="M5.50004 39L16.5 46H40.5L51.5 39L55.5 22H1.5L5.50004 39Z" fill="url(#paint2_linear_4374_30824)"/>
<path d="M16.5637 45.7812L5.91394 39.0041L17.0613 32.2188H39.9387L51.0861 39.0041L40.4363 45.7812H16.5637Z" fill="url(#paint3_linear_4374_30824)" stroke="url(#paint4_linear_4374_30824)" stroke-width="0.4375"/>
<path d="M17.5717 43.75L9.9717 39L17.5717 34.25H39.4283L47.0283 39L39.4283 43.75H17.5717Z" fill="url(#paint5_linear_4374_30824)" stroke="url(#paint6_linear_4374_30824)" stroke-width="0.5"/>
<path d="M9.5 39L17.5 44H39.5L47.5 39L51.5 20H5.5L9.5 39Z" fill="url(#paint7_linear_4374_30824)"/>
<path d="M39.4373 43.7812H17.5627L9.69488 38.8638L5.7696 20.2188H51.2304L47.3051 38.8638L39.4373 43.7812Z" stroke="url(#paint8_linear_4374_30824)" stroke-opacity="0.6" stroke-width="0.4375"/>
<g clip-path="url(#clip1_4374_30824)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.0004 5V4C35.0004 3.44772 34.5527 3 34.0004 3H21.0004C20.4481 3 20.0004 3.44772 20.0004 4V20C20.0004 20.5523 20.4481 21 21.0004 21H34.0004C34.5527 21 35.0004 20.5523 35.0004 20V19H25.0004V5H35.0004ZM27.0004 11.5V12.5C27.0004 13.0523 27.4481 13.5 28.0004 13.5H32.5004V15.2268C32.5004 16.0969 33.5347 16.5519 34.1761 15.9639L37.6962 12.7372C38.1286 12.3408 38.1286 11.6592 37.6962 11.2628L34.1761 8.03608C33.5347 7.44813 32.5004 7.90313 32.5004 8.77324V10.5H28.0004C27.4481 10.5 27.0004 10.9477 27.0004 11.5Z" fill="white"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_4374_30824" x1="28.5" y1="32" x2="28.5" y2="48" gradientUnits="userSpaceOnUse">
<stop offset="0.315958" stop-color="#199FFF" stop-opacity="0"/>
<stop offset="1" stop-color="#199FFF" stop-opacity="0.45"/>
</linearGradient>
<linearGradient id="paint1_linear_4374_30824" x1="28.5" y1="32" x2="28.5" y2="48" gradientUnits="userSpaceOnUse">
<stop offset="0.442609" stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF"/>
</linearGradient>
<linearGradient id="paint2_linear_4374_30824" x1="28.5003" y1="22" x2="28.5003" y2="44.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint3_linear_4374_30824" x1="28.5" y1="32" x2="28.5" y2="46" gradientUnits="userSpaceOnUse">
<stop offset="0.315958" stop-color="#199FFF" stop-opacity="0"/>
<stop offset="1" stop-color="#199FFF" stop-opacity="0.45"/>
</linearGradient>
<linearGradient id="paint4_linear_4374_30824" x1="28.5" y1="32" x2="28.5" y2="46" gradientUnits="userSpaceOnUse">
<stop offset="0.442609" stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF"/>
</linearGradient>
<linearGradient id="paint5_linear_4374_30824" x1="28.5" y1="34" x2="28.5" y2="44" gradientUnits="userSpaceOnUse">
<stop offset="0.200486" stop-color="#199FFF" stop-opacity="0"/>
<stop offset="1" stop-color="#199FFF" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint6_linear_4374_30824" x1="28.5" y1="34" x2="28.5" y2="44" gradientUnits="userSpaceOnUse">
<stop offset="0.191403" stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF"/>
</linearGradient>
<linearGradient id="paint7_linear_4374_30824" x1="28.0208" y1="20" x2="28.0208" y2="44" gradientUnits="userSpaceOnUse">
<stop stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF" stop-opacity="0.45"/>
</linearGradient>
<linearGradient id="paint8_linear_4374_30824" x1="28.0208" y1="20" x2="28.0208" y2="44" gradientUnits="userSpaceOnUse">
<stop stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF"/>
</linearGradient>
<clipPath id="clip0_4374_30824">
<rect width="56" height="48" fill="white" transform="translate(0.5)"/>
</clipPath>
<clipPath id="clip1_4374_30824">
<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 41 0)"/>
</clipPath>
</defs>
</svg>
