<template>
    <div class="violation-records">
        <div class="btn" @click="clickSearch">报警查询</div>
        <div v-if="list == null || list.length == 0">
            <div class="none-img"></div>
            <div class="text">暂无告警数据</div>
        </div>
        <div class="detail" v-else>
            <div :class="list.length > 4 ? 'wrapper first-marquee' : 'wrapper'">
                <div
                    class="inner"
                    v-for="(item, index) in list"
                    :key="item.car_plate"
                >
                    <table cellspacing="3" cellpadding="0">
                        <tr>
                            <!-- <td>
                    <span class="title"
                        ><el-icon class="icon"><Van /></el-icon>行为：</span
                    >{{ item.behavior }}
                    </td> -->
                            <td>
                                <span class="title"
                                    ><el-icon class="icon"><Postcard /></el-icon
                                    >车牌：</span
                                >{{ item.car_plate }}
                            </td>
                            <td>
                                <span class="title"
                                    ><el-icon class="icon"
                                        ><CircleClose /></el-icon
                                    >违规类型：</span
                                >{{ showWords(item.alarm_type, 4) }}
                            </td>
                        </tr>
                        <tr>
                            <td @click="clickLocation(item)">
                                <span class="title"
                                    ><el-icon class="icon"><Location /></el-icon
                                    >地点：</span
                                >{{ item.longitude }}，{{ item.latitude }}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="title"
                                    ><el-icon class="icon"><Clock /></el-icon
                                    >时间：</span
                                >{{ item.alarm_time }}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div v-if="list.length > 4">
                <div
                    :class="
                        list.length > 4 ? 'wrapper second-marquee' : 'wrapper'
                    "
                >
                    <div
                        class="inner"
                        v-for="(item, index) in list"
                        :key="index"
                    >
                        <table cellspacing="3" cellpadding="0">
                            <tr>
                                <!-- <td>
                    <span class="title"
                        ><el-icon class="icon"><Van /></el-icon>行为：</span
                    >{{ item.behavior }}
                    </td> -->
                                <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><Postcard /></el-icon
                                        >车牌：</span
                                    >{{ item.car_plate }}
                                </td>
                                <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><CircleClose /></el-icon
                                        >违规类型：</span
                                    >{{ showWords(item.alarm_type, 6) }}
                                </td>
                            </tr>
                            <tr>
                                <td @click="clickLocation(item)">
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><Location /></el-icon
                                        >地点：</span
                                    >{{ item.longitude }}，{{ item.latitude }}
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><Clock /></el-icon
                                        >时间：</span
                                    >{{ item.alarm_time }}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineEmits } from "vue";
import { security_car_violation } from "../../../../assets/js/api/closedManagement";
import * as Cesium from "cesium";
const list = ref([]);
const emit = defineEmits(["search"]);
const clickSearch = () => {
    emit("search");
};
const showWords = (content, length) => {
    if (content.length > length) {
        return content.substr(0, length) + "...";
    } else {
        return content;
    }
};
const clickLocation = (val) => {
    console.log(val.longitude);
    Reflect.set(val, "longitude", Number(val.longitude));
    Reflect.set(val, "latitude", Number(val.latitude));
    window.viewer.entities.removeAll(); //删除所有
    window.map1.clearMap();

    if (window.toggle == 2) {
        var data = [];
        data.push(val);
        putIcons(data, wastewaterOutletImg);
        console.log(window.viewer.entities);
    } else if (window.toggle == 3) {
        let marker = new AMap.Marker({
            icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            position: [val.longitude, val.latitude],
            // offset: new AMap.Pixel(-26, -54),
        });

        marker.setMap(window.map1);
        window.map1.setFitView();
    }
};
import wastewaterOutletImg from "../../../../assets/images/poi/bayonet.svg";

const putIcons = (_datas, img, _parent) => {
    console.log("添加视频icon");
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        console.log(data);
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        const entity = window.viewer.entities.add({
            name: "cameraPoint",
            // 参数顺序：经度、纬度
            id: data.name,
            position: Cesium.Cartesian3.fromDegrees(
                data.longitude,
                data.latitude,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 48,
                height: 88,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                pixelOffset: new Cesium.Cartesian2(0, -44),
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            type: "text", // 自定义属性
        });
        window.viewer.zoomTo(entity);
        console.log(999999999999999999999999999999999);
    }
};
onMounted(() => {
    security_car_violation({ "": "" }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            if (res.data.data.length > 5) {
                list.value = res.data.data.slice(0, 5);
            } else {
                list.value = res.data.data;
            }
            console.log(list.value);
        }
    });
});
</script>

<style lang="less" scoped>
.violation-records {
    width: 400px;
    height: 584px;
    // background-color: aquamarine;
    .none-img {
        width: 190px;
        height: 134px;
        background: url("../../../../assets/images/card/none.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: 160px auto 10px;
    }
    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 157.143% */
        text-align: center;
    }
    .btn {
        width: 72px;
        height: 24px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        border: 0.622px solid #1ab2ff;
        background: rgba(26, 178, 255, 0.3);
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 133.333% */
    }
    .detail {
        width: 400px;
        height: 560px;
        overflow: auto;
        margin-top: 8px;
        // background-color: antiquewhite;
        > div:not(:first-child) {
            margin-top: 8px;
        }
        .inner {
            width: 396px;
            height: 102px;
            border-radius: 4px;
            border: 1px solid #30abe8;
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px; /* 157.143% */
            //   overflow: hidden;
            margin-top: 8px;

            .title {
                color: #30abe8;
                display: inline-block;
                .icon {
                    display: inline-block;
                    margin-right: 4px;
                    // margin-top: 4px;
                }
            }
        }
    }
    .wrapper {
        width: 400px;
        height: 560px;
        overflow: hidden;

        // animation: scroll 5s linear infinite;
    }
    .first-marquee {
        animation: 16s first-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes first-marquee {
        0% {
            transform: translate3d(0, 0, 0);
        }
        /* 向上移动 */
        100% {
            transform: translate3d(0, -100%, 0);
        }
    }
    .second-marquee {
        /* 因为要在第一个span播完之前就得出现第二个span，所以就延迟12s才播放 */
        animation: 16s second-marquee linear infinite normal;
        // padding-right: 30%;
    }
    @keyframes second-marquee {
        0% {
            transform: translateY(0);
        }
        /* 向上移动 */
        100% {
            transform: translateY(-100%);
        }
    }
    //   .inner  {
    //     animation: scroll 5s linear infinite;
    //   }

    //   @keyframes scroll {
    //     0% {
    //       transform: translateY(0);
    //     }

    //     100% {
    //       transform: translateY(-100%);
    //     }
    //   }
    .detail::-webkit-scrollbar {
        // width: 3px;
        // height: 10px;
        // background-color: transparent;
        display: none;
    }
}
</style>
