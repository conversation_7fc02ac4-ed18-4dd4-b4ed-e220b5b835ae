<template>
<div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">全方位监控</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    <div class="container">
                        <div class="left-ctl" @click="clickLeft" v-if="showArrow"></div>
                        <div class="ctl" v-else></div>
                        <div class="video-wall">
                            <div class="one">
                                <div class="container-inner" id="container1"></div>
                                <div class="container-inner" id="container2"></div>
                                <div class="container-inner" id="container3"></div>
                            </div>
                            <div class="two">
                                <div class="container-inner" id="container4"></div>
                                <div class="container-inner" id="container5"></div>
                                <div class="container-inner" id="container6"></div>
                            </div>
                            <div class="three">
                                <div class="container-inner" id="container7"></div>
                                <div class="container-inner" id="container8"></div>
                                <div class="container-inner" id="container9"></div>
                            </div>
                        </div>
                        <div class="right-ctl" @click="clickRight" v-if="showArrow"></div>
                        <div class="ctl" v-else></div>
                    </div>
                   
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
    
</template>

<script setup>
import { get } from "@vueuse/shared";
import {
    getEquipmentInfo,
    getFlv,
} from "../../../assets/js/api/safetySupervision";
import { selectAllClosed } from "../../../assets/js/api/comprehensiveSupervision";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
const allList = ref([]);
const listIndex = ref(0);
const canUseIndex = ref(1); //共几个页面
const showList = ref([]);
const showArrow = ref(false);
const { proxy } = getCurrentInstance();
let playList = [];
const emit = defineEmits(["closeDialog"]);
const searchAllClosed = () => {
    selectAllClosed({ groupId: 1001, equipmentstatus: 1 }).then((res) => {
        console.log(res);
        if (res.data && res.data.length) {
            console.log(res.data);
            allList.value = res.data;
            canUseIndex.value = Math.ceil(res.data.length / 9);
            if (canUseIndex.value > 1) {
                showArrow.value = true;
            }
            console.log(canUseIndex.value);
        }
        clickRight();
    });
};
//创建jessibuca对象数组
const _create = (id) => {
    console.log("888888", id);
    const $container = document.getElementById("container" + id);

    const jessibuca = new JessibucaPro({
        container: $container,
        decoder: "/Jessibuca/decoder-pro.js",
        videoBuffer: 0.1, // 缓存时长
        isResize: false,
        text: "",
        loadingText: "加载中",
        debug: false,
        useMSE: false,
        useSIMD: true,
        useWCS: true,
        hasAudio: true,
        useVideoRender: false,
        controlAutoHide: false,
        showBandwidth: false, // 显示网速
        showPerformance: false,
        operateBtns: {
            fullscreen: false,
            screenshot: false,
            play: false,
            audio: false,
        },
        watermarkConfig: {
            text: {
                content: "",
            },
            right: 10,
            top: 10,
        },
    });
    jessibuca.on("fullscreen", function (flag) {
        console.log("is fullscreen", id, flag);
    });
    playList.push(jessibuca);
};
//销毁jessibuca对象数组
const destroy = () => {
    for (let i = 0; i < playList.length; i++) {
        let player = playList[i];
        console.log(player);
        player && player.destroy();
    }
    playList = [];
};
onMounted(() => {
    searchAllClosed();
});
// 创建 并播放
const creatDom = () => {
    console.log(222222222222);
    for (let i = 0; i < showList.value.length; i++) {
        _create(i + 1);
        // console.log($player)
        // $player.style.display = 'inline-block';
        // $pause.style.display = 'none';
        // $destroy.style.display = 'none';
    }
    play();
};
//播放
const play = () => {
    for (let i = 0; i < wsFlvData.value.length; i++) {
        var id = i + 1;
        //接口
        let player = playList[i];
        let url = proxy.$changeUrl(wsFlvData.value[i]);
        // if ($playHref.value) {
        if (url != null && url != "离线") {
            setTimeout(
                (url) => {
                    console.log(url);
                    player &&
                        player
                            .play(url)
                            .then(() => {})
                            .catch((e) => {
                                console.error(e);
                            });
                },
                0,
                url,
            );
            // }
        }
    }
};
//
const changeShow = () => {
    if (playList != []) {
        destroy();
        wsFlvData.value = [];
    }
    console.log((listIndex.value - 1) * 9);
    console.log(listIndex.value * 9 - 1);
    if (listIndex.value * 9 > allList.value.length) {
        showList.value = allList.value.slice((listIndex.value - 1) * 9);
    } else {
        showList.value = allList.value.slice(
            (listIndex.value - 1) * 9,
            listIndex.value * 9,
        );
    }
    searchVideo();
};
const wsFlvData = ref([]); //视频流数组
const searchVideo = () => {
    console.log("11111111111111");
    showList.value.forEach((item, index) => {
        getFlv({
            equipmentIdList: [item.equipmentId],
        }).then((res3) => {
            console.log("tesssssssssssssst", res3.data[0].wsFlvAddress);
            wsFlvData.value.push(res3.data[0].wsFlvAddress);
            if (index == showList.value.length - 1) {
                console.log(wsFlvData.value);
                creatDom();
            }
        });
    });
};
const clickLeft = () => {
    if (listIndex.value > 1) {
        listIndex.value = listIndex.value - 1;
    } else {
        listIndex.value = canUseIndex.value;
    }
    changeShow();
};
const clickRight = () => {
    if (listIndex.value < canUseIndex.value) {
        listIndex.value = listIndex.value + 1;
    } else {
        listIndex.value = 1;
    }
    changeShow();
};
const closeDialog = () => {
    emit("closeDialog");
};
</script>
<style lang="less" scoped>

.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 1444px;
    height: 860px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 1444px;
    height: 860px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 1412px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 1404px;
        height: 764px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.container {
    // width: 1394px;
    // height: 760px;
    // padding: 24px;
    display: flex;
    gap: 24px;
    .video-wall {
        width: 1284px;
        height: 744px;
        // display: flex;
        // flex-direction: column;
        // gap:24px;

        .one {
            width: 1284px;
            height: 232px;
            display: flex;
            gap: 24px;
            .container-inner {
                width: 412px;
                height: 232px;
            }
        }
        .two {
            margin-top: 24px;
            width: 1284px;
            height: 232px;
            display: flex;
            gap: 24px;
            .container-inner {
                width: 412px;
                height: 232px;
            }
        }
        .three {
            margin-top: 24px;
            width: 1284px;
            height: 232px;
            display: flex;
            gap: 24px;
            .container-inner {
                width: 412px;
                height: 232px;
            }
        }
    }
    .ctl {
        width: 32px;
        height: 32px;
    }
    .left-ctl {
        width: 32px;
        height: 32px;
        background: url("../../../assets/images/change/left-arrow.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: auto 0;
    }
    .right-ctl {
        width: 32px;
        height: 32px;
        background: url("../../../assets/images/change/right-arrow.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: auto 0;
    }
}
</style>
