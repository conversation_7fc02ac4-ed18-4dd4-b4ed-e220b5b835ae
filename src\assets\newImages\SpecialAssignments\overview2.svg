<svg width="200" height="75" viewBox="0 0 200 75" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="8.5" y="9.5" width="55" height="55" rx="27.5" stroke="url(#paint0_linear_53_55)"/>
<circle cx="36" cy="37" r="21.5" fill="url(#paint1_linear_53_55)" stroke="url(#paint2_linear_53_55)"/>
<g clip-path="url(#clip0_53_55)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.5 30V36C31.5 36.5523 31.9477 37 32.5 37H39.5C40.0523 37 40.5 36.5523 40.5 36V30C40.5 29.4477 40.0523 29 39.5 29H32.5C31.9477 29 31.5 29.4477 31.5 30ZM41.5 37V32.5H44C44.5523 32.5 45 32.9477 45 33.5V44C45 44.5523 44.5523 45 44 45H28C27.4477 45 27 44.5523 27 44V33.5C27 32.9477 27.4477 32.5 28 32.5H30.5V37H30C29.7239 37 29.5 37.2239 29.5 37.5C29.5 37.7761 29.7239 38 30 38H42C42.2761 38 42.5 37.7761 42.5 37.5C42.5 37.2239 42.2761 37 42 37H41.5Z" fill="white"/>
</g>
<path d="M64 31L71.5777 46.1554C74.288 51.576 79.8282 55 85.8885 55H192" stroke="url(#paint3_linear_53_55)"/>
<rect x="184" y="54" width="16" height="2" fill="#80EAFF"/>
<defs>
<linearGradient id="paint0_linear_53_55" x1="36" y1="9" x2="36" y2="65" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AC6FF"/>
<stop offset="1" stop-color="#1AC6FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_53_55" x1="36" y1="59" x2="36" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="#80EAFF" stop-opacity="0"/>
<stop offset="1" stop-color="#80EAFF" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint2_linear_53_55" x1="58" y1="37" x2="14" y2="37" gradientUnits="userSpaceOnUse">
<stop stop-color="#9CECFC"/>
<stop offset="0.5" stop-color="#1AA0FF" stop-opacity="0.45"/>
<stop offset="1" stop-color="#9CECFC"/>
</linearGradient>
<linearGradient id="paint3_linear_53_55" x1="125.86" y1="31" x2="125.86" y2="55" gradientUnits="userSpaceOnUse">
<stop stop-color="#6699CC" stop-opacity="0"/>
<stop offset="1" stop-color="#6699CC" stop-opacity="0.45"/>
</linearGradient>
<clipPath id="clip0_53_55">
<rect width="24" height="24" fill="white" transform="translate(24 25)"/>
</clipPath>
</defs>
</svg>
