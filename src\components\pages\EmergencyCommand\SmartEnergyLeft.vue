<template>
    <div class="smart-energy-left">
        <!-- <wrap-slot title1="事件概览" title2="事件报告" title3="应急预案">
            <template #content1>
            </template>
            <template #content2>
            </template>
            <template #content3>
            </template>
        </wrap-slot> -->
        <left-top></left-top>
        <left-center></left-center>
        <left-bottom></left-bottom>


    </div>
</template>

<script setup>
import WrapSlot from "../../commenNew/WrapSlot.vue";
import LeftTop from "./subgroup/LeftTop.vue";
import LeftCenter from "./subgroup/LeftCenter.vue";
import LeftBottom from "./subgroup/LeftBottom.vue";
</script>

<style lang="less" scoped>
.smart-energy-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
