<template>
    <Card title="重大危险源">
        <template v-slot>
            <div class="key-processes-wrapper">
            <div class="key-processes">
                <div class="pointwrap" ref="pointwrap"></div>
            </div>
        </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";

import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { danger_chemical_processh } from "../../../../assets/js/api/safetySupervision";

const num = reactive({
    total: 0,
    normal: 0,
    abnormal: 0,
});
let data = ref([]);
let option;
let pointwrap = ref(null);
let pointChart;
let echartData = ref([]);
const initChart = () => {
    // 小气泡

    //字符串截取
    var wordLength = (value) => {
        console.log(value.length);
        if (value.length > 5) {
            value = value.substring(0, 5);
        }
        var ret = ""; //拼接加\n返回的类目项
        var maxLength = 3; //每项显示文字个数
        var valLength = value.length; //X轴类目项的文字个数
        var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
        if (rowN > 1) {
            //如果类目项的文字大于3,
            for (var i = 0; i < rowN; i++) {
                var temp = ""; //每次截取的字符串
                var start = i * maxLength; //开始截取的位置
                var end = start + maxLength; //结束截取的位置
                //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                temp = value.substring(start, end) + "\n";
                ret += temp; //凭借最终的字符串
            }
            return ret;
        } else {
            return value;
        }
    };
    //

    //偏移量
    var offsetData = [
        [45, 15],
        [20, 40],
        [70, 40],
        [10, 10],
        [80, 10],
        [45, 65],
        [80, 70],
    ];
    //symbolSize 散点气泡大小
    var symbolSizeData = [60, 45, 45, 45, 57, 49, 49, 49, 49, 49, 49];
    //
    //循环定义series的data值
    var datas = [];
    for (var i = 0; i < data.length; i++) {
        var item = data[i];
        //var itemToStyle = datalist[i];

        datas.push({
            name: wordLength(item.craft_name),
            value: offsetData[i],
            realName: item.craft_name,
            symbolSize: symbolSizeData[i],
            label: {
                normal: {
                    textStyle: {
                        fontSize: 12,
                        lineHeight: 16,
                    },
                },
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                        {
                            offset: 0,
                            color: "rgba(48, 171, 232, 0)",
                        },
                        {
                            offset: 1,
                            color: "#47EBEB",
                        },
                    ]),
                    // opacity: 0.8,
                    // shadowColor: '#47EBEB',
                    // shadowBlur: 30,
                    // shadowOffsetX: 1,
                    // shadowOffsetY: 1,
                },
            },
        });
    }

    option = {
        //   backgroundColor: "transparent",
        // backgroundColor: '#0e2147',
        grid: {
            show: false,
            top: 10,
            bottom: 10,
        },
        tooltip: {
            show: true,
            trigger: "item",
            position: "right",
            formatter: function (params) {
                console.log(params);
                return params.data.realName;
            },
        },
        xAxis: [
            {
                gridIndex: 0,
                type: "value",
                show: false,
                min: 0,
                max: 100,
                nameLocation: "middle",
                nameGap: 5,
            },
        ],
        yAxis: [
            {
                gridIndex: 0,
                min: 0,
                show: false,
                max: 100,
                nameLocation: "middle",
                nameGap: 30,
            },
        ],

        series: [
            {
                type: "scatter",
                symbol: "circle",
                symbolSize: 120,
                label: {
                    normal: {
                        show: true,
                        formatter: "{b}",
                        color: "#fff",
                        textStyle: {
                            fontSize: 12,
                            lineHeight: 16,
                        },
                    },
                },
                animationDurationUpdate: 1000,
                animationEasingUpdate: 1000,
                animationDelay: function (idx) {
                    // 越往后的数据延迟越大
                    return idx * 100;
                },
                itemStyle: {
                    normal: {
                        color: "#00acea",
                        fontSize: 12,
                        lineHeight: 16,
                    },
                },
                data: datas,
            },
        ],
    };
};
onMounted(() => {
    data = [
        { craft_name: "磺化" },
        { craft_name: "氯化" },
        { craft_name: "烷基化" },
        { craft_name: "重氮化" },
    ];
    pointChart = echarts.init(pointwrap.value);
    initChart();
    pointChart.setOption(option);
    window.addEventListener("resize", () => {
        pointChart.resize();
    });
    //   danger_chemical_processh({ "": "" }).then((res) => {
    //     console.log(res.data.data);
    //     data = res.data.data;
    //     initChart();
    //     pointChart.setOption(option);
    //     window.addEventListener("resize", () => {
    //       pointChart.resize();
    //     });
    //   });
});
onBeforeUnmount(() => {
    if (pointChart) {
        pointChart.dispose();
    }
});
</script>

<style lang="less" scoped>
.key-processes-wrapper {
    // width: 400px;
    // height: 244px;
    width: 416px;
    height: 224px;
    padding: 16px;
    .key-processes{
        width: 376px;
    height: 224px;
    // margin-bottom: 20px;
        background: url("../../../../assets/images/card/key-processes.svg")
        no-repeat center center;
    background-size: cover;
    background-position: center;
    }
   
    .pointwrap {
        width: 356px;
        height: 176px;
        margin: 0 auto;
        // background-color:aqua;
    }
}
</style>
