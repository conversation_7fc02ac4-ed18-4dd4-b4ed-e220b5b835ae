<template>
    <Card title="装置开停车和大检修">
        <template v-slot:title>
            <div class="title-bar">
                <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
                <div class="title-bar-right" @click="openDialog('outageMaintenanceDialogShow')">
                    <div>查看详情</div>
                    <img class="right-arrow" src="@/assets/newImages/right-arrow.svg">
                </div>
            </div>
        </template>
        <template v-slot>
            <div class="spot-check-wrapper">
                <div class="list-header">
                    <div class="td1">事项</div>
                    <div class="td2">备案总数</div>
                    <div class="td3">进行中</div>
                    <div class="td4">已完成</div>
                    <div class="td5">完成比例</div>
                </div>   
                <div v-if="noDataFlag" class="list-content">
                    <div class="img-no">暂无数据</div>
                </div>
                <div v-else>
                    <autoScroll class="card-container" :step="2" :deltaTime="100" v-if="list&&list.length>4">   
                        <div class="list-content">
                            <div class="list-item" v-for="(item,index) in list" :key="index">
                                <div class="td1">{{ item.shut_type=='device_filing_type_1'?'开车':item.shut_type=='device_filing_type_2'?'大检修':item.shut_type=='device_filing_type_3'?'停车':'全部' }}</div>
                                <div class="td2">{{item.total}}</div>
                                <div class="td3">{{item.handle_count}}</div>
                                <div class="td4">{{item.pass_count}}</div>
                                <div class="td5">
                                    <el-progress
                        class="progress-height"
                        :stroke-width="16"
                        :percentage="Number(item.rate)"
                    ></el-progress></div>
                            </div>
                        </div>
                    </autoScroll>  
                    <div v-else class="list-content">
                        <div class="list-item" v-for="(item,index) in list" :key="index">
                            <div class="td1">{{ item.shut_type=='device_filing_type_1'?'开车':item.shut_type=='device_filing_type_2'?'大检修':'全部' }}</div>
                                <div class="td2">{{item.total}}</div>
                                <div class="td3">{{item.handle_count}}</div>
                                <div class="td4">{{item.pass_count}}</div>
                                <div class="td5">
                                    <el-progress
                        class="progress-height"
                        :stroke-width="16"
                        :percentage="Number(item.rate)"
                    ></el-progress></div>
                        </div>
                    </div>
                </div>
        </div>
        </template>
    </Card>
</template>

<script setup>
import Card from "@/components/commenNew/Card.vue";
import selectVue from "@/components/commenNew/selectVue.vue";
import autoScroll from "@/components/commenNew/autoScroll.vue";
import { outage_maintenance } from "@/assets/js/api/secureBase.js";
import {
    ref,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits
} from "vue";
const { proxy } = getCurrentInstance();
import { type_trend } from "@/assets/js/api/specialAssignments.js";
const emit = defineEmits(["openDialog"]);

    const openDialog = (val) => {
        console.log(val, '打开弹窗');
        emit('openDialog',val,nowChoose.value);
    }
//按钮选项
const btnOptions = ref([
        {
            label:'今日',
            value:'today'
        },
        {
            label:'本周',
            value:'this_week'
        },
        {
            label:'本月',
            value:'this_month'
        },
        {
            label:'本年',
            value:'this_year'
        }
    ])
    //当前选择
let nowChoose = ref('this_year');
//按钮
let chooseValue = ref('本年');
//暂无数据展示
let noDataFlag = ref(false);

const selectBtn = (val) => {
    console.log(val,'vallllllllll');
    nowChoose.value=val.value;
    getData(val.value);
};
//列表
const list = ref([{
    name:'大检修',
}]);

const getData = (val) => {
    list.value = [];
    noDataFlag.value = true;
    outage_maintenance({
        type:val
    }).then(res => {
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            list.value = res.data.data;
            noDataFlag.value = false;
        }
    })
    
};
onMounted(() => {
    getData('this_year');
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>
.title-bar{
    display: flex;
    gap: 16px;

    .title-bar-right{
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: center;
color: #80EAFF;

        display: flex;
        gap: 4px;
        .right-arrow{
            width: 16px;
            height: 16px;
            margin: auto;
        }
    }
}

.spot-check-wrapper{
    width: 416px;
    height: 224px;
    padding: 16px;
    // background-color: #1A9FFF;
    
   .list-header{
    width: 416px;
height: 40px;
background: linear-gradient(90deg, 
        rgba(26, 159, 255, 0) 0%, 
        #1A9FFF4D, 
        rgba(26, 159, 255, 0) 100%
    );
    display: flex;
    align-items: center;

    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 40px;
color: #FFFFFF;
   }
   .card-container{
    width: 416px;
    height: 184px;
    
   }
   .img-no {
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 184px; /* 150% */
        }
   .list-content{
    width: 416px;
    height: 184px;
    
    
    .list-item{
        width: 416px;
height: 40.8px;
display: flex;
align-items: center;
border-bottom: 1px solid #1A9FFF73;
font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: #FFFFFF;

    }
   }
   .td1{
    width: 54px;
    padding-left: 12px;

text-align: center;
   }
   .td2{
    width: 68px;
    padding-left: 12px;
text-align: left;
   }
   .td3{
    width: 54px;
    text-align: left;
    padding-left: 12px;
   }
   .td4{
    width: 54px;
    text-align: left;
    padding-left: 12px;
   }
   .td5{
    width: 126px;
    text-align: left;
    padding-left: 12px;
   }
   .td-index{
    width: 24px;
height: 24px;
background: url("@/assets/newImages/td-index.svg") no-repeat;
            background-size: cover;
            background-position: center;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
margin:auto;
   }
   :deep(.el-progress-bar__inner) {
            background: rgba(26, 159, 255, 1);
           
            border-radius: 0%;
        }
        :deep(.el-progress) {
            height: 8px;
        }
        :deep(.el-progress-bar__outer) {
            width: 77px;
            height: 8px !important;
            background-color: rgba(26, 159, 255, 0.15);
            // border: 1px solid #4C94FF;
            border-radius: 0%;
        }
        :deep(.el-progress__text) {
            // display: none;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px !important;
line-height: 20px;
letter-spacing: 0%;
color: #FFFFFF;
        }
        .progress-height {
            height: 8px;
        }
}
</style>
